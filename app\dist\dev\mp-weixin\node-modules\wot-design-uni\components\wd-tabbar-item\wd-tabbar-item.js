"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  (wdIcon + wdBadge)();
}
const wdBadge = () => "../wd-badge/wd-badge.js";
const wdIcon = () => "../wd-icon/wd-icon.js";
const __default__ = {
  name: "wd-tabbar-item",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.tabbarItemProps,
  setup(__props) {
    const props = __props;
    const { parent: tabbar, index } = common_vendor.useParent(common_vendor.TABBAR_KEY);
    const customBadgeProps = common_vendor.computed(() => {
      const badgeProps = common_vendor.deepAssign(
        common_vendor.isDef(props.badgeProps) ? common_vendor.omitBy(props.badgeProps, common_vendor.isUndefined) : {},
        common_vendor.omitBy(
          {
            max: props.max,
            isDot: props.isDot,
            modelValue: props.value
          },
          common_vendor.isUndefined
        )
      );
      if (!common_vendor.isDef(badgeProps.max)) {
        badgeProps.max = 99;
      }
      return badgeProps;
    });
    const textStyle = common_vendor.computed(() => {
      const style = {};
      if (tabbar) {
        if (active.value && tabbar.props.activeColor) {
          style["color"] = tabbar.props.activeColor;
        }
        if (!active.value && tabbar.props.inactiveColor) {
          style["color"] = tabbar.props.inactiveColor;
        }
      }
      return `${common_vendor.objToStyle(style)}`;
    });
    const active = common_vendor.computed(() => {
      const name = common_vendor.isDef(props.name) ? props.name : index.value;
      if (tabbar) {
        if (tabbar.props.modelValue === name) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    });
    function handleClick() {
      const name = common_vendor.isDef(props.name) ? props.name : index.value;
      tabbar && tabbar.setChange({ name });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.r("icon", {
          active: active.value
        }),
        b: !_ctx.$slots.icon && _ctx.icon
      }, !_ctx.$slots.icon && _ctx.icon ? {
        c: common_vendor.p({
          name: _ctx.icon,
          ["custom-style"]: textStyle.value,
          ["custom-class"]: `wd-tabbar-item__body-icon ${active.value ? "is-active" : "is-inactive"}`
        })
      } : {}, {
        d: _ctx.title
      }, _ctx.title ? {
        e: common_vendor.t(_ctx.title),
        f: common_vendor.s(textStyle.value),
        g: common_vendor.n(`wd-tabbar-item__body-title ${active.value ? "is-active" : "is-inactive"}`)
      } : {}, {
        h: common_vendor.p(__spreadValues({}, customBadgeProps.value)),
        i: common_vendor.n(`wd-tabbar-item ${_ctx.customClass}`),
        j: common_vendor.s(_ctx.customStyle),
        k: common_vendor.o(handleClick)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9d3575ab"]]);
wx.createComponent(Component);
