<view class="quotation-card data-v-c7b08e0d" bindtap="{{B}}"><view class="card-type-tag data-v-c7b08e0d"><wd-tag wx:if="{{b}}" class="data-v-c7b08e0d" u-s="{{['d']}}" u-i="c7b08e0d-0" bind:__l="__l" u-p="{{b}}">{{a}}</wd-tag></view><view class="card-body data-v-c7b08e0d"><view class="left-content data-v-c7b08e0d"><text class="quotation-title data-v-c7b08e0d">{{c}}</text><view class="publisher-info data-v-c7b08e0d"><text class="publisher-name clickable data-v-c7b08e0d" bindtap="{{e}}">{{d}}</text><text class="{{['remaining-time', 'data-v-c7b08e0d', g && 'expired']}}">{{f}}</text></view><view class="tag-info data-v-c7b08e0d"><wd-tag wx:if="{{h}}" class="data-v-c7b08e0d" u-s="{{['d']}}" u-i="c7b08e0d-1" bind:__l="__l" u-p="{{j}}">{{i}}</wd-tag><wd-tag wx:if="{{k}}" class="data-v-c7b08e0d" u-s="{{['d']}}" u-i="c7b08e0d-2" bind:__l="__l" u-p="{{m}}">{{l}}</wd-tag><wd-tag wx:if="{{n}}" class="data-v-c7b08e0d" u-s="{{['d']}}" u-i="c7b08e0d-3" bind:__l="__l" u-p="{{p}}">{{o}}</wd-tag></view></view><view class="right-content data-v-c7b08e0d"><view class="price-display data-v-c7b08e0d"><block wx:if="{{q}}"><text class="price-value adaptive-price data-v-c7b08e0d" style="{{'font-size:' + s}}" title="{{t}}">{{r}}</text></block><block wx:else><view class="basis-display data-v-c7b08e0d"><text class="contract-name data-v-c7b08e0d">{{v}}</text><text class="basis-value adaptive-price data-v-c7b08e0d" style="{{'font-size:' + x}}" data-positive="{{y}}" data-negative="{{z}}" title="{{A}}">{{w}}</text></view></block></view></view></view></view>