"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "list",
  setup(__props) {
    const router = common_vendor.useRouter();
    function goToSetterList() {
      router.push("/pages/contract/setter-list");
    }
    function goToPricerList() {
      router.push("/pages/contract/pricer-list");
    }
    function goToCreate() {
      router.push("/pages/contract/form");
    }
    function goToSetterManagement() {
      router.push("/pages/trade/setter-management");
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(goToSetterList),
        b: common_vendor.o(goToPricerList),
        c: common_vendor.o(goToCreate),
        d: common_vendor.o(goToSetterList),
        e: common_vendor.o(goToPricerList),
        f: common_vendor.o(goToSetterManagement)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ec30f093"]]);
wx.createPage(MiniProgramPage);
