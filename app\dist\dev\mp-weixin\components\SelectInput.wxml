<view class="{{['select-input-wrapper', 'data-v-01d49153', h && 'dropdown-active']}}" style="{{'--label-width:' + i}}"><wd-input wx:if="{{e}}" class="data-v-01d49153" bindupdateModelValue="{{a}}" bindfocus="{{b}}" bindblur="{{c}}" bindclear="{{d}}" u-i="01d49153-0" bind:__l="__l" u-p="{{e}}"/><view wx:if="{{f}}" class="dropdown-container data-v-01d49153"><view class="dropdown-content data-v-01d49153"><view wx:for="{{g}}" wx:for-item="option" wx:key="d" class="dropdown-item data-v-01d49153" bindtap="{{option.e}}"><text class="option-label data-v-01d49153">{{option.a}}</text><text wx:if="{{option.b}}" class="option-description data-v-01d49153">{{option.c}}</text></view></view></view></view>