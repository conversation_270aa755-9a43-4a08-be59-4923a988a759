"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_layout_tabbar_uni = common_vendor.resolveComponent("layout-tabbar-uni");
  _component_layout_tabbar_uni();
}
if (!Math) {
  RequestComp();
}
const RequestComp = () => "./components/request.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "about",
  setup(__props) {
    console.log("about");
    function gotoAlova() {
      common_vendor.index.navigateTo({
        url: "/pages/about/alova"
      });
    }
    function gotoVueQuery() {
      common_vendor.index.navigateTo({
        url: "/pages/about/vue-query"
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(gotoAlova),
        b: common_vendor.o(gotoVueQuery)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b5177f87"]]);
wx.createPage(MiniProgramPage);
