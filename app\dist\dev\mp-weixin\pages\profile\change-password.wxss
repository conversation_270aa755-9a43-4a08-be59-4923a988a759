/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.change-password-container.data-v-667d2844 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40rpx 32rpx;
}
.page-header.data-v-667d2844 {
  text-align: center;
  margin-bottom: 60rpx;
}
.page-header .header-icon.data-v-667d2844 {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}
.page-header .header-title.data-v-667d2844 {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.page-header .header-subtitle.data-v-667d2844 {
  font-size: 26rpx;
  color: #909399;
  line-height: 1.5;
}
.password-form.data-v-667d2844 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.form-item.data-v-667d2844 {
  margin-bottom: 24rpx;
}
.form-item.data-v-667d2844:last-child {
  margin-bottom: 0;
}
.data-v-667d2844 .password-input {
  border: 2rpx solid #e4e7ed !important;
  border-radius: 12rpx !important;
  height: 88rpx !important;
  font-size: 28rpx !important;
  padding: 0 24rpx !important;
}
.data-v-667d2844 .password-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1) !important;
}
.password-tips.data-v-667d2844 {
  margin-top: 12rpx;
}
.password-tips text.data-v-667d2844 {
  font-size: 24rpx;
  color: #909399;
  line-height: 1.4;
}
.error-tips.data-v-667d2844 {
  margin-top: 12rpx;
}
.error-tips text.data-v-667d2844 {
  font-size: 24rpx;
  color: #f56c6c;
  line-height: 1.4;
}
.submit-section.data-v-667d2844 {
  margin-bottom: 60rpx;
}
.data-v-667d2844 .submit-btn {
  width: 100% !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 44rpx !important;
  height: 88rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
}
.data-v-667d2844 .submit-btn[disabled] {
  background: #c0c4cc !important;
  opacity: 0.6 !important;
}
.security-tips.data-v-667d2844 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.tips-header.data-v-667d2844 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.tips-header text.data-v-667d2844 {
  margin-left: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.tips-content text.data-v-667d2844 {
  display: block;
  font-size: 26rpx;
  color: #606266;
  line-height: 2;
  margin-bottom: 8rpx;
}
.tips-content text.data-v-667d2844:last-child {
  margin-bottom: 0;
}