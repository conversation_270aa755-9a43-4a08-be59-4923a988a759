<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "用户主页"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { getUserPublicProfile } from '@/api/user'
import { getPublicQuotationList } from '@/api/quotation'
import QuotationCard from '@/components/marketplace/QuotationCard.vue'
import QuotationListPoster from '@/components/marketplace/QuotationListPoster.vue'
import { useUserStore } from '@/store/user'
import type {
  IUserPublicProfile,
  IQuotationResponse,
  IQuotationListRequest
} from '@/types'

defineOptions({
  name: 'UserHomePage'
})

// ==================== 响应式数据声明 ====================

// 页面参数
const userId = ref<number>()

// 页面状态管理
const isLoading = ref<boolean>(false)
const isRefreshing = ref<boolean>(false)
const hasMore = ref<boolean>(true)

// 数据存储
const userProfile = ref<IUserPublicProfile>()
const quotationList = ref<IQuotationResponse[]>([])
const currentPage = ref<number>(1)
const pageSize = ref<number>(10)
const total = ref<number>(0)

// 用户状态管理
const userStore = useUserStore()

// 海报生成相关
const showPoster = ref<boolean>(false)

// ==================== 计算属性 ====================

const pageTitle = computed(() => {
  if (userProfile.value?.companyName) {
    return userProfile.value.companyName
  }
  return userProfile.value?.nickName || '用户主页'
})

const isValidUser = computed(() => {
  return userProfile.value && userProfile.value.enable === 1
})

// 判断是否为用户自己的主页
const isOwner = computed(() => {
  return userStore.isLoggedIn && 
         userStore.userInfo.ID === userId.value
})

// ==================== 数据获取相关函数 ====================

/**
 * 加载用户公开档案
 */
async function loadUserProfile(): Promise<void> {
  if (!userId.value) return
  
  try {
    const res = await getUserPublicProfile(userId.value)
    userProfile.value = res.data
    
    // 更新页面标题
    uni.setNavigationBarTitle({
      title: pageTitle.value
    })
  } catch (error) {
    console.error('加载用户档案失败:', error)
    uni.showToast({
      title: '用户不存在',
      icon: 'error'
    })
    
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
}

/**
 * 加载用户报价列表
 * @param refresh 是否刷新（重置分页）
 */
async function loadQuotationList(refresh: boolean = false): Promise<void> {
  if (!userId.value) return
  
  if (refresh) {
    currentPage.value = 1
    quotationList.value = []
    isRefreshing.value = true
  } else {
    isLoading.value = true
  }

  try {
    const params: IQuotationListRequest = {
      page: currentPage.value,
      pageSize: pageSize.value,
      userID: userId.value
    }

    const res = await getPublicQuotationList(params)
    const { list, total: totalCount } = res.data

    if (refresh) {
      quotationList.value = list
    } else {
      quotationList.value.push(...list)
    }

    total.value = totalCount
    hasMore.value = quotationList.value.length < totalCount

  } catch (error) {
    console.error('加载报价列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

/**
 * 加载更多数据
 */
async function loadMore(): Promise<void> {
  if (!hasMore.value || isLoading.value) return

  currentPage.value++
  await loadQuotationList()
}

/**
 * 下拉刷新
 */
async function onRefresh(): Promise<void> {
  await loadQuotationList(true)
}

// ==================== 交互处理函数 ====================

/**
 * 查看报价详情
 * @param quotation 报价对象
 */
function viewQuotationDetail(quotation: IQuotationResponse): void {
  uni.navigateTo({
    url: `/pages/quotes/detail?id=${quotation.id}&from=public`
  })
}

/**
 * 处理发布者点击（在用户主页中，这个应该无效或回到自己）
 */
function handlePublisherClick(): void {
  // 在用户主页中点击发布者名称不做任何操作，因为已经在用户主页了
}

/**
 * 跳转到管理页面
 */
function handleManageClick(): void {
  uni.navigateTo({
    url: '/pages/quotes/my-list'
  })
}

/**
 * 生成分享海报
 */
function handleGeneratePoster(): void {
  if (!userProfile.value || quotationList.value.length === 0) {
    uni.showToast({
      title: '数据加载中，请稍后',
      icon: 'none'
    })
    return
  }
  
  showPoster.value = true
}

/**
 * 海报生成成功回调
 */
function handlePosterSuccess(imagePath: string): void {
  console.log('海报生成成功:', imagePath)
  // 图片预览已在组件内部处理
}

/**
 * 海报生成失败回调
 */
function handlePosterFail(error: any): void {
  console.error('海报生成失败:', error)
  // 错误提示已在组件内部处理
}

// ==================== 微信小程序分享功能 ====================

// 微信小程序分享
onShareAppMessage(() => {
  if (!userProfile.value || !userId.value) return {}
  
  const displayName = userProfile.value.companyName || userProfile.value.nickName || '用户'
  const title = `快来看看 ${displayName} 的最新报价`
  const path = `/pages/quotes/public-list?id=${userId.value}`

  return {
    title,
    path
  }
})

// 微信小程序朋友圈分享
onShareTimeline(() => {
  if (!userProfile.value || !userId.value) return {}
  
  const displayName = userProfile.value.companyName || userProfile.value.nickName || '用户'
  const title = `快来看看 ${displayName} 的最新报价`
  const query = `share=timeline&id=${userId.value}`

  return {
    title,
    query
  }
})

// ==================== 页面生命周期函数 ====================

onLoad((options) => {
  // 如果有 share=timeline 参数，说明是从朋友圈分享链接进入的
  if (options.share === 'timeline' && options.id) {
    // 已经在正确的页面，继续加载
    userId.value = parseInt(options.id)
    return
  }
  if (options?.id) {
    userId.value = parseInt(options.id)
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'error'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
    return
  }
})

onMounted(async () => {
  if (userId.value) {
    await loadUserProfile()
    await loadQuotationList()
  }
})
</script>

<template>
  <view class="page-container gradient-bg-primary">
    <!-- 用户档案头部 -->
    <view v-if="userProfile" class="user-profile-header">
      <view class="profile-card">
        <!-- 用户头像 -->
        <view class="avatar-section">
          <wd-img
            :src="userProfile.headerImg"
            width="120rpx"
            height="120rpx"
            mode="aspectFill"
            round
            custom-class="user-avatar"
          />
        </view>
        
        <!-- 用户信息 -->
        <view class="user-info">
          <text class="company-name">{{ userProfile.companyName || userProfile.nickName }}</text>
          <text v-if="userProfile.companyName" class="nick-name">{{ userProfile.nickName }}</text>
          <text v-if="userProfile.companyAddress" class="company-address">
            <wd-icon name="location" size="24rpx" />
            {{ userProfile.companyAddress }}
          </text>
        </view>
        
        <!-- 状态指示 -->
        <view class="status-indicator">
          <wd-tag 
            :type="userProfile.enable === 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ userProfile.enable === 1 ? '正常' : '冻结' }}
          </wd-tag>
        </view>
        
        <!-- 管理按钮（仅当访问自己主页时显示） -->
        <view v-if="isOwner" class="manage-button">
          <wd-button 
            type="primary" 
            size="small"
            @click.stop="handleManageClick"
            custom-class="manage-btn"
          >
            <wd-icon name="edit" size="24rpx" />
            管理
          </wd-button>
        </view>
      </view>
    </view>

    <!-- 报价列表标题 -->
    <view class="section-header">
      <view class="section-title-row">
        <text class="section-title">发布的报价</text>
        <text class="section-subtitle">共 {{ total }} 条有效报价</text>
      </view>
    </view>

    <!-- 报价列表内容 -->
    <scroll-view 
      class="scroll-container"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view class="list-container">
        <!-- 报价列表 -->
        <view v-if="quotationList.length > 0" class="quotation-list">
          <QuotationCard
            v-for="quotation in quotationList"
            :key="quotation.id"
            :quotation="quotation"
            @click="viewQuotationDetail"
            @publisherClick="handlePublisherClick"
          />
        </view>
        
        <!-- 空状态 -->
        <view v-else-if="!isLoading" class="empty-state">
          <text class="empty-text">该用户暂未发布任何报价</text>
        </view>

        <!-- 加载更多 -->
        <view v-if="isLoading && quotationList.length > 0" class="loading-more">
          <wd-loading size="24rpx" custom-class="loading-spinner" />
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 没有更多 -->
        <view v-if="!hasMore && quotationList.length > 0" class="no-more">
          <view class="divider"></view>
          <text class="no-more-text">没有更多报价了</text>
          <view class="divider"></view>
        </view>
        
        <!-- 生成海报按钮 -->
        <view v-if="quotationList.length > 0" class="poster-button-container">
          <wd-button 
            type="primary"
            block
            @click="handleGeneratePoster"
            custom-class="poster-btn"
          >
            <wd-icon name="share" size="32rpx" />
            生成分享海报
          </wd-button>
        </view>
      </view>
    </scroll-view>
    
    <!-- 海报生成组件 -->
    <QuotationListPoster
      v-model:visible="showPoster"
      :user-profile="userProfile || {}"
      :quotation-list="quotationList"
      @success="handlePosterSuccess"
      @fail="handlePosterFail"
    />
  </view>
</template>

<style lang="scss" scoped>
// 设计系统变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$primary-dark: #764ba2;
$bg-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
$bg-card: rgba(255, 255, 255, 0.95);
$radius-sm: 8rpx;
$radius-md: 12rpx;
$radius-lg: 20rpx;
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
$spacing-sm: 20rpx;
$spacing-md: 36rpx;
$spacing-lg: 48rpx;

.page-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  box-sizing: border-box;
}

// 用户档案头部
.user-profile-header {
  padding: 0 0 $spacing-sm;
  margin-bottom: $spacing-sm;
  
  .profile-card {
    background: $bg-card;
    backdrop-filter: blur(10rpx);
    border-radius: $radius-lg;
    padding: $spacing-md;
    box-shadow: $shadow-md;
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    animation: slideInDown 0.3s ease-out;
    
    .avatar-section {
      flex-shrink: 0;
      
      :deep(.user-avatar) {
        border: 4rpx solid rgba(102, 126, 234, 0.1);
        box-shadow: $shadow-sm;
      }
    }
    
    .user-info {
      flex: 1;
      min-width: 0;
      
      .company-name {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .nick-name {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .company-address {
        display: flex;
        align-items: center;
        gap: 8rpx;
        font-size: 24rpx;
        color: #909399;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    
    .status-indicator {
      flex-shrink: 0;
    }
    
    .manage-button {
      flex-shrink: 0;
      margin-left: 16rpx;
      
      :deep(.manage-btn) {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border: none !important;
        border-radius: 20rpx !important;
        padding: 8rpx 16rpx !important;
        font-size: 24rpx !important;
        display: flex !important;
        align-items: center !important;
        gap: 6rpx !important;
        
        &:hover {
          opacity: 0.9;
        }
        
        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}

// 区块标题
.section-header {
  padding: 0 $spacing-sm $spacing-sm;
  
  .section-title-row {
    display: flex;
    align-items: baseline;
    gap: 12rpx;
  }
  
  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
  }
  
  .section-subtitle {
    font-size: 24rpx;
    color: #666;
  }
}

// 滚动容器
.scroll-container {
  flex: 1;
  padding: 0 $spacing-sm;
  box-sizing: border-box;
}

// 报价列表
.quotation-list {
  width: 100%;
  box-sizing: border-box;

  :deep(.quotation-card) {
    background: $bg-card !important;
    backdrop-filter: blur(10rpx) !important;
    border-radius: $radius-lg !important;
    box-shadow: $shadow-md !important;
    transition: all 0.3s ease !important;

    &:hover {
      transform: translateY(-4rpx) !important;
      box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15) !important;
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: $bg-card;
  backdrop-filter: blur(10rpx);
  border-radius: $radius-lg;
  box-shadow: $shadow-md;
  margin: $spacing-md 0;
  animation: fadeIn 0.5s ease-out;

  :deep(.empty-image) {
    opacity: 0.6;
    filter: grayscale(20%);
  }

  .empty-text {
    font-size: 28rpx;
    color: #606266;
    margin-top: 24rpx;
    text-align: center;
    font-weight: 500;
  }
}

// 加载更多
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: $bg-card;
  backdrop-filter: blur(10rpx);
  border-radius: $radius-lg;
  margin: $spacing-sm 0;
  box-shadow: $shadow-sm;

  :deep(.loading-spinner) {
    color: $primary-color !important;
  }

  .loading-text {
    margin-left: 16rpx;
    font-size: 26rpx;
    color: $primary-color;
    font-weight: 500;
  }
}

// 没有更多
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  margin: $spacing-md 0;
  
  .divider {
    flex: 1;
    height: 2rpx;
    background-color: #d0d4d9;
    min-width: 20rpx;
  }
  
  .no-more-text {
    padding: 0 24rpx;
    font-size: 24rpx;
    color: #c0c4cc;
    white-space: nowrap;
  }
}

// 海报按钮容器
.poster-button-container {
  padding: 40rpx 0 20rpx;
  margin-top: 20rpx;
  
  :deep(.poster-btn) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 50rpx !important;
    padding: 24rpx 0 !important;
    font-size: 32rpx !important;
    font-weight: 600 !important;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 12rpx !important;
    transition: all 0.3s ease !important;
    
    &:hover {
      transform: translateY(-2rpx) !important;
      box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4) !important;
    }
    
    &:active {
      transform: scale(0.98) !important;
    }
  }
}

// 动画定义
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .user-profile-header {
    padding: 16rpx;
    
    .profile-card {
      padding: 24rpx;
    }
  }
  
  .scroll-container {
    padding: 0 16rpx;
  }
  
  .empty-state {
    margin: 16rpx 0;
    padding: 80rpx 20rpx;
  }
}
</style>
