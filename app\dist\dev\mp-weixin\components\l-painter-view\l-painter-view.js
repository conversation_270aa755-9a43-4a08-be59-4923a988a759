"use strict";
const components_common_relation = require("../common/relation.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "lime-painter-view",
  mixins: [components_common_relation.children("painter"), components_common_relation.parent("painter")],
  props: {
    id: String,
    type: {
      type: String,
      default: "view"
    },
    css: [String, Object]
  },
  data() {
    return {
      // type: 'view',
      el: {
        css: {},
        views: []
      }
    };
  },
  mounted() {
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {};
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
