/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}
image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}
/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 提供统一的页面容器样式，供各个页面复用
 */
.gradient-bg-primary {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}
.page-container {
  min-height: 100vh;
}
.common-card {
  margin: 0 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.test {
  margin-top:32rpx;margin-left:32rpx;
  padding-top: 4px;
  color: red;
}
 page,::before,::after{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / 0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}::backdrop{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / 0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}.i-carbon-code{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='inline-block' vertical-align='middle' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='m31 16l-7 7l-1.41-1.41L28.17 16l-5.58-5.59L24 9zM1 16l7-7l1.41 1.41L3.83 16l5.58 5.59L8 23zm11.42 9.484L17.64 6l1.932.517L14.352 26z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:inline-block;vertical-align:middle;width:1.2em;height:1.2em;}.i-carbon-logo-wechat{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='inline-block' vertical-align='middle' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' fill-rule='evenodd' d='M27.086 24.78A6.62 6.62 0 0 0 30 19.465c0-3.88-3.776-7.027-8.434-7.027s-8.434 3.147-8.434 7.027s3.777 7.028 8.434 7.028a10 10 0 0 0 2.754-.385l.247-.037a.9.9 0 0 1 .448.13l1.847 1.066l.162.053a.28.28 0 0 0 .281-.282l-.045-.205l-.38-1.417l-.03-.18a.56.56 0 0 1 .236-.458M12.12 4.68C6.53 4.68 2 8.455 2 13.114a7.94 7.94 0 0 0 3.497 6.374a.67.67 0 0 1 .283.55l-.035.215l-.456 1.701l-.055.246a.34.34 0 0 0 .337.338l.196-.063l2.216-1.28a1.06 1.06 0 0 1 .536-.155l.298.044a12 12 0 0 0 3.304.464l.555-.014a6.5 6.5 0 0 1-.34-2.067c0-4.247 4.133-7.691 9.23-7.691l.55.014c-.762-4.029-4.947-7.11-9.995-7.11m6.633 13.663a1.125 1.125 0 1 1 1.125-1.125a1.124 1.124 0 0 1-1.125 1.125m5.624 0a1.125 1.125 0 1 1 1.123-1.125a1.125 1.125 0 0 1-1.123 1.125m-15.631-6.58a1.35 1.35 0 1 1 1.35-1.348a1.35 1.35 0 0 1-1.35 1.349m6.747 0a1.35 1.35 0 1 1 1.35-1.348a1.35 1.35 0 0 1-1.35 1.349'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:inline-block;vertical-align:middle;width:1.2em;height:1.2em;}.center{display:flex;align-items:center;justify-content:center;}[display_a_none=""]{display:none="";}[opacity_a_1=""]{opacity:1="";}[update_a_modelValue=""]{update:modelValue="";}.visible{visibility:visible;}.absolute{position:absolute;}.fixed{position:fixed;}.relative{position:relative;}.sticky{position:sticky;}.static{position:static;}.grid{display:grid;}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr));}.grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr));}.m-8{margin:64rpx;}.m-auto{margin:auto;}.mx-auto{margin-left:auto;margin-right:auto;}.my-2{margin-top:16rpx;margin-bottom:16rpx;}.my-6{margin-top:48rpx;margin-bottom:48rpx;}.mb-1{margin-bottom:8rpx;}.mb-2{margin-bottom:16rpx;}.mb-3{margin-bottom:24rpx;}.mb-4{margin-bottom:32rpx;}.mb-6{margin-bottom:48rpx;}.mb-8{margin-bottom:64rpx;}.ml-1{margin-left:8rpx;}.ml-2{margin-left:16rpx;}.mr-2{margin-right:16rpx;}.mr-3{margin-right:24rpx;}.ms{margin-inline-start:32rpx;}.mt-1{margin-top:8rpx;}.mt-10{margin-top:80rpx;}.mt-2{margin-top:16rpx;}.mt-3{margin-top:24rpx;}.mt-4{margin-top:32rpx;}.mt-6{margin-top:48rpx;}.mt-8{margin-top:64rpx;}.inline{display:inline;}.block{display:block;}.inline-block{display:inline-block;}.list-item{display:list-item;}.hidden{display:none;}.h-16{height:128rpx;}.h-1px{height:1px;}.h-28{height:224rpx;}.h-4,.h4{height:32rpx;}.h-40rpx,.h5{height:40rpx;}.h-8{height:64rpx;}.h-full{height:100%;}.h1{height:8rpx;}.h2{height:16rpx;}.h3{height:24rpx;}.h6{height:48rpx;}.max-w-100{max-width:800rpx;}.w-16{width:128rpx;}.w-160px{width:160px;}.w-28{width:224rpx;}.w-4{width:32rpx;}.w-40rpx{width:40rpx;}.w-8{width:64rpx;}.w-80_a_{width:80%;}.w-full{width:100%;}.wxs{width:640rpx;}.flex{display:flex;}.flex-1{flex:1 1 0%;}.flex-shrink{flex-shrink:1;}.flex-col{flex-direction:column;}.flex-wrap{flex-wrap:wrap;}.table{display:table;}.table-cell{display:table-cell;}.table-row{display:table-row;}.table-row-group{display:table-row-group;}.border-collapse{border-collapse:collapse;}.transform{transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}@keyframes spin{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}.animate-spin{animation:spin 1s linear infinite;}.cursor-pointer{cursor:pointer;}.resize{resize:both;}.items-center{align-items:center;}.items-stretch{align-items:stretch;}.justify-center{justify-content:center;}.justify-between{justify-content:space-between;}.gap-2{gap:16rpx;}.gap-4{gap:32rpx;}.space-x-1>view+view,.space-x-1>view+button,.space-x-1>view+text,.space-x-1>view+image,.space-x-1>button+view,.space-x-1>button+button,.space-x-1>button+text,.space-x-1>button+image,.space-x-1>text+view,.space-x-1>text+button,.space-x-1>text+text,.space-x-1>text+image,.space-x-1>image+view,.space-x-1>image+button,.space-x-1>image+text,.space-x-1>image+image{--un-space-x-reverse:0;margin-left:calc(8rpx * calc(1 - var(--un-space-x-reverse)));margin-right:calc(8rpx * var(--un-space-x-reverse));}.space-x-2>view+view,.space-x-2>view+button,.space-x-2>view+text,.space-x-2>view+image,.space-x-2>button+view,.space-x-2>button+button,.space-x-2>button+text,.space-x-2>button+image,.space-x-2>text+view,.space-x-2>text+button,.space-x-2>text+text,.space-x-2>text+image,.space-x-2>image+view,.space-x-2>image+button,.space-x-2>image+text,.space-x-2>image+image{--un-space-x-reverse:0;margin-left:calc(16rpx * calc(1 - var(--un-space-x-reverse)));margin-right:calc(16rpx * var(--un-space-x-reverse));}.space-x-3>view+view,.space-x-3>view+button,.space-x-3>view+text,.space-x-3>view+image,.space-x-3>button+view,.space-x-3>button+button,.space-x-3>button+text,.space-x-3>button+image,.space-x-3>text+view,.space-x-3>text+button,.space-x-3>text+text,.space-x-3>text+image,.space-x-3>image+view,.space-x-3>image+button,.space-x-3>image+text,.space-x-3>image+image{--un-space-x-reverse:0;margin-left:calc(24rpx * calc(1 - var(--un-space-x-reverse)));margin-right:calc(24rpx * var(--un-space-x-reverse));}.space-y-1>view+view,.space-y-1>view+button,.space-y-1>view+text,.space-y-1>view+image,.space-y-1>button+view,.space-y-1>button+button,.space-y-1>button+text,.space-y-1>button+image,.space-y-1>text+view,.space-y-1>text+button,.space-y-1>text+text,.space-y-1>text+image,.space-y-1>image+view,.space-y-1>image+button,.space-y-1>image+text,.space-y-1>image+image{--un-space-y-reverse:0;margin-top:calc(8rpx * calc(1 - var(--un-space-y-reverse)));margin-bottom:calc(8rpx * var(--un-space-y-reverse));}.space-y-2>view+view,.space-y-2>view+button,.space-y-2>view+text,.space-y-2>view+image,.space-y-2>button+view,.space-y-2>button+button,.space-y-2>button+text,.space-y-2>button+image,.space-y-2>text+view,.space-y-2>text+button,.space-y-2>text+text,.space-y-2>text+image,.space-y-2>image+view,.space-y-2>image+button,.space-y-2>image+text,.space-y-2>image+image{--un-space-y-reverse:0;margin-top:calc(16rpx * calc(1 - var(--un-space-y-reverse)));margin-bottom:calc(16rpx * var(--un-space-y-reverse));}.divide-x>view+view,.divide-x>view+button,.divide-x>view+text,.divide-x>view+image,.divide-x>button+view,.divide-x>button+button,.divide-x>button+text,.divide-x>button+image,.divide-x>text+view,.divide-x>text+button,.divide-x>text+text,.divide-x>text+image,.divide-x>image+view,.divide-x>image+button,.divide-x>image+text,.divide-x>image+image{--un-divide-x-reverse:0;border-left-width:calc(1px * calc(1 - var(--un-divide-x-reverse)));border-right-width:calc(1px * var(--un-divide-x-reverse));}.divide-y>view+view,.divide-y>view+button,.divide-y>view+text,.divide-y>view+image,.divide-y>button+view,.divide-y>button+button,.divide-y>button+text,.divide-y>button+image,.divide-y>text+view,.divide-y>text+button,.divide-y>text+text,.divide-y>text+image,.divide-y>image+view,.divide-y>image+button,.divide-y>image+text,.divide-y>image+image{--un-divide-y-reverse:0;border-top-width:calc(1px * calc(1 - var(--un-divide-y-reverse)));border-bottom-width:calc(1px * var(--un-divide-y-reverse));}.divide-gray-100>view+view,.divide-gray-100>view+button,.divide-gray-100>view+text,.divide-gray-100>view+image,.divide-gray-100>button+view,.divide-gray-100>button+button,.divide-gray-100>button+text,.divide-gray-100>button+image,.divide-gray-100>text+view,.divide-gray-100>text+button,.divide-gray-100>text+text,.divide-gray-100>text+image,.divide-gray-100>image+view,.divide-gray-100>image+button,.divide-gray-100>image+text,.divide-gray-100>image+image{--un-divide-opacity:1;border-color:rgba(243, 244, 246, var(--un-divide-opacity)) /* #f3f4f6 */;}.overflow-hidden{overflow:hidden;}.break-all{word-break:break-all;}.b,.border{border-width:1px;}.border-2{border-width:2px;}.border-blue-500{--un-border-opacity:1;border-color:rgba(59, 130, 246, var(--un-border-opacity));}.border-t-transparent{border-top-color:transparent;}.rounded-full{border-radius:9999px;}.rounded-lg{border-radius:16rpx;}.bg-_a_eee{--un-bg-opacity:1;background-color:rgba(238, 238, 238, var(--un-bg-opacity)) /* #eee */;}.bg-blue-50{--un-bg-opacity:1;background-color:rgba(239, 246, 255, var(--un-bg-opacity)) /* #eff6ff */;}.bg-blue-500{--un-bg-opacity:1;background-color:rgba(59, 130, 246, var(--un-bg-opacity)) /* #3b82f6 */;}.bg-gray-50{--un-bg-opacity:1;background-color:rgba(249, 250, 251, var(--un-bg-opacity)) /* #f9fafb */;}.bg-green-50{--un-bg-opacity:1;background-color:rgba(240, 253, 244, var(--un-bg-opacity)) /* #f0fdf4 */;}.bg-orange-50{--un-bg-opacity:1;background-color:rgba(255, 247, 237, var(--un-bg-opacity)) /* #fff7ed */;}.bg-purple-50{--un-bg-opacity:1;background-color:rgba(250, 245, 255, var(--un-bg-opacity)) /* #faf5ff */;}.bg-red-50{--un-bg-opacity:1;background-color:rgba(254, 242, 242, var(--un-bg-opacity)) /* #fef2f2 */;}.bg-white{--un-bg-opacity:1;background-color:rgba(255, 255, 255, var(--un-bg-opacity)) /* #fff */;}.active_a_bg-gray-100:active{--un-bg-opacity:1;background-color:rgba(243, 244, 246, var(--un-bg-opacity)) /* #f3f4f6 */;}.p-3{padding:24rpx;}.p-4{padding:32rpx;}.p-6{padding:48rpx;}.p1{padding:8rpx;}.px,.px-4{padding-left:32rpx;padding-right:32rpx;}.px-2{padding-left:16rpx;padding-right:16rpx;}.py-1{padding-top:8rpx;padding-bottom:8rpx;}.py-12{padding-top:96rpx;padding-bottom:96rpx;}.py-4{padding-top:32rpx;padding-bottom:32rpx;}.py-6{padding-top:48rpx;padding-bottom:48rpx;}.py-8{padding-top:64rpx;padding-bottom:64rpx;}.pt-2{padding-top:16rpx;}.text-center{text-align:center;}.text-right{text-align:right;}.text-justify{text-align:justify;}.indent{text-indent:48rpx;}.text-2xl{font-size:48rpx;line-height:64rpx;}.text-4{font-size:32rpx;}.text-4xl{font-size:72rpx;line-height:80rpx;}.text-base{font-size:32rpx;line-height:48rpx;}.text-lg{font-size:36rpx;line-height:56rpx;}.text-sm{font-size:28rpx;line-height:40rpx;}.text-xl{font-size:40rpx;line-height:56rpx;}.text-xs{font-size:24rpx;line-height:32rpx;}.text-_a__a_d14328_a_{--un-text-opacity:1;color:rgba(209, 67, 40, var(--un-text-opacity)) /* #d14328 */;}.text-amber-500{--un-text-opacity:1;color:rgba(245, 158, 11, var(--un-text-opacity)) /* #f59e0b */;}.text-blue-500{--un-text-opacity:1;color:rgba(59, 130, 246, var(--un-text-opacity)) /* #3b82f6 */;}.text-blue-600{--un-text-opacity:1;color:rgba(37, 99, 235, var(--un-text-opacity)) /* #2563eb */;}.text-cyan-500{--un-text-opacity:1;color:rgba(6, 182, 212, var(--un-text-opacity)) /* #06b6d4 */;}.text-gray-300{--un-text-opacity:1;color:rgba(209, 213, 219, var(--un-text-opacity)) /* #d1d5db */;}.text-gray-400{--un-text-opacity:1;color:rgba(156, 163, 175, var(--un-text-opacity)) /* #9ca3af */;}.text-gray-500{--un-text-opacity:1;color:rgba(107, 114, 128, var(--un-text-opacity)) /* #6b7280 */;}.text-gray-600{--un-text-opacity:1;color:rgba(75, 85, 99, var(--un-text-opacity)) /* #4b5563 */;}.text-gray-700{--un-text-opacity:1;color:rgba(55, 65, 81, var(--un-text-opacity)) /* #374151 */;}.text-gray-800{--un-text-opacity:1;color:rgba(31, 41, 55, var(--un-text-opacity)) /* #1f2937 */;}.text-green,.text-green-400{--un-text-opacity:1;color:rgba(74, 222, 128, var(--un-text-opacity)) /* #4ade80 */;}.text-green-500{--un-text-opacity:1;color:rgba(34, 197, 94, var(--un-text-opacity)) /* #22c55e */;}.text-green-600{--un-text-opacity:1;color:rgba(22, 163, 74, var(--un-text-opacity)) /* #16a34a */;}.text-indigo-500{--un-text-opacity:1;color:rgba(99, 102, 241, var(--un-text-opacity)) /* #6366f1 */;}.text-orange-500{--un-text-opacity:1;color:rgba(249, 115, 22, var(--un-text-opacity)) /* #f97316 */;}.text-purple-500{--un-text-opacity:1;color:rgba(168, 85, 247, var(--un-text-opacity)) /* #a855f7 */;}.text-red{--un-text-opacity:1;color:rgba(248, 113, 113, var(--un-text-opacity)) /* #f87171 */;}.text-red-500{--un-text-opacity:1;color:rgba(239, 68, 68, var(--un-text-opacity)) /* #ef4444 */;}.text-red-600{--un-text-opacity:1;color:rgba(220, 38, 38, var(--un-text-opacity)) /* #dc2626 */;}.text-white{--un-text-opacity:1;color:rgba(255, 255, 255, var(--un-text-opacity)) /* #fff */;}.font-bold{font-weight:700;}.font-medium{font-weight:500;}.font-semibold{font-weight:600;}.leading-8{line-height:64rpx;}.leading-tight{line-height:1.25;}.uppercase{text-transform:uppercase;}.italic{font-style:italic;}.line-through{text-decoration-line:line-through;}.underline{text-decoration-line:underline;}.tab{-moz-tab-size:4;-o-tab-size:4;tab-size:4;}.text-shadow{--un-text-shadow:0 0 1px var(--un-text-shadow-color, rgba(0, 0, 0, 0.2)),0 0 1px var(--un-text-shadow-color, rgba(1, 0, 5, 0.1));text-shadow:var(--un-text-shadow);}.opacity-20{opacity:0.2;}.shadow-sm{--un-shadow:var(--un-shadow-inset) 0 1px 2px 0 var(--un-shadow-color, rgba(0, 0, 0, 0.05));box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);}.outline{outline-style:solid;}.ring{--un-ring-width:3px;--un-ring-offset-shadow:var(--un-ring-inset) 0 0 0 var(--un-ring-offset-width) var(--un-ring-offset-color);--un-ring-shadow:var(--un-ring-inset) 0 0 0 calc(var(--un-ring-width) + var(--un-ring-offset-width)) var(--un-ring-color);box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);}.blur{--un-blur:blur(8px);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.filter{filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.backdrop-filter{-webkit-backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.duration-200{transition-duration:200ms;}.ease,.ease-in-out{transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);}.ease-out{transition-timing-function:cubic-bezier(0, 0, 0.2, 1);} 
page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}