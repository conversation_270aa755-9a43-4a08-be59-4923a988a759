/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.trade-request-item.data-v-0e90c9d7 {
  transition: all 0.3s ease;
}
.trade-request-item.data-v-0e90c9d7:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.trade-request-item .card-header.data-v-0e90c9d7 {
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 12rpx;
  margin-bottom: 12rpx;
}
.trade-request-item .card-header .request-type.data-v-0e90c9d7 {
  font-size: 32rpx;
  color: #303133;
  font-weight: 600;
}
.trade-request-item .card-header .request-id.data-v-0e90c9d7, .trade-request-item .card-header .request-time.data-v-0e90c9d7 {
  font-size: 24rpx;
  color: #909399;
}
.trade-request-item .card-header .badges .execution-mode-badge.data-v-0e90c9d7, .trade-request-item .card-header .badges .status-badge.data-v-0e90c9d7 {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}
.trade-request-item .card-body .info-label.data-v-0e90c9d7, .trade-request-item .card-body .section-label.data-v-0e90c9d7 {
  font-size: 22rpx;
  color: #909399;
  margin-bottom: 4rpx;
}
.trade-request-item .card-body .info-value.data-v-0e90c9d7 {
  font-size: 26rpx;
  color: #303133;
  font-weight: 500;
}
.trade-request-item .card-body .section-content .quantity.data-v-0e90c9d7 {
  font-size: 28rpx;
  color: #303133;
  font-weight: 500;
}
.trade-request-item .card-body .section-content .price.data-v-0e90c9d7 {
  font-size: 26rpx;
  color: #606266;
}
.trade-request-item .card-body .section-content .no-execution.data-v-0e90c9d7 {
  font-size: 28rpx;
  color: #c0c4cc;
}
.trade-request-item .card-body .section-content .remaining.data-v-0e90c9d7 {
  font-weight: 500;
}
.trade-request-item .card-footer.data-v-0e90c9d7 {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 12rpx;
}
.trade-request-item .card-footer .additional-info.data-v-0e90c9d7 {
  font-size: 22rpx;
}
.trade-request-item .card-footer .additional-info .info-label.data-v-0e90c9d7 {
  color: #909399;
  font-weight: 500;
}
.trade-request-item .card-footer .additional-info .info-value.data-v-0e90c9d7 {
  color: #606266;
}
.trade-request-item .card-footer .additional-info .warning-text.data-v-0e90c9d7 {
  font-weight: 500;
}
.trade-request-item .card-footer .notes.data-v-0e90c9d7 {
  background-color: #f8f9fa;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  margin-top: 8rpx;
}
.trade-request-item .card-footer .notes .info-label.data-v-0e90c9d7 {
  font-size: 22rpx;
  color: #909399;
}
.trade-request-item .card-footer .notes .info-value.data-v-0e90c9d7 {
  font-size: 24rpx;
  color: #606266;
  line-height: 1.4;
}
.trade-request-item .card-footer .action-buttons .pricer-actions.data-v-0e90c9d7 .wd-button, .trade-request-item .card-footer .action-buttons .manual-actions.data-v-0e90c9d7 .wd-button, .trade-request-item .card-footer .action-buttons .auto-sim-actions.data-v-0e90c9d7 .wd-button {
  margin-right: 8rpx;
  margin-bottom: 8rpx;
}