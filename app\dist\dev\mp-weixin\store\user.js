"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_auth = require("../api/auth.js");
const api_login = require("../api/login.js");
const utils_toast = require("../utils/toast.js");
const store_socket = require("./socket.js");
const initialUserState = {
  ID: 0,
  CreatedAt: "",
  UpdatedAt: "",
  uuid: "",
  userName: "",
  phone: "",
  nickName: "",
  headerImg: "/static/images/default-avatar.png",
  companyName: "",
  companyOrgId: "",
  companyAddress: "",
  authorityId: 888,
  enable: 1
};
const useUserStore = common_vendor.defineStore(
  "user",
  () => {
    const userInfo = common_vendor.ref(__spreadValues({}, initialUserState));
    const token = common_vendor.ref("");
    const isLoggedIn = common_vendor.computed(() => !!token.value && userInfo.value.ID > 0);
    const setUserInfo = (val, userToken) => {
      console.log("设置用户信息", val);
      if (!val.headerImg) {
        val.headerImg = initialUserState.headerImg;
      }
      userInfo.value = val;
      if (userToken) {
        token.value = userToken;
        common_vendor.index.setStorageSync("token", userToken);
        const socketStore = store_socket.useSocketStore();
        if (socketStore.isConnected) {
          socketStore.authenticate(userToken);
        }
      }
      common_vendor.index.setStorageSync("userInfo", val);
    };
    const setUserAvatar = (avatar) => {
      userInfo.value.headerImg = avatar;
      console.log("设置用户头像", avatar);
    };
    const removeUserInfo = () => {
      userInfo.value = __spreadValues({}, initialUserState);
      token.value = "";
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.removeStorageSync("token");
    };
    const getUserProfile = () => __async(exports, null, function* () {
      const res = yield api_auth.getProfile();
      const userData = res.data.userInfo;
      setUserInfo(userData);
      return res;
    });
    const sendVerificationCode = (phone) => __async(exports, null, function* () {
      const res = yield api_auth.sendLoginCode({ phone });
      if (res.code === 0) {
        utils_toast.toast.success(res.msg || "验证码发送成功");
      } else {
        utils_toast.toast.error(res.msg || "验证码发送失败");
        throw new Error(res.msg);
      }
      return res;
    });
    const phoneLogin = (data) => __async(exports, null, function* () {
      const res = yield api_auth.loginByPhone(data);
      console.log("手机号登录信息", res);
      if (res.code === 0) {
        utils_toast.toast.success(res.msg || "登录成功");
      } else {
        utils_toast.toast.error(res.msg || "登录失败");
        throw new Error(res.msg);
      }
      setUserInfo(res.data.user, res.data.token);
      utils_toast.toast.success("登录成功");
      return res;
    });
    const newWxLogin = () => __async(exports, null, function* () {
      const data = yield api_login.getWxCode();
      console.log("微信登录code", data);
      const res = yield api_auth.loginByWechat(data);
      setUserInfo(res.data.user, res.data.token);
      utils_toast.toast.success("微信登录成功");
      return res;
    });
    const logout = () => __async(exports, null, function* () {
      try {
        yield api_login.logout();
      } catch (error) {
        console.warn("调用退出登录API失败", error);
      }
      removeUserInfo();
      utils_toast.toast.success("已退出登录");
    });
    const usernameLogin = (data) => __async(exports, null, function* () {
      const res = yield api_auth.loginByUsername(data);
      console.log("用户名密码登录信息", res);
      if (res.code === 0) {
        utils_toast.toast.success(res.msg || "登录成功");
      } else {
        utils_toast.toast.error(res.msg || "登录失败");
        throw new Error(res.msg);
      }
      setUserInfo(res.data.user, res.data.token);
      utils_toast.toast.success("登录成功");
      return res;
    });
    const updateUserProfile = (data) => __async(exports, null, function* () {
      const res = yield api_auth.updateProfile(data);
      if (res.code === 0) {
        yield getUserProfile();
        utils_toast.toast.success("资料更新成功");
      } else {
        utils_toast.toast.error(res.data.msg || "更新失败");
        throw new Error(res.data.msg);
      }
      return res;
    });
    const changeUserPassword = (data) => __async(exports, null, function* () {
      const res = yield api_auth.changePassword(data);
      if (res.code === 0) {
        utils_toast.toast.success("密码修改成功");
      } else {
        utils_toast.toast.error(res.data.msg || "密码修改失败");
        throw new Error(res.data.msg);
      }
      return res;
    });
    return {
      // 状态
      userInfo,
      token,
      isLoggedIn,
      // 方法
      phoneLogin,
      usernameLogin,
      sendVerificationCode,
      wxLogin: newWxLogin,
      getUserProfile,
      updateUserProfile,
      changeUserPassword,
      setUserInfo,
      setUserAvatar,
      logout,
      clearUserInfo: removeUserInfo
      // 为HTTP错误处理提供清理用户信息的方法
    };
  },
  {
    persist: true
  }
);
exports.useUserStore = useUserStore;
