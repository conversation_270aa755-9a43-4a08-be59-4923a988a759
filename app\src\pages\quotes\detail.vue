<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "报价详情",
    "navigationStyle": "default"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import {
  getQuotationDetail,
  getPublicQuotationDetail,
  publishQuotation,
  toggleQuotationStatus,
  deleteQuotation
} from '@/api/quotation'
import type { IQuotationResponse } from '@/types/quotation'

defineOptions({
  name: 'QuotationDetail'
})

function goBack() {
  uni.navigateBack()
}

const userStore = useUserStore()
const quotationId = ref<number>()
const isLoading = ref(false)
const quotation = ref<IQuotationResponse>()
const isPublicAccess = ref<boolean>(false) // 标识是否从公开链接访问

const statusConfig = {
  Draft: { label: '草稿', description: '报价草稿，未公开发布' },
  Active: { label: '上线', description: '报价已公开，可被其他用户查看' }
}

const isOwner = computed(() => quotation.value && userStore.userInfo && quotation.value.userID === userStore.userInfo.ID)
const isPublicView = computed(() => !isOwner.value)
// 发布者查看时，无论什么状态都要显示相应的操作按钮
const canEdit = computed(() => isOwner.value) // 发布者任何状态都可以编辑
const canPublish = computed(() => isOwner.value && quotation.value?.status === 'Draft') // 只有草稿状态才显示发布按钮
const canToggleStatus = computed(() => isOwner.value && quotation.value?.status === 'Active') // 只有发布状态才显示下架按钮
const canDelete = computed(() => isOwner.value && quotation.value?.status === 'Draft') // 只有草稿状态才显示删除按钮
const canShare = computed(() => isOwner.value && quotation.value?.status === 'Active') // 只有发布状态才显示分享按钮
const showContactButton = computed(() => isPublicView.value && quotation.value?.status === 'Active')

onLoad((options) => {
  if (options?.id) {
    quotationId.value = parseInt(options.id)
    // 判断是否从公开分享链接访问（可以通过URL参数、来源页面等判断）
    isPublicAccess.value = options?.from === 'share' || options?.from === 'public' || options?.from === 'marketplace'
    loadQuotationDetail()
  } else {
    uni.showToast({ title: '报价ID不存在', icon: 'error' })
    setTimeout(() => uni.navigateBack(), 1500)
  }
})

async function loadQuotationDetail() {
  if (!quotationId.value) return
  isLoading.value = true
  try {
    let res
    if (isPublicAccess.value || !userStore.isLoggedIn) {
      // 如果是公开访问或未登录，使用公开接口
      res = await getPublicQuotationDetail(quotationId.value)
    } else {
      // 如果是登录用户访问，使用带权限验证的接口
      res = await getQuotationDetail(quotationId.value)
    }
    quotation.value = res.data
    uni.setNavigationBarTitle({ title: quotation.value.title || '报价详情' })
  } catch (error) {
    console.error('加载报价详情失败:', error)
    uni.showToast({ title: '加载失败', icon: 'error' })
    setTimeout(() => uni.navigateBack(), 1500)
  } finally {
    isLoading.value = false
  }
}

function editQuotation() {
  if (!quotation.value) return
  uni.navigateTo({ url: `/pages/quotes/edit?id=${quotation.value.id}` })
}

async function publishQuotationItem() {
  if (!quotation.value) return
  uni.showLoading({ title: '发布中...' })
  try {
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)
    await publishQuotation({ id: quotation.value.id, expiresAt: expiresAt.toISOString() })
    uni.showToast({ title: '发布成功', icon: 'success' })
    await loadQuotationDetail()
  } catch (error) {
    console.error('发布报价失败:', error)
    uni.showToast({ title: '发布失败', icon: 'error' })
  } finally {
    uni.hideLoading()
  }
}

async function toggleQuotationStatusItem() {
  if (!quotation.value) return
  // 现在只有发布状态才会显示下架按钮，所以这里只处理下架逻辑
  const actionText = '下架'
  const confirmText = '确定要将此报价下架吗？下架后报价将变为草稿状态。'

  const { confirm } = await uni.showModal({ title: `确认${actionText}`, content: confirmText })
  if (!confirm) return

  uni.showLoading({ title: `${actionText}中...` })
  try {
    await toggleQuotationStatus(quotation.value.id)
    uni.showToast({ title: `${actionText}成功`, icon: 'success' })
    await loadQuotationDetail()
  } catch (error) {
    console.error('下架失败:', error)
    uni.showToast({ title: `${actionText}失败`, icon: 'error' })
  } finally {
    uni.hideLoading()
  }
}

async function deleteQuotationItem() {
  if (!quotation.value) return
  const { confirm } = await uni.showModal({ title: '确认删除', content: '确定要删除这个报价草稿吗？' })
  if (!confirm) return

  uni.showLoading({ title: '删除中...' })
  try {
    await deleteQuotation(quotation.value.id)
    uni.showToast({ title: '删除成功', icon: 'success' })
    setTimeout(() => uni.navigateBack(), 1500)
  } catch (error) {
    console.error('删除报价失败:', error)
    uni.showToast({ title: '删除失败', icon: 'error' })
  } finally {
    uni.hideLoading()
  }
}

function contactPublisher() {
  if (!quotation.value) return
  uni.showToast({ title: '功能开发中', icon: 'none' })
}

function callPhone(phoneNumber: string) {
  if (!phoneNumber) return
  uni.makePhoneCall({ phoneNumber })
}

function formatPrice(q: IQuotationResponse): string {
  if (q.priceType === 'Fixed') return `¥ ${q.price.toFixed(2)}`
  if (q.priceType === 'Basis' && q.instrumentRef) {
    return `${q.instrumentRef.instrument_id} ${q.price >= 0 ? '+ ' : ''}${q.price}`
  }
  return q.price.toString()
}

function formatDateTime(dateTime: string): string {
  const d = new Date(dateTime)
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`
}

function formatRemainingTime(): string {
  if (!quotation.value || quotation.value.status !== 'Active') return ''
  if (quotation.value.isExpired) return '已过期'
  const { remainingHours } = quotation.value
  if (remainingHours <= 0) return '即将过期'
  if (remainingHours < 24) return `剩余 ${remainingHours} 小时`
  return `剩余 ${Math.floor(remainingHours / 24)} 天`
}

// ==================== 微信小程序分享功能 ====================

// 微信小程序分享到聊天
onShareAppMessage(() => {
  if (!quotation.value) return {}
  
  const title = quotation.value.title
  const path = `/pages/quotes/detail?id=${quotation.value.id}&from=share`
  
  return {
    title,
    path,
    imageUrl: quotation.value.user?.headerImg || 'static/logo.svg'
  }
})

// 微信小程序分享到朋友圈
onShareTimeline(() => {
  if (!quotation.value) return {}
  
  const title = `${quotation.value.title} - ${quotation.value.commodityName}`
  const query = `share=timeline&id=${quotation.value.id}&from=share`
  
  return {
    title,
    query,
    imageUrl: quotation.value.user?.headerImg || 'static/logo.svg'
  }
})

function shareQuotation() {
  if (!quotation.value) return
  
  // 微信小程序环境下，不需要调用 uni.share，系统会自动处理分享
  // 这里可以添加一些提示或统计
  uni.showToast({
    title: '请点击右上角分享',
    icon: 'none',
    duration: 2000
  })
}
</script>

<template>
  <view class="page-container gradient-bg-primary">
    <view v-if="isLoading" class="loading-container">
      <wd-loading custom-class="loading-spinner" />
      <text class="loading-text">加载中...</text>
    </view>
    
    <view v-else-if="quotation" class="detail-container">
      <!-- 发布者信息 -->
      <view v-if="isPublicView && quotation.user" class="publisher-info-section common-card">
        <view class="card-title">报价方信息</view>
        <wd-cell-group border>
          <wd-cell v-if="quotation.user?.companyName" title="发布企业" :value="quotation.user.companyName" />
          <wd-cell v-if="quotation.user?.nickName" title="联系人" :value="quotation.user.nickName" />
          <wd-cell title="联系电话">
            <view class="phone-cell" @click="callPhone(quotation.user.phone)">
              <text class="phone-number">{{ quotation.user.phone }}</text>
              <wd-icon name="phone" custom-class="phone-icon" />
            </view>
          </wd-cell>
        </wd-cell-group>
      </view>

      <!-- 主要信息区域 -->
      <view class="main-info-section common-card">
        <view class="status-tag" :class="quotation.status.toLowerCase()">
          {{ statusConfig[quotation.status].label }}
        </view>
        <view class="title-price-section">
          <text class="quotation-title">{{ quotation.title }}</text>
          <text class="quotation-price">{{ formatPrice(quotation) }}</text>
        </view>
        <view class="core-info-card">
          <view class="info-row">
            <text class="label">商品种类</text>
            <text class="value highlight">{{ quotation.commodityName || '-' }}</text>
          </view>
          <view class="info-row">
            <text class="label">交货地点</text>
            <text class="value highlight">{{ quotation.deliveryLocation }}</text>
          </view>
          <view class="info-row">
            <text class="label">价格类型</text>
            <text class="value highlight">{{ quotation.priceType === 'Fixed' ? '一口价' : '基差报价' }}</text>
          </view>
          <view v-if="quotation.priceType === 'Basis' && quotation.instrumentRef" class="info-row">
            <text class="label">参考合约</text>
            <text class="value highlight">{{ quotation.instrumentRef.instrument_name }}</text>
          </view>
        </view>
      </view>

      <!-- 次要信息区域 -->
      <view class="secondary-info-section common-card">
        <wd-cell-group>
          <wd-cell v-if="quotation.brand" title="品牌" :value="quotation.brand" />
          <wd-cell title="发布时间" :value="formatDateTime(quotation.createdAt)" />
          <wd-cell title="过期时间" :value="formatDateTime(quotation.expiresAt)" />
          <wd-cell v-if="quotation.status === 'Active'" title="剩余有效期">
            <text class="remaining-time" :class="{ expired: quotation.isExpired }">
              {{ formatRemainingTime() }}
            </text>
          </wd-cell>
        </wd-cell-group>
      </view>

      <!-- 详细信息区域 -->
      <view v-if="quotation.specifications || quotation.description" class="detail-info-section common-card">
        <wd-cell-group>
          <wd-cell v-if="quotation.specifications" title="规格说明">
            <text class="multi-line-text">{{ quotation.specifications }}</text>
          </wd-cell>
          <wd-cell v-if="quotation.description" title="补充说明">
            <text class="multi-line-text">{{ quotation.description }}</text>
          </wd-cell>
        </wd-cell-group>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-section">
        <view v-if="isOwner" class="owner-actions">
          <wd-button v-if="canEdit" custom-class="action-button" size="large" @click="editQuotation">编辑</wd-button>
          <wd-button v-if="canPublish" custom-class="action-button primary-button" size="large" @click="publishQuotationItem">发布</wd-button>
          <wd-button v-if="canToggleStatus" custom-class="action-button warning-button" size="large" @click="toggleQuotationStatusItem">下架</wd-button>
          <wd-button v-if="canDelete" custom-class="action-button error-button" size="large" @click="deleteQuotationItem">删除</wd-button>
          <wd-button v-if="canShare" custom-class="action-button success-button" plain size="large" @click="shareQuotation">分享</wd-button>
        </view>
        <view v-else class="public-actions">
          <wd-button v-if="showContactButton" custom-class="action-button primary-button" size="large" @click="contactPublisher">发起点价</wd-button>
          <wd-button custom-class="action-button success-button" plain size="large" @click="shareQuotation">分享给朋友</wd-button>
        </view>
      </view>
    </view>
    
    <view v-else class="error-container">
      <wd-img src="/static/images/error-404.png" width="200rpx" height="200rpx" mode="aspectFit" />
      <text class="error-text">报价不存在或已被删除</text>
      <wd-button custom-class="primary-button" @click="goBack">返回上一页</wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 页面容器样式已移至全局样式文件

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
  .loading-text { margin-top: 20rpx; font-size: 28rpx; color: #909399; }
  :deep(.loading-spinner) {
    --loading-color: #667eea;
  }
}

.detail-container {
  padding-bottom: 200rpx;
}

// 卡片内容样式（补充 common-card 缺失的样式）
.card-content {
  padding: 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
}

.publisher-info-section {
  padding: 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
  .card-title {
    font-size: 32rpx;
    font-weight: 600;
    margin-bottom: 24rpx;
    color: #333;
  }
  :deep(.wd-cell-group) {
    margin: 0 -40rpx -40rpx; // Remove padding to make cells full-width
  }
}

.main-info-section {
  position: relative;
  padding: 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
  .status-tag {
    position: absolute;
    top: 0;
    right: 0;
    padding: 12rpx 24rpx;
    border-radius: 0 20rpx 0 20rpx;
    font-size: 24rpx;
    font-weight: 600;
    color: white;
    &.active { background: linear-gradient(135deg, #4cd964 0%, #667eea 100%); }
    &.draft { background: linear-gradient(135deg, #909399 0%, #606266 100%); }
  }
  .title-price-section {
    margin-bottom: 32rpx;
    padding-right: 140rpx;
    .quotation-title {
      display: block;
      font-size: 40rpx;
      font-weight: 700;
      color: #333;
      line-height: 1.4;
      margin-bottom: 16rpx;
    }
    .quotation-price {
      font-size: 52rpx;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      color: transparent;
    }
  }
  .core-info-card {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12rpx;
    padding: 24rpx;
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;
      &:last-child { margin-bottom: 0; }
      .label { font-size: 28rpx; color: #606266; }
      .value.highlight { font-size: 30rpx; color: #667eea; font-weight: 600; }
    }
  }
}

.secondary-info-section, .detail-info-section {
  padding: 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
  :deep(.wd-cell-group) {
    margin: -40rpx;
  }
}

.phone-cell {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .phone-number { color: #667eea; font-weight: 500; margin-right: 8rpx; }
  .phone-icon { color: #667eea; font-size: 32rpx; }
}

.multi-line-text {
  max-width: 400rpx;
  line-height: 1.6;
  word-break: break-all;
  white-space: pre-wrap;
  color: #606266;
}

.remaining-time {
  font-weight: 500;
  color: #4cd964;
  &.expired { color: #dd524d; }
}

.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.85);
  padding: 24rpx 36rpx;
  padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  .owner-actions, .public-actions {
    display: flex;
    gap: 24rpx;
    flex-wrap: wrap;
  }
  :deep(.action-button) {
    flex: 1;
    border-radius: 44rpx;
    height: 88rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    border: none;
    color: #333;
    background: #fff;
    min-width: calc(33.33% - 16rpx);
    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
    }
  }
  :deep(.primary-button) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }
  :deep(.warning-button) {
    background: #f5a623;
    color: white;
  }
  :deep(.error-button) {
    background: #dd524d;
    color: white;
  }
  :deep(.success-button) {
    background: #4cd964;
    color: white;
    &.is-plain {
      background: white;
      color: #4cd964;
      border: 1rpx solid #4cd964;
    }
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
  padding: 40rpx;
  .error-text {
    font-size: 28rpx;
    color: #909399;
    margin: 24rpx 0 40rpx;
    text-align: center;
  }
}
</style>
