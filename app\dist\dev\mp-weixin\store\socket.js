"use strict";
const common_vendor = require("../common/vendor.js");
const store_user = require("./user.js");
const utils_toast = require("../utils/toast.js");
var define_import_meta_env_default = {};
const VITE_WS_URL = define_import_meta_env_default.VITE_WS_URL || "ws://localhost:8888/ws/app";
const useSocketStore = common_vendor.defineStore("socket", () => {
  const isConnected = common_vendor.ref(false);
  const isAuthenticated = common_vendor.ref(false);
  let heartbeatInterval = null;
  let reconnectTimeout = null;
  let reconnectAttempts = 0;
  const eventHandlers = /* @__PURE__ */ new Map();
  function registerHandler(eventType, handler) {
    if (!eventHandlers.has(eventType)) {
      eventHandlers.set(eventType, []);
    }
    const handlers = eventHandlers.get(eventType);
    if (!handlers.includes(handler)) {
      handlers.push(handler);
      console.log(`[WebSocket] 注册事件处理器: ${eventType}`);
    }
  }
  function unregisterHandler(eventType, handler) {
    const handlers = eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
        console.log(`[WebSocket] 取消注册事件处理器: ${eventType}`);
        if (handlers.length === 0) {
          eventHandlers.delete(eventType);
        }
      }
    }
  }
  function connect() {
    if (isConnected.value) {
      console.log("[WebSocket] 已连接，无需重复连接。");
      return;
    }
    console.log(`[WebSocket] 正在连接到 ${VITE_WS_URL}...`);
    try {
      common_vendor.index.connectSocket({
        url: VITE_WS_URL,
        complete: () => {
          console.log("[WebSocket] 连接请求已发送");
        }
      });
      common_vendor.index.onSocketOpen((res) => {
        console.log("[WebSocket] 连接成功！", res);
        isConnected.value = true;
        reconnectAttempts = 0;
        if (reconnectTimeout) {
          clearTimeout(reconnectTimeout);
          reconnectTimeout = null;
        }
        startHeartbeat();
        const userStore = store_user.useUserStore();
        if (userStore.isLoggedIn) {
          authenticate(userStore.token);
        }
      });
      common_vendor.index.onSocketMessage((res) => {
        var _a, _b, _c, _d, _e, _f;
        try {
          const message = JSON.parse(res.data);
          if (message.event !== "pong") {
            console.log("[WebSocket] 收到消息:", message);
          }
          switch (message.event) {
            case "pong":
              if ((_a = message.payload) == null ? void 0 : _a.timestamp) {
                const rtt = Date.now() - message.payload.timestamp;
                console.log(`[WebSocket] Pong received. RTT: ${rtt}ms`);
              }
              break;
            case "auth_response":
              if ((_b = message.payload) == null ? void 0 : _b.success) {
                isAuthenticated.value = true;
                console.log("[WebSocket] 认证成功。");
              } else {
                utils_toast.toast.error(`认证失败: ${((_c = message.payload) == null ? void 0 : _c.message) || "未知错误"}`);
              }
              break;
            case "notification":
              utils_toast.toast.info(((_d = message.payload) == null ? void 0 : _d.content) || "收到一条新通知");
              break;
            case "trade_update":
              console.log("[WebSocket] 交易更新:", message.payload);
              utils_toast.toast.success("您的交易状态已更新");
              break;
            case "error":
              const errorMsg = ((_e = message.payload) == null ? void 0 : _e.message) || "服务器错误";
              const errorCode = ((_f = message.payload) == null ? void 0 : _f.code) || 0;
              console.error(`[WebSocket] 服务器错误 [${errorCode}]: ${errorMsg}`);
              utils_toast.toast.error(errorMsg);
              break;
          }
          const handlers = eventHandlers.get(message.event);
          if (handlers && handlers.length > 0) {
            handlers.forEach((handler) => {
              try {
                handler(message.payload);
              } catch (error) {
                console.error(`[WebSocket] 事件处理器执行失败 (${message.event}):`, error);
              }
            });
          }
        } catch (error) {
          console.error("[WebSocket] 解析消息失败:", error, "原始数据:", res.data);
        }
      });
      common_vendor.index.onSocketClose((res) => {
        console.log("[WebSocket] 连接已关闭。", res);
        isConnected.value = false;
        isAuthenticated.value = false;
        stopHeartbeat();
        if (!reconnectTimeout) {
          attemptReconnect();
        }
      });
      common_vendor.index.onSocketError((error) => {
        console.error("[WebSocket] 发生错误:", error);
        isConnected.value = false;
        attemptReconnect();
      });
    } catch (error) {
      console.error("[WebSocket] 创建连接失败:", error);
      isConnected.value = false;
      attemptReconnect();
    }
  }
  function disconnect() {
    console.log("[WebSocket] 主动断开连接。");
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout);
      reconnectTimeout = null;
    }
    common_vendor.index.closeSocket({
      success: () => {
        console.log("[WebSocket] 主动关闭连接成功");
      },
      fail: (error) => {
        console.error("[WebSocket] 关闭连接失败:", error);
      }
    });
    stopHeartbeat();
  }
  function sendMessage(event, payload) {
    if (!isConnected.value) {
      utils_toast.toast.error("连接已断开，消息发送失败");
      return;
    }
    const message = {
      event,
      payload,
      timestamp: Date.now()
    };
    console.log("[WebSocket] 发送消息:", message);
    common_vendor.index.sendSocketMessage({
      data: JSON.stringify(message),
      success: () => {
        console.log("[WebSocket] 消息发送成功");
      },
      fail: (error) => {
        var _a;
        console.error("[WebSocket] 消息发送失败:", error);
        if ((_a = error.errMsg) == null ? void 0 : _a.includes("not connected")) {
          isConnected.value = false;
          attemptReconnect();
        }
      }
    });
  }
  function startHeartbeat() {
    stopHeartbeat();
    heartbeatInterval = setInterval(() => {
      sendMessage("ping", { timestamp: Date.now() });
    }, 25e3);
  }
  function stopHeartbeat() {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }
  }
  function attemptReconnect() {
    if (reconnectTimeout) {
      return;
    }
    if (reconnectAttempts >= 5) {
      console.log("[WebSocket] 重连次数过多，已停止。");
      return;
    }
    const delay = Math.pow(2, reconnectAttempts) * 1e3;
    reconnectAttempts++;
    console.log(`[WebSocket] 将在 ${delay / 1e3} 秒后尝试重新连接...`);
    reconnectTimeout = setTimeout(() => {
      console.log(`[WebSocket] 第 ${reconnectAttempts} 次重连...`);
      connect();
    }, delay);
  }
  function authenticate(token) {
    if (!isConnected.value) {
      console.warn("[WebSocket] 连接未建立，无法认证");
      return;
    }
    sendMessage("auth", { token });
  }
  function subscribe(channel) {
    sendMessage("subscribe", { channel });
  }
  function unsubscribe(channel) {
    sendMessage("unsubscribe", { channel });
  }
  return {
    isConnected: common_vendor.readonly(isConnected),
    isAuthenticated: common_vendor.readonly(isAuthenticated),
    connect,
    disconnect,
    sendMessage,
    authenticate,
    subscribe,
    unsubscribe,
    registerHandler,
    unregisterHandler
  };
});
exports.useSocketStore = useSocketStore;
