"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_instrument = require("../../store/instrument.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  _easycom_wd_button();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "TestInstrumentStore"
}), {
  __name: "test-instrument-store",
  setup(__props) {
    const instrumentStore = store_instrument.useInstrumentStore();
    const testResults = common_vendor.ref([]);
    const isRunning = common_vendor.ref(false);
    function addResult(message, isError = false) {
      const timestamp = (/* @__PURE__ */ new Date()).toLocaleTimeString();
      const prefix = isError ? "❌" : "✅";
      testResults.value.push(`${prefix} [${timestamp}] ${message}`);
    }
    function clearResults() {
      testResults.value = [];
    }
    function runAllTests() {
      return __async(this, null, function* () {
        if (isRunning.value)
          return;
        isRunning.value = true;
        clearResults();
        try {
          addResult("开始测试 Instrument Store...");
          addResult("测试1: 加载合约数据");
          const loadResult = yield instrumentStore.loadInstruments(true);
          if (loadResult.success) {
            addResult(`数据加载成功，共 ${instrumentStore.instruments.length} 个合约`);
          } else {
            addResult(`数据加载失败: ${loadResult.error}`, true);
            return;
          }
          addResult("测试2: 获取交易所列表");
          const exchanges = instrumentStore.getExchanges();
          addResult(`获取到 ${exchanges.length} 个交易所: ${exchanges.map((e) => e.label).join(", ")}`);
          addResult("测试3: 按ID获取合约");
          if (instrumentStore.instruments.length > 0) {
            const firstInstrument = instrumentStore.instruments[0];
            const foundById = instrumentStore.getInstrumentById(firstInstrument.id);
            if (foundById && foundById.id === firstInstrument.id) {
              addResult(`按ID获取成功: ${foundById.instrument_id}`);
            } else {
              addResult("按ID获取失败", true);
            }
          }
          addResult("测试4: 按instrument_id获取合约");
          if (instrumentStore.instruments.length > 0) {
            const firstInstrument = instrumentStore.instruments[0];
            const foundByInstrumentId = instrumentStore.getInstrumentByInstrumentId(firstInstrument.instrument_id);
            if (foundByInstrumentId && foundByInstrumentId.instrument_id === firstInstrument.instrument_id) {
              addResult(`按instrument_id获取成功: ${foundByInstrumentId.instrument_id}`);
            } else {
              addResult("按instrument_id获取失败", true);
            }
          }
          addResult("测试5: 获取产品列表");
          if (exchanges.length > 0) {
            const firstExchange = exchanges[0].value;
            const products = instrumentStore.getProductsByExchange(firstExchange);
            addResult(`交易所 ${firstExchange} 有 ${products.length} 个产品`);
          }
          addResult("测试6: 获取合约列表");
          if (exchanges.length > 0 && instrumentStore.instruments.length > 0) {
            const firstInstrument = instrumentStore.instruments[0];
            const contracts = instrumentStore.getContractsByProduct(firstInstrument.exchange_id, firstInstrument.product_name);
            addResult(`产品 ${firstInstrument.product_name} 有 ${contracts.length} 个合约`);
          }
          addResult("测试7: 搜索功能");
          const searchResults = instrumentStore.searchInstruments("i25");
          addResult(`搜索 'i25' 找到 ${searchResults.length} 个结果`);
          addResult("测试8: 安全获取方法");
          if (instrumentStore.instruments.length > 0) {
            const firstInstrument = instrumentStore.instruments[0];
            const safeResult = yield instrumentStore.safeGetInstrumentById(firstInstrument.id);
            if (safeResult) {
              addResult(`安全获取成功: ${safeResult.instrument_id}`);
            } else {
              addResult("安全获取失败", true);
            }
          }
          addResult("所有测试完成！");
        } catch (error) {
          addResult(`测试过程中出现错误: ${error}`, true);
        } finally {
          isRunning.value = false;
        }
      });
    }
    function testCache() {
      return __async(this, null, function* () {
        if (isRunning.value)
          return;
        isRunning.value = true;
        addResult("测试缓存功能...");
        try {
          const start1 = Date.now();
          yield instrumentStore.loadInstruments(true);
          const time1 = Date.now() - start1;
          addResult(`首次加载耗时: ${time1}ms`);
          const start2 = Date.now();
          yield instrumentStore.loadInstruments();
          const time2 = Date.now() - start2;
          addResult(`缓存加载耗时: ${time2}ms`);
          if (time2 < time1 / 2) {
            addResult("缓存功能正常工作");
          } else {
            addResult("缓存功能可能有问题", true);
          }
        } catch (error) {
          addResult(`缓存测试失败: ${error}`, true);
        } finally {
          isRunning.value = false;
        }
      });
    }
    const storeStatus = common_vendor.computed(() => {
      return {
        isLoaded: instrumentStore.isLoaded,
        isLoading: instrumentStore.isLoading,
        isDataFresh: instrumentStore.isDataFresh,
        instrumentCount: instrumentStore.instruments.length,
        error: instrumentStore.error
      };
    });
    common_vendor.onMounted(() => {
      addResult("页面已加载，可以开始测试");
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(storeStatus.value.isLoaded ? "是" : "否"),
        b: common_vendor.t(storeStatus.value.isLoading ? "是" : "否"),
        c: common_vendor.t(storeStatus.value.isDataFresh ? "是" : "否"),
        d: common_vendor.t(storeStatus.value.instrumentCount),
        e: storeStatus.value.error
      }, storeStatus.value.error ? {
        f: common_vendor.t(storeStatus.value.error)
      } : {}, {
        g: common_vendor.t(isRunning.value ? "测试中..." : "运行所有测试"),
        h: common_vendor.o(runAllTests),
        i: common_vendor.p({
          type: "primary",
          disabled: isRunning.value,
          ["custom-class"]: "test-button"
        }),
        j: common_vendor.t(isRunning.value ? "测试中..." : "测试缓存功能"),
        k: common_vendor.o(testCache),
        l: common_vendor.p({
          type: "success",
          disabled: isRunning.value,
          ["custom-class"]: "test-button"
        }),
        m: common_vendor.o(clearResults),
        n: common_vendor.p({
          type: "warning",
          ["custom-class"]: "test-button"
        }),
        o: common_vendor.f(testResults.value, (result, index, i0) => {
          return {
            a: common_vendor.t(result),
            b: index,
            c: result.includes("❌") ? 1 : ""
          };
        }),
        p: testResults.value.length === 0
      }, testResults.value.length === 0 ? {} : {});
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e50356ae"]]);
wx.createPage(MiniProgramPage);
