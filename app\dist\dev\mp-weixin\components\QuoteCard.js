"use strict";
const common_vendor = require("../common/vendor.js");
const store_market = require("../store/market.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  _easycom_wd_icon2();
}
const _easycom_wd_icon = () => "../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
if (!Math) {
  _easycom_wd_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "QuoteCard",
  props: {
    instrumentId: { default: "" },
    symbol: { default: "" },
    exchange: { default: "DCE" }
  },
  emits: ["priceClick"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const marketStore = store_market.useMarketStore();
    const marketData = common_vendor.computed(() => {
      const symbol = props.instrumentId || props.symbol;
      if (!symbol)
        return null;
      return marketStore.getMarketData(symbol);
    });
    const isSubscribed = common_vendor.computed(() => {
      const symbol = props.instrumentId || props.symbol;
      if (!symbol)
        return false;
      return marketStore.isSubscribed(symbol);
    });
    const priceChange = common_vendor.computed(() => {
      if (!marketData.value)
        return 0;
      const lastPrice = parseFloat(marketData.value.last_price);
      const preClose = parseFloat(marketData.value.pre_close);
      if (isNaN(lastPrice) || isNaN(preClose))
        return 0;
      return lastPrice - preClose;
    });
    const priceChangePercent = common_vendor.computed(() => {
      if (!marketData.value)
        return 0;
      const lastPrice = parseFloat(marketData.value.last_price);
      const preClose = parseFloat(marketData.value.pre_close);
      if (isNaN(lastPrice) || isNaN(preClose) || preClose === 0)
        return 0;
      return (lastPrice - preClose) / preClose * 100;
    });
    const priceChangeClass = common_vendor.computed(() => {
      if (priceChange.value > 0)
        return "text-red-500";
      if (priceChange.value < 0)
        return "text-green-500";
      return "text-gray-700";
    });
    const formatTime = (datetime) => {
      if (!datetime || datetime === "None")
        return "--";
      const date = new Date(datetime);
      return date.toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit"
      });
    };
    const formatPrice = (price) => {
      if (typeof price === "string") {
        const numPrice = parseFloat(price);
        return isNaN(numPrice) ? "0.0" : numPrice.toFixed(1);
      }
      return price ? price.toFixed(1) : "0.0";
    };
    const onPriceClick = (type, price) => {
      emit("priceClick", { type, price, numericPrice: price });
    };
    const subscribeToMarket = () => {
      const symbol = props.instrumentId || props.symbol;
      if (symbol && !marketStore.isSubscribed(symbol)) {
        marketStore.subscribe(symbol, props.exchange);
      }
    };
    common_vendor.watch(() => [props.instrumentId, props.symbol, props.exchange], () => {
      subscribeToMarket();
    }, { immediate: false });
    common_vendor.onMounted(() => {
      subscribeToMarket();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: marketData.value
      }, marketData.value ? {
        b: common_vendor.t(marketData.value.symbol),
        c: common_vendor.t(marketData.value.exchange),
        d: common_vendor.t(formatTime(marketData.value.datetime)),
        e: common_vendor.t(formatPrice(marketData.value.last_price)),
        f: common_vendor.n(priceChangeClass.value),
        g: common_vendor.o(($event) => onPriceClick("last", parseFloat(marketData.value.last_price))),
        h: common_vendor.t(priceChangePercent.value >= 0 ? "+" : ""),
        i: common_vendor.t(priceChangePercent.value.toFixed(2)),
        j: common_vendor.n(priceChangeClass.value),
        k: common_vendor.t(marketData.value.last_volume),
        l: common_vendor.t(formatPrice(marketData.value.ask_price_1)),
        m: common_vendor.o(($event) => onPriceClick("ask", parseFloat(marketData.value.ask_price_1))),
        n: common_vendor.t(marketData.value.ask_volume_1),
        o: common_vendor.t(formatPrice(marketData.value.bid_price_1)),
        p: common_vendor.o(($event) => onPriceClick("bid", parseFloat(marketData.value.bid_price_1))),
        q: common_vendor.t(marketData.value.bid_volume_1),
        r: common_vendor.t(formatPrice(parseFloat(marketData.value.limit_up))),
        s: common_vendor.o(($event) => onPriceClick("limit_up", parseFloat(marketData.value.limit_up))),
        t: common_vendor.t(formatPrice(parseFloat(marketData.value.limit_down))),
        v: common_vendor.o(($event) => onPriceClick("limit_down", parseFloat(marketData.value.limit_down)))
      } : {
        w: common_vendor.p({
          name: "chart-line",
          size: "24px",
          ["custom-class"]: "text-gray-300 mb-1"
        }),
        x: common_vendor.t(isSubscribed.value ? "没有获取到行情数据" : "正在获取行情数据...")
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-74e1d4f6"]]);
wx.createComponent(Component);
