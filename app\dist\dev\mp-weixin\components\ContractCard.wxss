/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.contract-card.data-v-e4947044 {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}
.contract-card.data-v-e4947044::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}
.contract-card.data-v-e4947044:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.contract-card.data-v-e4947044:active {
  transform: translateY(0);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.contract-header.data-v-e4947044 {
  margin-bottom: 20rpx;
}
.contract-header .header-main.data-v-e4947044 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12rpx;
}
.contract-header .header-main .contract-code.data-v-e4947044 {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.2;
  flex-shrink: 0;
}
.contract-header .header-main .header-tags.data-v-e4947044 {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
}
.contract-info.data-v-e4947044 {
  margin-bottom: 20rpx;
}
.contract-info .user-contract-info.data-v-e4947044 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 16rpx;
}
.contract-info .user-contract-info .user-name.data-v-e4947044 {
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 600;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.contract-info .user-contract-info .instrument-name.data-v-e4947044 {
  font-size: 24rpx;
  color: #666;
  flex-shrink: 0;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.contract-info .user-contract-info .create-time.data-v-e4947044 {
  font-size: 24rpx;
  color: #999;
  flex-shrink: 0;
}
.contract-info .price-quantity-section.data-v-e4947044 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.contract-info .price-quantity-section .price-section.data-v-e4947044 {
  flex: 1;
}
.contract-info .price-quantity-section .price-section .price-value.data-v-e4947044 {
  font-size: 32rpx;
  color: #e6a23c;
  font-weight: 700;
  background: linear-gradient(135deg, rgba(230, 162, 60, 0.1) 0%, rgba(230, 162, 60, 0.05) 100%);
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  display: block;
  box-shadow: 0 2rpx 6rpx rgba(230, 162, 60, 0.15);
  text-align: center;
}
.contract-info .price-quantity-section .quantity-section.data-v-e4947044 {
  flex: 1;
}
.contract-info .price-quantity-section .quantity-section .quantity-row.data-v-e4947044 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 8rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}
.contract-info .price-quantity-section .quantity-section .quantity-row .quantity-item.data-v-e4947044 {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 6rpx 2rpx;
  border-radius: 4rpx;
  transition: all 0.2s ease;
}
.contract-info .price-quantity-section .quantity-section .quantity-row .quantity-item.data-v-e4947044:hover {
  background: rgba(0, 0, 0, 0.02);
}
.contract-info .price-quantity-section .quantity-section .quantity-row .quantity-item .quantity-number.data-v-e4947044 {
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 700;
  margin-bottom: 2rpx;
  line-height: 1.2;
}
.contract-info .price-quantity-section .quantity-section .quantity-row .quantity-item .quantity-number.warning.data-v-e4947044 {
  color: #f56c6c;
}
.contract-info .price-quantity-section .quantity-section .quantity-row .quantity-item .quantity-label.data-v-e4947044 {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
  line-height: 1.2;
}
.contract-info .price-quantity-section .quantity-section .quantity-row .quantity-item .quantity-label.warning.data-v-e4947044 {
  color: #f56c6c;
}
.contract-info .remarks-section.data-v-e4947044 {
  margin-top: 8rpx;
  padding: 8rpx 12rpx;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 6rpx;
  border-left: 3rpx solid #667eea;
}
.contract-info .remarks-section .remarks-text.data-v-e4947044 {
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
  font-style: italic;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.data-v-e4947044 .contract-actions {
  display: flex;
  gap: 12rpx;
  justify-content: flex-end;
  margin-top: 20rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}
.data-v-e4947044 .contract-actions .wd-button {
  border-radius: 8rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}
.data-v-e4947044 .contract-actions .wd-button:hover {
  transform: translateY(-1rpx);
}
.data-v-e4947044 .contract-actions .wd-button:active {
  transform: translateY(0);
}