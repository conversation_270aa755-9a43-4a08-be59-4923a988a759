"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_contract = require("../../api/contract.js");
const types_instrument = require("../../types/instrument.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_tag2 = common_vendor.resolveComponent("wd-tag");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_button2 + _easycom_wd_loading2 + _easycom_wd_tag2 + _component_layout_default_uni)();
}
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_tag = () => "../../node-modules/wot-design-uni/components/wd-tag/wd-tag.js";
if (!Math) {
  (_easycom_wd_button + _easycom_wd_loading + _easycom_wd_tag + CancelContractDialog)();
}
const CancelContractDialog = () => "../../components/CancelContractDialog.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "detail",
  setup(__props) {
    const router = common_vendor.useRouter();
    const route = common_vendor.useRoute();
    const contractDetail = common_vendor.ref(null);
    const loading = common_vendor.ref(false);
    const contractId = common_vendor.ref(0);
    const userRole = common_vendor.ref("setter");
    const showCancelDialog = common_vendor.ref(false);
    const isSetter = common_vendor.computed(() => {
      if (!contractDetail.value)
        return false;
      return true;
    });
    const hasCancelRecords = common_vendor.computed(() => {
      if (!contractDetail.value)
        return false;
      return contractDetail.value.status === "Cancelled" || contractDetail.value.remainingQuantity < contractDetail.value.totalQuantity;
    });
    function loadContractDetail() {
      if (!contractId.value) {
        common_vendor.index.showToast({
          title: "合同ID不能为空",
          icon: "error"
        });
        return;
      }
      loading.value = true;
      api_contract.getContractDetail(contractId.value).then((response) => {
        if (response.code === 0) {
          contractDetail.value = response.data;
          console.log("合同详情:", contractDetail.value);
        } else {
          common_vendor.index.showToast({
            title: response.msg || "获取合同详情失败",
            icon: "error"
          });
        }
      }).catch((error) => {
        console.error("获取合同详情失败:", error);
        common_vendor.index.showToast({
          title: "网络错误",
          icon: "error"
        });
      }).finally(() => {
        loading.value = false;
      });
    }
    function getContractTypeText(priceType) {
      const typeMap = {
        basis: "基差合同（点价）",
        fixed: "固定价合同（洗基差）"
      };
      return typeMap[priceType] || priceType;
    }
    function getStatusType(status) {
      const statusMap = {
        Unexecuted: "warning",
        Executing: "success",
        Pending: "warning",
        Completed: "primary",
        Cancelled: "danger"
      };
      return statusMap[status] || "warning";
    }
    function getStatusText(status) {
      const statusMap = {
        Unexecuted: "未执行",
        Executing: "执行中",
        Pending: "待处理",
        Completed: "已完成",
        Cancelled: "已取消"
      };
      return statusMap[status] || "未知";
    }
    function formatDateTime(dateStr) {
      if (!dateStr)
        return "未设置";
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    }
    function getExchangeName(exchangeId) {
      return types_instrument.ExchangeMap[exchangeId] || exchangeId;
    }
    function activateContract() {
      return __async(this, null, function* () {
        if (!contractId.value)
          return;
        try {
          const response = yield api_contract.activateContract(contractId.value);
          if (response.code === 0) {
            common_vendor.index.showToast({
              title: "激活成功",
              icon: "success"
            });
            loadContractDetail();
          } else {
            common_vendor.index.showToast({
              title: response.msg || "激活失败",
              icon: "error"
            });
          }
        } catch (error) {
          console.error("激活合同失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        }
      });
    }
    function deactivateContract() {
      return __async(this, null, function* () {
        if (!contractId.value)
          return;
        try {
          const response = yield api_contract.deactivateContract(contractId.value);
          if (response.code === 0) {
            common_vendor.index.showToast({
              title: "挂起成功",
              icon: "success"
            });
            loadContractDetail();
          } else {
            common_vendor.index.showToast({
              title: response.msg || "挂起失败",
              icon: "error"
            });
          }
        } catch (error) {
          console.error("挂起合同失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        }
      });
    }
    function editContract() {
      router.push(`/pages/contract/form?id=${contractId.value}&mode=edit`);
    }
    function cancelContract() {
      showCancelDialog.value = true;
    }
    function handleCancelConfirm(data) {
      return __async(this, null, function* () {
        if (!contractDetail.value)
          return;
        try {
          const cancelData = {
            cancelQuantity: data.cancelQuantity,
            reason: data.reason
          };
          const response = yield api_contract.cancelContract(contractDetail.value.ID, cancelData);
          if (response.code === 0) {
            common_vendor.index.showToast({
              title: "取消成功",
              icon: "success"
            });
            showCancelDialog.value = false;
            loadContractDetail();
          } else {
            common_vendor.index.showToast({
              title: response.msg || "取消失败",
              icon: "error"
            });
          }
        } catch (error) {
          console.error("取消合同失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        }
      });
    }
    function viewCancelRecords() {
      router.push(`/pages/contract/cancel-records?contractId=${contractId.value}`);
    }
    common_vendor.onMounted(() => {
      contractId.value = Number(route.query.id);
      userRole.value = route.query.role || "setter";
      if (contractId.value) {
        loadContractDetail();
      } else {
        common_vendor.index.showToast({
          title: "合同ID不能为空",
          icon: "error"
        });
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: contractDetail.value && isSetter.value
      }, contractDetail.value && isSetter.value ? common_vendor.e({
        b: contractDetail.value.status === "Unexecuted"
      }, contractDetail.value.status === "Unexecuted" ? {
        c: common_vendor.o(activateContract),
        d: common_vendor.p({
          type: "success",
          size: "small"
        })
      } : {}, {
        e: contractDetail.value.status === "Executing" && contractDetail.value.frozenQuantity === 0
      }, contractDetail.value.status === "Executing" && contractDetail.value.frozenQuantity === 0 ? {
        f: common_vendor.o(deactivateContract),
        g: common_vendor.p({
          type: "warning",
          size: "small"
        })
      } : {}, {
        h: contractDetail.value.status === "Unexecuted"
      }, contractDetail.value.status === "Unexecuted" ? {
        i: common_vendor.o(cancelContract),
        j: common_vendor.p({
          type: "error",
          size: "small"
        })
      } : {}, {
        k: contractDetail.value.status === "Unexecuted"
      }, contractDetail.value.status === "Unexecuted" ? {
        l: common_vendor.o(editContract),
        m: common_vendor.p({
          type: "primary",
          size: "small"
        })
      } : {}) : {}, {
        n: contractDetail.value && hasCancelRecords.value
      }, contractDetail.value && hasCancelRecords.value ? {
        o: common_vendor.o(viewCancelRecords),
        p: common_vendor.p({
          type: "info",
          size: "small"
        })
      } : {}, {
        q: loading.value
      }, loading.value ? {} : contractDetail.value ? common_vendor.e({
        s: common_vendor.t(contractDetail.value.contractCode),
        t: common_vendor.t(contractDetail.value.instrument ? `${contractDetail.value.instrument.instrument_name} (${contractDetail.value.instrument.instrument_id})` : contractDetail.value.instrumentRefID),
        v: contractDetail.value.instrument
      }, contractDetail.value.instrument ? {
        w: common_vendor.t(getExchangeName(contractDetail.value.instrument.exchange_id))
      } : {}, {
        x: contractDetail.value.instrument
      }, contractDetail.value.instrument ? {
        y: common_vendor.t(contractDetail.value.instrument.product_name)
      } : {}, {
        z: common_vendor.t(getStatusText(contractDetail.value.status)),
        A: common_vendor.p({
          type: getStatusType(contractDetail.value.status)
        }),
        B: common_vendor.t(formatDateTime(contractDetail.value.CreatedAt)),
        C: common_vendor.t(formatDateTime(contractDetail.value.UpdatedAt)),
        D: common_vendor.t(contractDetail.value.remarks || "无"),
        E: common_vendor.t(getContractTypeText(contractDetail.value.priceType)),
        F: common_vendor.t(contractDetail.value.totalQuantity),
        G: common_vendor.t(contractDetail.value.remainingQuantity),
        H: contractDetail.value.frozenQuantity > 0
      }, contractDetail.value.frozenQuantity > 0 ? {
        I: common_vendor.t(contractDetail.value.frozenQuantity)
      } : {}, {
        J: contractDetail.value.frozenQuantity > 0
      }, contractDetail.value.frozenQuantity > 0 ? {
        K: common_vendor.t(contractDetail.value.remainingQuantity - contractDetail.value.frozenQuantity)
      } : {}, {
        L: common_vendor.t(contractDetail.value.priceValue),
        M: common_vendor.t(((contractDetail.value.totalQuantity - contractDetail.value.remainingQuantity) / contractDetail.value.totalQuantity * 100).toFixed(1)),
        N: contractDetail.value.pricer
      }, contractDetail.value.pricer ? {
        O: common_vendor.t(contractDetail.value.pricer.nickName || contractDetail.value.pricer.userName),
        P: common_vendor.t(contractDetail.value.pricer.phone)
      } : {}, {
        Q: contractDetail.value.setter
      }, contractDetail.value.setter ? {
        R: common_vendor.t(contractDetail.value.setter.nickName || contractDetail.value.setter.userName),
        S: common_vendor.t(contractDetail.value.setter.phone)
      } : {}, {
        T: contractDetail.value.executionDetails && contractDetail.value.executionDetails.length > 0
      }, contractDetail.value.executionDetails && contractDetail.value.executionDetails.length > 0 ? {
        U: common_vendor.f(contractDetail.value.executionDetails, (execution, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(execution.ID),
            b: common_vendor.t(execution.status === "Success" ? "成功" : "失败"),
            c: "790f05c0-8-" + i0 + ",790f05c0-0",
            d: common_vendor.p({
              type: execution.status === "Success" ? "success" : "danger"
            }),
            e: common_vendor.t(execution.executedQuantity),
            f: common_vendor.t(execution.executedPrice),
            g: common_vendor.t(execution.contractPrice),
            h: common_vendor.t(execution.resultPrice),
            i: common_vendor.t(execution.executionType === "Online" ? "线上" : "线下"),
            j: execution.remarks
          }, execution.remarks ? {
            k: common_vendor.t(execution.remarks)
          } : {}, {
            l: execution.ID
          });
        })
      } : {}) : {
        V: common_vendor.o(loadContractDetail),
        W: common_vendor.p({
          type: "primary"
        })
      }, {
        r: contractDetail.value,
        X: common_vendor.o(handleCancelConfirm),
        Y: common_vendor.o(($event) => showCancelDialog.value = $event),
        Z: common_vendor.p({
          ["contract-data"]: contractDetail.value,
          modelValue: showCancelDialog.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-790f05c0"]]);
wx.createPage(MiniProgramPage);
