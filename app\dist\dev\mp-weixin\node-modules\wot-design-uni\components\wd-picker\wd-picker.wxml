<view class="{{['data-v-16e9a12c', I]}}" style="{{J}}"><view class="wd-picker__field data-v-16e9a12c" bindtap="{{p}}"><slot wx:if="{{a}}"></slot><view wx:else class="wd-picker__cell data-v-16e9a12c"><view wx:if="{{b}}" class="{{['data-v-16e9a12c', e]}}" style="{{f}}"><block wx:if="{{c}}">{{d}}</block><slot wx:else name="label"></slot></view><view class="wd-picker__body data-v-16e9a12c"><view class="wd-picker__value-wraper data-v-16e9a12c"><view class="{{['data-v-16e9a12c', h]}}">{{g}}</view><wd-icon wx:if="{{i}}" class="data-v-16e9a12c" u-i="16e9a12c-0" bind:__l="__l" u-p="{{j}}"/><view wx:elif="{{k}}" class="data-v-16e9a12c" catchtap="{{m}}"><wd-icon wx:if="{{l}}" class="data-v-16e9a12c" u-i="16e9a12c-1" bind:__l="__l" u-p="{{l}}"/></view></view><view wx:if="{{n}}" class="wd-picker__error-message data-v-16e9a12c">{{o}}</view></view></view></view><wd-popup wx:if="{{H}}" class="data-v-16e9a12c" u-s="{{['d']}}" bindclose="{{F}}" u-i="16e9a12c-2" bind:__l="__l" bindupdateModelValue="{{G}}" u-p="{{H}}"><view class="wd-picker__wraper data-v-16e9a12c"><view class="wd-picker__toolbar data-v-16e9a12c" bindtouchmove="{{y}}"><view class="wd-picker__action wd-picker__action--cancel data-v-16e9a12c" bindtap="{{r}}">{{q}}</view><view wx:if="{{s}}" class="wd-picker__title data-v-16e9a12c">{{t}}</view><view class="{{['data-v-16e9a12c', w]}}" bindtap="{{x}}">{{v}}</view></view><wd-picker-view wx:if="{{E}}" class="r data-v-16e9a12c" u-r="pickerViewWd" bindchange="{{A}}" bindpickstart="{{B}}" bindpickend="{{C}}" u-i="16e9a12c-3,16e9a12c-2" bind:__l="__l" bindupdateModelValue="{{D}}" u-p="{{E}}"/></view></wd-popup></view>