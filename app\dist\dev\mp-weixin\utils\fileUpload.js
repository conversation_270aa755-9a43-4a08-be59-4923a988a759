"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
class FileUploadUtil {
  /**
   * 上传文件到服务器
   * @param filePath 本地文件路径
   * @param token 认证token
   * @param options 上传选项
   */
  static uploadFile(_0, _1) {
    return __async(this, arguments, function* (filePath, token, options = {}) {
      const { classId = "0", onProgress } = options;
      const uploadUrl = `${"http://localhost:8888"}/fileUploadAndDownload/upload`;
      return new Promise((resolve, reject) => {
        const uploadTask = common_vendor.index.uploadFile({
          url: uploadUrl,
          filePath,
          name: "file",
          header: {
            "x-token": token,
            "Authorization": `Bearer ${token}`
          },
          formData: {
            classId: String(classId)
          },
          success: (res) => {
            try {
              const responseData = JSON.parse(res.data);
              if (responseData.code === 0 && responseData.data && responseData.data.file) {
                resolve(responseData.data.file);
              } else {
                reject(new Error(responseData.msg || "文件上传失败"));
              }
            } catch (parseError) {
              console.error("响应解析失败:", parseError, "原始响应:", res.data);
              reject(new Error("服务器响应格式错误"));
            }
          },
          fail: (error) => {
            console.error("文件上传请求失败:", error);
            reject(new Error(`上传请求失败: ${error.errMsg || "网络错误"}`));
          }
        });
        if (onProgress) {
          uploadTask.onProgressUpdate((res) => {
            onProgress(res.progress);
          });
        }
      });
    });
  }
  /**
   * 选择图片文件
   * @param options 选择选项
   */
  static chooseImage() {
    return __async(this, arguments, function* (options = {}) {
      const {
        count = 1,
        sizeType = ["original", "compressed"],
        sourceType = ["album", "camera"]
      } = options;
      return new Promise((resolve, reject) => {
        common_vendor.index.chooseImage({
          count,
          sizeType,
          sourceType,
          success: (res) => {
            if (res.tempFilePaths && res.tempFilePaths.length > 0) {
              resolve(res.tempFilePaths[0]);
            } else {
              reject(new Error("未选择任何图片"));
            }
          },
          fail: (err) => {
            console.error("选择图片失败:", err);
            reject(new Error("选择图片失败"));
          }
        });
      });
    });
  }
  /**
   * 验证图片文件
   * @param filePath 文件路径
   * @param maxSize 最大文件大小（字节）
   */
  static validateImage(_0) {
    return __async(this, arguments, function* (filePath, maxSize = 10 * 1024 * 1024) {
      return new Promise((resolve) => {
        common_vendor.index.getFileInfo({
          filePath,
          success: (res) => {
            if (res.size > maxSize) {
              common_vendor.index.showToast({
                title: `图片大小不能超过${Math.round(maxSize / 1024 / 1024)}MB`,
                icon: "none"
              });
              resolve(false);
            } else {
              resolve(true);
            }
          },
          fail: () => {
            resolve(true);
          }
        });
      });
    });
  }
}
exports.FileUploadUtil = FileUploadUtil;
