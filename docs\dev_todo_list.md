# 项目开发待办事项 (Generated TODO List)

本文件由AI根据项目文档自动生成，列出了已设计但尚未完成的开发任务。

---

## 01: 基差点价交易 (Basis Point Trading)

### 后端 (Backend - admin/server)

-   [x] **合同管理API**:
    -   [x] `GET /dianjia/contracts/as-setter`: 获取被点价方合同列表。
    -   [x] `GET /dianjia/contracts/as-pricer`: 获取点价方合同列表。
    -   [x] `GET /dianjia/contract/{id}`: 获取单个合同详情。
    -   [x] `POST /dianjia/contract`: 创建新合同 (初始状态 `Unexecuted`)。
    -   [x] `PUT /dianjia/contract/{id}`: 更新合同 (仅限 `Unexecuted` 状态)。
    -   [x] `DELETE /dianjia/contract/{id}`: 删除合同 (仅限 `Unexecuted` 状态)。
    -   [x] `POST /dianjia/contract/{id}/activate`: 激活合同 (状态变为 `Executing`)。
    -   [x] `POST /dianjia/contract/{id}/deactivate`: 挂起合同 (状态变为 `Unexecuted`, 仅当无冻结数量时)。
    -   [x] `POST /dianjia/contract/{id}/cancel`: 部分或全部取消合同。
-   [x] **交易请求API**:
    -   [x] `POST /dianjia/traderequests`: 创建交易请求 (点价/洗基差)。
    -   [x] `POST /dianjia/traderequests/{id}/cancel`: 取消交易请求 (由Pricer发起)。
    -   [x] `POST /dianjia/traderequests/{id}/reject`: 拒绝交易请求 (由Setter发起)。
    -   [x] `POST /dianjia/traderequests/{id}/feedback`: 人工回报交易结果。
    -   [x] `GET /dianjia/traderequests/{id}`: 查看交易请求详情。
    -   [x] `GET /dianjia/traderequests/as-pricer`: 获取点价方视角的交易列表。
    -   [x] `GET /dianjia/traderequests/as-setter`: 获取被点价方视角的交易列表。
-   [x] **核心逻辑**:
    -   [x] 实现合同 `contracts` 表的状态机逻辑 (Unexecuted, Executing, Completed, Cancelled)。
    -   [x] 实现交易请求 `trade_requests` 表的状态机逻辑 (V4 Simplified Flow)。
    -   [x] 实现系统自动匹配合同并冻结/解冻数量的逻辑。
    -   [x] 实现交易请求的部分成交与加权平均价计算逻辑。
    -   [x] 实现交易请求的后台过期处理定时任务。

### 后台管理 (Admin - admin/web)

-   [ ] **商品与合约管理**:
    -   [ ] 创建商品 (`commodities`) 管理页面 (CRUD)。
    -   [ ] 创建期货合约 (`instruments`) 管理页面 (CRUD)。

### 前端 (App - app)

-   [x] **核心组件**:
    -   [x] 实现或完善期货合约选择器组件 `InstrumentSelector.vue`。
    -   [x] 建立全局Instrument数据缓存方案 `store/instrument.ts`。
-   [x] **被点价方 (Setter) 页面**:
    -   [x] `pages/contract/setter-list.vue`: 合同管理页面，包含激活、挂起、取消等操作。
    -   [ ] `pages/trade/review-list.vue`: 交易审核页面。
-   [x] **点价方 (Pricer) 页面**:
    -   [x] `pages/contract/pricer-list.vue`: 合同中心页面 (信息展示)。
    -   [x] `pages/trade/execute.vue`: 统一的交易执行页面 (发起点价/洗基差)。

---

## 02: 基础数据看板 (Basic Data Dashboard)

### 后端 (Backend - admin/server)

-   [ ] **看板API**:
    -   [ ] `GET /api/v1/dashboard/term-structure`: 实现期限结构数据查询接口。
    -   [ ] `GET /api/v1/dashboard/historical-comparison`: 实现历史叠加对比数据查询接口。

### 前端 (App - app)

-   [ ] **核心页面与组件**:
    -   [ ] `pages/dashboard/index.vue`: 实现通用行情看板主页面。
    -   [ ] `components/charts/TermStructureChart.vue`: 实现期限结构图表组件。
    -   [ ] `components/charts/HistoricalComparisonChart.vue`: 实现历史叠加对比图表组件。
-   [ ] **报表页面**:
    -   [ ] `pages/reports/setter-view.vue`: 实现卖方报表页面。
    -   [ ] `pages/reports/pricer-view.vue`: 实现买方报表页面。

---

## 04: 本地交易下单端 (Order Client)

-   [x] **立项**: 开发一个独立的、基于Python (`PySide6`, `vnpy`) 的桌面应用。
-   [x] **核心功能 - 登录**:
    -   [x] 实现登录窗口UI (`LoginWindow`, `LoginWidget`)。
    -   [x] 实现登录管理器 `LoginManager`，支持用户名/密码和手机/验证码两种方式。
    -   [x] 实现安全的 `TokenManager`，用于加密存储和管理Token。
    -   [x] 实现 `AuthService` 对接后端的HTTP登录接口。
-   [ ] **核心功能 - WebSocket通信**:
    -   [x] 实现 `WebSocketClient`，负责连接管理、心跳和自动重连。
    -   [ ] 实现与 `LoginManager` 的集成，登录后自动发起WebSocket连接与认证。
    -   [ ] 实现消息处理器框架，处理来自服务端的各类消息（订单、心跳、冲突等）。
    -   [ ] 实现连接冲突处理与强制下线功能。
-   [ ] **核心功能 - 交易执行**:
    -   [ ] 实现对服务端下发的订单指令的接收、ACK确认和幂等性处理。
    -   [ ] 集成 `vnpy` 的交易网关 (TTS/CTP)。
    -   [ ] 将交易执行结果通过WebSocket反馈给服务器。
-   [ ] **核心功能 - 数据同步**:
    -   [ ] 实现账户资金和持仓数据的定时上报。
-   [x] **UI实现**:
    -   [x] 实现主窗口UI，包括账户管理、交易面板、订单、持仓、日志等区域。
    -   [x] 实现各类状态的实时显示与更新。

---

## 09: 交易执行风险组件 (Trade Execution Risk Component)

### 后端 (Backend - admin/server)

-   [ ] **数据库**:
    -   [ ] 创建 `dianjia_risk_configs` 表。
    -   [ ] 扩展 `trades` 表，增加 `executed_by`, `executor_id`, `feedback_notes` 字段。
-   [ ] **核心逻辑**:
    -   [ ] 实现交易审核通过后，按“合同->点价方->全局”优先级查找风险配置的逻辑。
    -   [ ] 根据查找到的 `execution_mode` (AUTOMATIC, MANUAL, SIMULATED) 更新交易状态。
    -   [ ] 扩展 `trades` 状态机，增加 `PendingManualFill` 等新状态。
-   [ ] **风险配置API**:
    -   [ ] 实现 `/api/v1/risk-configs` 的CRUD接口。

### 后台管理 (Admin - admin/web)

-   [ ] **管理页面**:
    -   [ ] 创建 `admin/web/src/views/risk-management/configs.vue` 页面，用于管理风险配置规则。

---

## 10: 公开报价市场 (Public Quotation Marketplace)

### 后端 (Backend - admin/server)

-   [x] **数据库**: 创建 `quotations` 表。
-   [x] **核心逻辑**:
    -   [x] 实现报价 `quotations` 表的状态机逻辑 (Draft, Active, Expired, Withdrawn)。
    -   [x] 创建后台定时任务 (Cron Job)，用于处理报价的自动过期。
-   [x] **报价API**:
    -   [x] `POST / PUT /api/v1/quotations`: 创建/更新报价。
    -   [x] `POST /api/v1/quotations/{id}/publish`: 发布报价。
    -   [x] `GET /api/v1/my-quotations`: 获取我的报价列表。
    -   [x] `POST /api/v1/quotations/{id}/withdraw`: 撤回报价。
    -   [x] `DELETE /api/v1/quotations/{id}`: 删除报价草稿。
    -   [x] `GET /api/v1/quotations`: 获取公开市场报价列表（支持筛选和搜索）。
    -   [x] `GET /api/v1/quotations/{id}`: 获取报价详情。

### 前端 (App - app)

-   [x] **报价方页面**:
    -   [x] `pages/quotes/edit.vue`: 创建/编辑报价的表单页面。
    -   [x] `pages/quotes/my-list.vue`: 我的报价管理列表页面。
-   [x] **公开市场页面**:
    -   [x] `pages/quotes/marketplace.vue`: 公开报价市场列表页，支持分类和搜索。
    -   [x] `pages/quotes/{id}.vue`: 自适应的报价详情页（区分公众视角和发布者视角）。
-   [ ] **流程打通**:
    -   [ ] 将报价详情页的“发起点价”按钮与 **功能01** 的创建交易请求流程连接起来。

---

## 11: UI & 核心组件 (UI & Core Components)

### 前端 (App - app)
-   [ ] **统一Tabbar**:
    -   [ ] 设计并实现一个全局统一的底部Tabbar组件，用于核心页面（如工作台、报价市场、我的）的快速跳转。
    -   [ ] 确保在H5、小程序、App等多端表现一致。

---

## 12: 模拟成交插件 (Simulated Matching Engine)

### 后端 (Backend - admin/server)
-   [ ] **核心逻辑**:
    -   [ ] 创建一个独立的模拟成交服务或插件。
    -   [ ] 该服务需要订阅Redis中的实时行情数据 (`tick:*`)。
    -   [ ] 监听所有 `execution_mode` 为 `SIMULATED` 的交易请求。
    -   [ ] 根据实时行情价格，自动撮合市价单和限价单的成交。
-   [ ] **结果反馈**:
    -   [ ] 模拟成交后，调用 `trade_request` 服务的 `ManualFeedback` 接口（或创建一个新的内部接口）回报成交结果。
    -   [ ] 确保成交逻辑（如部分成交、全部成交）与真实交易流程一致。

---

## 13: 合作伙伴管理 (Partnership Management)

### 后端 (Backend - admin/server)
-   [ ] **数据库**:
    -   [ ] 设计并创建 `dianjia_partners` 表，用于存储用户间的伙伴关系（`user_id`, `partner_id`, `status`, `request_message`）。
    -   [ ] 状态机: `pending`, `active`, `rejected`, `terminated`。
-   [ ] **API - 邀请与状态管理**:
    -   [ ] `POST /api/v1/partners/invite`: 发送合作伙伴邀请。
    -   [ ] `POST /api/v1/partners/invitations/{id}/accept`: 接受邀请。
    -   [ ] `POST /api/v1/partners/invitations/{id}/reject`: 拒绝邀请。
    -   [ ] `DELETE /api/v1/partners/{id}`: 解除合作伙伴关系。
-   [ ] **API - 数据查询**:
    -   [ ] `GET /api/v1/partners`: 获取当前用户的合作伙伴列表。
    -   [ ] `GET /api/v1/partners/invitations`: 获取待处理的邀请（收到的和发出的）。
-   [ ] **核心逻辑 - 权限**:
    -   [ ] **重要**: 修改用户选择器API (`/api/v1/user/getSelectableList`)，使其在特定上下文中（如创建合同）只返回当前用户的**激活状态（active）**合作伙伴。

### 前端 (App - app)
-   [ ] **页面**:
    -   [ ] 创建合作伙伴管理页面 (`pages/profile/partners.vue`)。
-   [ ] **UI功能**:
    -   [ ] 显示当前的合作伙伴列表。
    -   [ ] 显示收到和发出的邀请列表。
    -   [ ] 提供接受/拒绝邀请、解除伙伴关系、发起新邀请的功能。
-   [ ] **组件修改**:
    -   [ ] 确保 `UserSelector.vue` 组件能正确调用被修改后的API，并展示正确的合作伙伴列表。

---

## 14: 用户账户设置 (User Account Settings)

### 后端 (Backend - admin/server)
-   [x] **API**:
    -   [x] `POST /user/changePassword`: 修改密码接口已实现（需要验证旧密码）。
    -   [x] 确认 `/user/updateProfile` 和 `/user/getProfile` 接口满足前端“设置资料”页面的需求。
    -   [x] **API字段适配**: 修复前后端字段名不匹配问题（`oldPassword` -> `password`）。

### 前端 (App - app)
-   [ ] **页面**:
    -   [ ] 创建“我的”或“账户中心”主页面 (`pages/profile/index.vue`)。
    -   [ ] 创建“修改密码”页面 (`pages/profile/change-password.vue`)。
-   [ ] **功能**:
    -   [ ] 在“账户中心”页面展示和编辑用户基本资料（昵称、头像、企业信息等）。
    -   [ ] 提供入口跳转到修改密码页面。

---

## 15: 应用支持与设置 (App Support & Settings)

### 后端 (Backend - admin/server)
-   [ ] **意见反馈API**:
    -   [ ] `POST /api/v1/feedback`: 创建意见反馈接口，存储用户提交的反馈内容。
-   [ ] **静态内容管理**:
    -   [ ] 在后台管理系统中，提供一个简单的文本编辑器，用于管理“帮助中心”、“关于我们”、“隐私政策”和“服务条款”的静态内容。

### 前端 (App - app)
-   [x] **功能实现**:
    -   [x] **清理缓存**: 在“账户中心”页面增加“清理缓存”按钮，点击后清除 `uni.storage` 中的非关键数据（如API缓存），并提示用户“清理成功”。
-   [x] **页面创建**:
    -   [x] **帮助中心**: 创建一个静态页面 (`pages/support/help-center.vue`)，用于展示后台配置的帮助文档。
    -   [x] **意见反馈**: 创建一个表单页面 (`pages/support/feedback.vue`)，包含文本输入框和提交按钮。
    -   [x] **关于我们**: 创建一个静态页面 (`pages/support/about.vue`)，展示应用版本号、公司信息等。
    -   [x] **隐私与服务**: 创建一个通用的静态内容展示页面 (`pages/support/policy.vue`)，用于根据路由参数显示“隐私政策”或“服务条款”。
-   [x] **导航集成**:
    -   [x] 在“账户中心”页面添加入口，分别链接到以上各个新页面。


---

## 更新记录 (Update Log)
### 2025-01-01: 应用支持与设置功能完成
- ✅ **通用内容展示器**: 创建了通用的markdown内容展示页面 (`content-viewer.vue`)
  - 支持隐私政策、服务条款、帮助中心等多种内容类型
  - 集成 `zero-markdown-view` 组件实现专业的markdown渲染
  - 实现了加载状态、错误处理和重试机制
  - 优化了页面布局和响应式设计