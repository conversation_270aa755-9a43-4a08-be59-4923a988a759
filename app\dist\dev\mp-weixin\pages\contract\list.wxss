/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-header.data-v-ec30f093 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
}
.page-header .page-title.data-v-ec30f093 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.role-selection.data-v-ec30f093 {
  margin-bottom: 60rpx;
}
.role-selection .role-card.data-v-ec30f093 {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}
.role-selection .role-card.data-v-ec30f093:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}
.role-selection .role-card.setter.data-v-ec30f093 {
  border-left: 8rpx solid #667eea;
}
.role-selection .role-card.pricer.data-v-ec30f093 {
  border-left: 8rpx solid #764ba2;
}
.role-selection .role-card .role-icon.data-v-ec30f093 {
  margin-right: 30rpx;
}
.role-selection .role-card .role-icon .icon.data-v-ec30f093 {
  font-size: 48rpx;
}
.role-selection .role-card .role-info.data-v-ec30f093 {
  flex: 1;
}
.role-selection .role-card .role-info .role-name.data-v-ec30f093 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.role-selection .role-card .role-info .role-desc.data-v-ec30f093 {
  font-size: 26rpx;
  color: #666;
}
.role-selection .role-card .role-arrow .arrow.data-v-ec30f093 {
  font-size: 40rpx;
  color: #ccc;
}
.quick-actions .section-title.data-v-ec30f093 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.quick-actions .action-list.data-v-ec30f093 {
  display: flex;
  justify-content: space-around;
}
.quick-actions .action-list .action-item.data-v-ec30f093 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  min-width: 160rpx;
  transition: all 0.3s ease;
}
.quick-actions .action-list .action-item.data-v-ec30f093:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}
.quick-actions .action-list .action-item .action-icon.data-v-ec30f093 {
  margin-bottom: 16rpx;
}
.quick-actions .action-list .action-item .action-icon .icon.data-v-ec30f093 {
  font-size: 40rpx;
}
.quick-actions .action-list .action-item .action-name.data-v-ec30f093 {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}