/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
.wot-theme-dark .wd-message-box__body.data-v-6a12ec76 {
  background-color: var(--wot-dark-background2, #1b1b1b);
}
.wot-theme-dark .wd-message-box__title.data-v-6a12ec76 {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-message-box__content.data-v-6a12ec76 {
  color: var(--wot-dark-color3, rgba(232, 230, 227, 0.8));
}
.wot-theme-dark .wd-message-box__content.data-v-6a12ec76::-webkit-scrollbar-thumb {
  background: var(--wot-dark-border-color, #3a3a3c);
}
.data-v-6a12ec76 .wd-message-box {
  border-radius: var(--wot-message-box-radius, 16px);
  overflow: hidden;
}
.wd-message-box.data-v-6a12ec76 {
  border-radius: var(--wot-message-box-radius, 16px);
  overflow: hidden;
}
.wd-message-box__container.data-v-6a12ec76 {
  width: var(--wot-message-box-width, 300px);
  box-sizing: border-box;
}
.wd-message-box__body.data-v-6a12ec76 {
  background-color: var(--wot-message-box-bg, var(--wot-color-white, rgb(255, 255, 255)));
  padding: var(--wot-message-box-padding, 25px 24px 0);
}
.wd-message-box__body.is-no-title.data-v-6a12ec76 {
  padding: 25px 24px 0px;
}
.wd-message-box__title.data-v-6a12ec76 {
  text-align: center;
  font-size: var(--wot-message-box-title-fs, 16px);
  color: var(--wot-message-box-title-color, rgba(0, 0, 0, 0.85));
  line-height: 20px;
  font-weight: 500;
  padding-top: 5px;
  padding-bottom: 10px;
}
.wd-message-box__content.data-v-6a12ec76 {
  max-height: var(--wot-message-box-content-max-height, 264px);
  color: var(--wot-message-box-content-color, #666666);
  font-size: var(--wot-message-box-content-fs, 14px);
  text-align: center;
  overflow: auto;
  line-height: 20px;
}
.wd-message-box__content.data-v-6a12ec76::-webkit-scrollbar {
  width: var(--wot-message-box-content-scrollbar-width, 4px);
}
.wd-message-box__content.data-v-6a12ec76::-webkit-scrollbar-thumb {
  width: var(--wot-message-box-content-scrollbar-width, 4px);
  background: var(--wot-message-box-content-scrollbar-color, rgba(0, 0, 0, 0.1));
  border-radius: calc(var(--wot-message-box-content-scrollbar-width, 4px) / 2);
}
.wd-message-box__input-error.data-v-6a12ec76 {
  min-height: 18px;
  margin-top: 2px;
  color: var(--wot-message-box-input-error-color, var(--wot-input-error-color, var(--wot-color-danger, #fa4350)));
  text-align: left;
}
.wd-message-box__input-error.is-hidden.data-v-6a12ec76 {
  visibility: hidden;
}
.wd-message-box__actions.data-v-6a12ec76 {
  padding: 24px;
}
.data-v-6a12ec76  .wd-message-box__actions-btn:not(:last-child) {
  margin-right: 16px;
}
.wd-message-box__flex.data-v-6a12ec76 {
  display: flex;
}
.wd-message-box__block.data-v-6a12ec76 {
  display: block;
}
.wd-message-box__cancel.data-v-6a12ec76 {
  margin-right: 16px;
}