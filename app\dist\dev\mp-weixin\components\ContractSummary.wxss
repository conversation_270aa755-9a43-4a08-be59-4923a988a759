/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.contract-summary .summary-scroll.data-v-cf10ea1b {
  height: calc(100vh - 200rpx);
  padding: 20rpx 0;
}
.contract-summary .scroll-content.data-v-cf10ea1b {
  padding: 0 20rpx;
}
.contract-summary .summary-stats.data-v-cf10ea1b {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.contract-summary .summary-stats .stats-card.data-v-cf10ea1b {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}
.contract-summary .summary-stats .stats-card .stats-title.data-v-cf10ea1b {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}
.contract-summary .summary-stats .stats-card .stats-value.data-v-cf10ea1b {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: white;
}
.contract-summary .user-card.data-v-cf10ea1b {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.contract-summary .user-card .user-header.data-v-cf10ea1b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}
.contract-summary .user-card .user-header.data-v-cf10ea1b:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}
.contract-summary .user-card .user-header .user-main-info.data-v-cf10ea1b {
  flex: 1;
}
.contract-summary .user-card .user-header .user-main-info .user-name.data-v-cf10ea1b {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  display: block;
}
.contract-summary .user-card .user-header .user-main-info .user-stats.data-v-cf10ea1b {
  display: flex;
  gap: 20rpx;
}
.contract-summary .user-card .user-header .user-main-info .user-stats .stat-item.data-v-cf10ea1b {
  font-size: 24rpx;
  color: #666;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.contract-summary .user-card .user-header .expand-icon.data-v-cf10ea1b {
  transition: transform 0.3s ease;
}
.contract-summary .user-card .user-header .expand-icon.expanded.data-v-cf10ea1b {
  transform: rotate(180deg);
}
.contract-summary .user-card .user-header .expand-icon .icon.data-v-cf10ea1b {
  font-size: 24rpx;
  color: #666;
}
.contract-summary .user-card .user-details.data-v-cf10ea1b {
  padding: 20rpx 30rpx 30rpx;
  background: #fafafa;
}
.contract-summary .user-card .user-details .instrument-card.data-v-cf10ea1b {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}
.contract-summary .user-card .user-details .instrument-card.clickable.data-v-cf10ea1b {
  cursor: pointer;
}
.contract-summary .user-card .user-details .instrument-card.clickable.data-v-cf10ea1b:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}
.contract-summary .user-card .user-details .instrument-card.clickable.data-v-cf10ea1b:active {
  transform: translateY(0);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.contract-summary .user-card .user-details .instrument-card.data-v-cf10ea1b:last-child {
  margin-bottom: 0;
}
.contract-summary .user-card .user-details .instrument-card .instrument-header.data-v-cf10ea1b {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.contract-summary .user-card .user-details .instrument-card .instrument-header .instrument-title-row.data-v-cf10ea1b {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.contract-summary .user-card .user-details .instrument-card .instrument-header .instrument-title-row .instrument-name.data-v-cf10ea1b {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}
.contract-summary .user-card .user-details .instrument-card .instrument-header .instrument-title-row .summary-text.data-v-cf10ea1b {
  font-size: 24rpx;
  color: #666;
  flex-shrink: 0;
}
.contract-summary .user-card .user-details .instrument-card .contract-types.data-v-cf10ea1b {
  padding: 0;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card.data-v-cf10ea1b {
  border-radius: 0;
  padding: 16rpx 24rpx;
  margin-bottom: 0;
  border: none;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card.data-v-cf10ea1b:last-child {
  border-bottom: none;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card.basis-card.data-v-cf10ea1b {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(102, 126, 234, 0.02) 100%);
  border-color: rgba(102, 126, 234, 0.1);
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card.fixed-card.data-v-cf10ea1b {
  background: linear-gradient(135deg, rgba(230, 162, 60, 0.05) 0%, rgba(230, 162, 60, 0.02) 100%);
  border-color: rgba(230, 162, 60, 0.1);
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card .type-header.data-v-cf10ea1b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card .type-header .type-name.data-v-cf10ea1b {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card .type-header .contract-count.data-v-cf10ea1b {
  font-size: 22rpx;
  color: #666;
  background: rgba(0, 0, 0, 0.05);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card .type-stats.data-v-cf10ea1b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12rpx;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card .type-stats .stat-group.data-v-cf10ea1b {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card .type-stats .stat-group .stat-label.data-v-cf10ea1b {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 4rpx;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card .type-stats .stat-group .stat-value.data-v-cf10ea1b {
  font-size: 24rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card .type-stats .stat-group .stat-value.warning.data-v-cf10ea1b {
  color: #f56c6c;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card .type-stats .stat-group .stat-value.available.data-v-cf10ea1b {
  color: #67c23a;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .contract-type-card .type-stats .stat-group .stat-value.price.data-v-cf10ea1b {
  color: #e6a23c;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .no-contracts.data-v-cf10ea1b {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #999;
}
.contract-summary .user-card .user-details .instrument-card .contract-types .no-contracts .no-contracts-text.data-v-cf10ea1b {
  font-size: 26rpx;
}