"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const isBase64 = (path) => /^data:image\/(\w+);base64/.test(path);
function sleep(delay) {
  return new Promise((resolve) => setTimeout(resolve, delay));
}
let { platform, SDKVersion } = common_vendor.index.getSystemInfoSync();
const isPC = /windows|mac/.test(platform);
let cache = {};
function isNumber(value) {
  return /^-?\d+(\.\d+)?$/.test(value);
}
function toPx(value, baseSize, isDecimal = false) {
  if (typeof value === "number") {
    return value;
  }
  if (isNumber(value)) {
    return value * 1;
  }
  if (typeof value === "string") {
    const reg = /^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|px|%)$/g;
    const results = reg.exec(value);
    if (!value || !results) {
      return 0;
    }
    const unit = results[3];
    value = parseFloat(value);
    let res = 0;
    if (unit === "rpx") {
      res = common_vendor.index.upx2px(value);
    } else if (unit === "px") {
      res = value * 1;
    } else if (unit === "%") {
      res = value * toPx(baseSize) / 100;
    } else if (unit === "em") {
      res = value * toPx(14);
    }
    return isDecimal ? res.toFixed(2) * 1 : Math.round(res);
  }
  return 0;
}
function compareVersion(v1, v2) {
  v1 = v1.split(".");
  v2 = v2.split(".");
  const len = Math.max(v1.length, v2.length);
  while (v1.length < len) {
    v1.push("0");
  }
  while (v2.length < len) {
    v2.push("0");
  }
  for (let i = 0; i < len; i++) {
    const num1 = parseInt(v1[i], 10);
    const num2 = parseInt(v2[i], 10);
    if (num1 > num2) {
      return 1;
    } else if (num1 < num2) {
      return -1;
    }
  }
  return 0;
}
function gte(version) {
  return compareVersion(SDKVersion, version) >= 0;
}
function canIUseCanvas2d() {
  return gte("2.9.2");
}
const prefix = () => {
  return common_vendor.wx$1;
};
function base64ToPath(base64) {
  const [, format] = /^data:image\/(\w+);base64,/.exec(base64) || [];
  return new Promise((resolve, reject) => {
    const fs = common_vendor.index.getFileSystemManager();
    if (!format) {
      reject(new Error("ERROR_BASE64SRC_PARSE"));
    }
    const time = (/* @__PURE__ */ new Date()).getTime();
    let pre = prefix();
    const filePath = `${pre.env.USER_DATA_PATH}/${time}.${format}`;
    fs.writeFile({
      filePath,
      data: base64.split(",")[1],
      encoding: "base64",
      success() {
        resolve(filePath);
      },
      fail(err) {
        console.error(err);
        reject(err);
      }
    });
  });
}
function pathToBase64(path) {
  if (/^data:/.test(path))
    return path;
  return new Promise((resolve, reject) => {
    if (common_vendor.index.canIUse("getFileSystemManager")) {
      common_vendor.index.getFileSystemManager().readFile({
        filePath: path,
        encoding: "base64",
        success: (res) => {
          resolve("data:image/png;base64," + res.data);
        },
        fail: (error) => {
          console.error({ error, path });
          reject(error);
        }
      });
    }
  });
}
function getImageInfo(path, useCORS) {
  const isCanvas2D = this && this.canvas && this.canvas.createImage;
  return new Promise((resolve, reject) => __async(this, null, function* () {
    let src = path.replace(/^@\//, "/");
    if (cache[path] && cache[path].errMsg) {
      resolve(cache[path]);
    } else {
      try {
        if (isBase64(path) && (isCanvas2D ? isPC : true)) {
          src = yield base64ToPath(path);
        }
      } catch (error) {
        reject(__spreadProps(__spreadValues({}, error), {
          src
        }));
      }
      if (isCanvas2D && !isPC) {
        const img = this.canvas.createImage();
        img.onload = function() {
          const image = {
            path: img,
            width: img.width,
            height: img.height
          };
          cache[path] = image;
          resolve(cache[path]);
        };
        img.onerror = function(err) {
          reject({ err, path });
        };
        img.src = src;
        return;
      }
      common_vendor.index.getImageInfo({
        src,
        success: (image) => {
          const localReg = /^\.|^\/(?=[^\/])/;
          image.path = localReg.test(src) ? `/${image.path}` : image.path;
          if (isCanvas2D) {
            const img = this.canvas.createImage();
            img.onload = function() {
              image.path = img;
              cache[path] = image;
              resolve(cache[path]);
            };
            img.onerror = function(err) {
              reject({ err, path });
            };
            img.src = src;
            return;
          }
          cache[path] = image;
          resolve(cache[path]);
        },
        fail(err) {
          console.error({ err, path });
          reject({ err, path });
        }
      });
    }
  }));
}
exports.base64ToPath = base64ToPath;
exports.canIUseCanvas2d = canIUseCanvas2d;
exports.getImageInfo = getImageInfo;
exports.isBase64 = isBase64;
exports.isPC = isPC;
exports.pathToBase64 = pathToBase64;
exports.sleep = sleep;
exports.toPx = toPx;
