"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_traderequest = require("../../api/traderequest.js");
const utils_toast = require("../../utils/toast.js");
if (!Array) {
  const _easycom_wd_tab2 = common_vendor.resolveComponent("wd-tab");
  const _easycom_wd_tabs2 = common_vendor.resolveComponent("wd-tabs");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_tab2 + _easycom_wd_tabs2 + _component_layout_default_uni)();
}
const _easycom_wd_tab = () => "../../node-modules/wot-design-uni/components/wd-tab/wd-tab.js";
const _easycom_wd_tabs = () => "../../node-modules/wot-design-uni/components/wd-tabs/wd-tabs.js";
if (!Math) {
  (_easycom_wd_tab + _easycom_wd_tabs + TradeRequestList)();
}
const TradeRequestList = () => "../../components/TradeRequestList.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "PricerManagementPage"
}), {
  __name: "pricer-management",
  setup(__props) {
    const tradeRequests = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const refreshing = common_vendor.ref(false);
    const loadingMore = common_vendor.ref(false);
    const statusFilter = common_vendor.ref("");
    const viewMode = common_vendor.ref("management");
    const page = common_vendor.ref(1);
    const pageSize = common_vendor.computed(() => viewMode.value === "viewer" ? 20 : 50);
    const hasMore = common_vendor.ref(true);
    const statusTabs = common_vendor.ref([
      { label: "全部", value: "all" },
      { label: "执行中", value: "Executing" },
      { label: "已完成", value: "Completed" },
      { label: "已拒绝", value: "Rejected" },
      { label: "已取消", value: "Cancelled" },
      { label: "已过期", value: "Expired" }
    ]);
    const activeTab = common_vendor.ref("all");
    const loadMoreText = common_vendor.computed(() => {
      if (loadingMore.value) {
        return "加载中...";
      }
      if (!hasMore.value) {
        return "没有更多数据了";
      }
      return "上拉加载更多";
    });
    const currentMode = common_vendor.computed(() => {
      return viewMode.value === "viewer" ? "viewer" : "pricer";
    });
    const pageTitle = common_vendor.computed(() => {
      return viewMode.value === "viewer" ? "交易请求查看" : "我的交易请求";
    });
    function loadTradeRequests(isRefresh = false) {
      return __async(this, null, function* () {
        if ((loading.value || loadingMore.value) && !isRefresh)
          return;
        if (isRefresh) {
          refreshing.value = true;
          page.value = 1;
          hasMore.value = true;
        } else if (page.value === 1) {
          loading.value = true;
        } else if (viewMode.value === "viewer") {
          loadingMore.value = true;
        }
        try {
          const currentPage = isRefresh ? 1 : page.value;
          const response = yield api_traderequest.getMyTradeRequestsAsPricer({
            status: statusFilter.value === "" ? void 0 : statusFilter.value,
            page: currentPage,
            pageSize: pageSize.value
          });
          if (response.code === 0) {
            const newRequests = response.data.list;
            if (isRefresh || page.value === 1) {
              tradeRequests.value = newRequests;
              if (isRefresh) {
                utils_toast.toast.success("刷新成功");
              }
            } else {
              tradeRequests.value.push(...newRequests);
            }
            if (viewMode.value === "viewer") {
              hasMore.value = newRequests.length === pageSize.value;
              if (hasMore.value && !isRefresh && page.value > 1) {
                page.value++;
              }
            }
          } else {
            utils_toast.toast.error(response.msg || "获取交易请求失败");
          }
        } catch (error) {
          console.error("获取交易请求失败:", error);
          utils_toast.toast.error("网络错误");
        } finally {
          if (isRefresh) {
            refreshing.value = false;
            setTimeout(() => {
              try {
                common_vendor.index.stopPullDownRefresh();
              } catch (e) {
                console.warn("停止下拉刷新失败:", e);
              }
            }, 500);
          } else if (page.value === 1) {
            loading.value = false;
          } else {
            loadingMore.value = false;
          }
        }
      });
    }
    function loadMore() {
      return __async(this, null, function* () {
        if (viewMode.value !== "viewer" || !hasMore.value || loadingMore.value || loading.value)
          return;
        page.value++;
        yield loadTradeRequests();
      });
    }
    function handleTabChange(changeEvent) {
      console.log("Tab切换:", { changeEvent, oldActiveTab: activeTab.value });
      const newValue = changeEvent.name;
      activeTab.value = newValue;
      statusFilter.value = newValue === "all" ? "" : newValue;
      console.log("更新后:", { activeTab: activeTab.value, statusFilter: statusFilter.value });
      page.value = 1;
      hasMore.value = true;
      loadTradeRequests();
    }
    function handleRefresh() {
      loadTradeRequests(true);
    }
    function handleCancel(request) {
      return __async(this, null, function* () {
        try {
          const confirmResult = yield common_vendor.index.showModal({
            title: "确认取消",
            content: `确定要取消这个${request.requestType === "PointPrice" ? "点价" : "洗基差"}请求吗？`,
            confirmText: "确认取消",
            cancelText: "我再想想"
          });
          if (!confirmResult.confirm) {
            return;
          }
          loading.value = true;
          const response = yield api_traderequest.cancelTradeRequest(request.ID);
          if (response.code === 0) {
            utils_toast.toast.success("取消成功");
            loadTradeRequests(true);
          } else {
            utils_toast.toast.error(response.msg || "取消失败");
          }
        } catch (error) {
          console.error("取消交易请求失败:", error);
          utils_toast.toast.error("操作失败，请重试");
        } finally {
          loading.value = false;
        }
      });
    }
    common_vendor.onLoad((options) => {
      if ((options == null ? void 0 : options.mode) === "viewer") {
        viewMode.value = "viewer";
      }
      common_vendor.index.setNavigationBarTitle({
        title: pageTitle.value
      });
    });
    common_vendor.onMounted(() => {
      activeTab.value = statusFilter.value === "" ? "all" : statusFilter.value;
      loadTradeRequests();
    });
    common_vendor.onPullDownRefresh(() => __async(this, null, function* () {
      console.log("下拉刷新触发");
      try {
        yield loadTradeRequests(true);
      } catch (error) {
        console.error("下拉刷新失败:", error);
      }
    }));
    common_vendor.onReachBottom(() => {
      if (viewMode.value === "viewer") {
        loadMore();
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: refreshing.value
      }, refreshing.value ? {} : {}, {
        b: common_vendor.f(statusTabs.value, (tab, k0, i0) => {
          return {
            a: tab.value,
            b: "732d0900-2-" + i0 + ",732d0900-1",
            c: common_vendor.p({
              title: tab.label,
              name: tab.value
            })
          };
        }),
        c: common_vendor.o(handleTabChange),
        d: common_vendor.o(($event) => activeTab.value = $event),
        e: common_vendor.p({
          modelValue: activeTab.value
        }),
        f: common_vendor.o(handleCancel),
        g: common_vendor.o(handleRefresh),
        h: common_vendor.p({
          requests: tradeRequests.value,
          loading: loading.value,
          refreshing: refreshing.value,
          mode: currentMode.value
        }),
        i: viewMode.value === "viewer" && tradeRequests.value.length > 0
      }, viewMode.value === "viewer" && tradeRequests.value.length > 0 ? common_vendor.e({
        j: loadingMore.value
      }, loadingMore.value ? {
        k: common_vendor.t(loadMoreText.value)
      } : {
        l: common_vendor.t(loadMoreText.value)
      }) : {});
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-732d0900"]]);
wx.createPage(MiniProgramPage);
