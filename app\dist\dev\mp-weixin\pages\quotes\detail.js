"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const api_quotation = require("../../api/quotation.js");
if (!Array) {
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_loading2 + _easycom_wd_cell2 + _easycom_wd_icon2 + _easycom_wd_cell_group2 + _easycom_wd_button2 + _easycom_wd_img2 + _component_layout_default_uni)();
}
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
if (!Math) {
  (_easycom_wd_loading + _easycom_wd_cell + _easycom_wd_icon + _easycom_wd_cell_group + _easycom_wd_button + _easycom_wd_img)();
}
const _sfc_defineComponent = common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "QuotationDetail"
}), {
  __name: "detail",
  setup(__props) {
    function goBack() {
      common_vendor.index.navigateBack();
    }
    const userStore = store_user.useUserStore();
    const quotationId = common_vendor.ref();
    const isLoading = common_vendor.ref(false);
    const quotation = common_vendor.ref();
    const isPublicAccess = common_vendor.ref(false);
    const statusConfig = {
      Draft: { label: "草稿", description: "报价草稿，未公开发布" },
      Active: { label: "上线", description: "报价已公开，可被其他用户查看" }
    };
    const isOwner = common_vendor.computed(() => quotation.value && userStore.userInfo && quotation.value.userID === userStore.userInfo.ID);
    const isPublicView = common_vendor.computed(() => !isOwner.value);
    const canEdit = common_vendor.computed(() => isOwner.value);
    const canPublish = common_vendor.computed(() => {
      var _a;
      return isOwner.value && ((_a = quotation.value) == null ? void 0 : _a.status) === "Draft";
    });
    const canToggleStatus = common_vendor.computed(() => {
      var _a;
      return isOwner.value && ((_a = quotation.value) == null ? void 0 : _a.status) === "Active";
    });
    const canDelete = common_vendor.computed(() => {
      var _a;
      return isOwner.value && ((_a = quotation.value) == null ? void 0 : _a.status) === "Draft";
    });
    const canShare = common_vendor.computed(() => {
      var _a;
      return isOwner.value && ((_a = quotation.value) == null ? void 0 : _a.status) === "Active";
    });
    const showContactButton = common_vendor.computed(() => {
      var _a;
      return isPublicView.value && ((_a = quotation.value) == null ? void 0 : _a.status) === "Active";
    });
    common_vendor.onLoad((options) => {
      if (options == null ? void 0 : options.id) {
        quotationId.value = parseInt(options.id);
        isPublicAccess.value = (options == null ? void 0 : options.from) === "share" || (options == null ? void 0 : options.from) === "public" || (options == null ? void 0 : options.from) === "marketplace";
        loadQuotationDetail();
      } else {
        common_vendor.index.showToast({ title: "报价ID不存在", icon: "error" });
        setTimeout(() => common_vendor.index.navigateBack(), 1500);
      }
    });
    function loadQuotationDetail() {
      return __async(this, null, function* () {
        if (!quotationId.value)
          return;
        isLoading.value = true;
        try {
          let res;
          if (isPublicAccess.value || !userStore.isLoggedIn) {
            res = yield api_quotation.getPublicQuotationDetail(quotationId.value);
          } else {
            res = yield api_quotation.getQuotationDetail(quotationId.value);
          }
          quotation.value = res.data;
          common_vendor.index.setNavigationBarTitle({ title: quotation.value.title || "报价详情" });
        } catch (error) {
          console.error("加载报价详情失败:", error);
          common_vendor.index.showToast({ title: "加载失败", icon: "error" });
          setTimeout(() => common_vendor.index.navigateBack(), 1500);
        } finally {
          isLoading.value = false;
        }
      });
    }
    function editQuotation() {
      if (!quotation.value)
        return;
      common_vendor.index.navigateTo({ url: `/pages/quotes/edit?id=${quotation.value.id}` });
    }
    function publishQuotationItem() {
      return __async(this, null, function* () {
        if (!quotation.value)
          return;
        common_vendor.index.showLoading({ title: "发布中..." });
        try {
          const expiresAt = /* @__PURE__ */ new Date();
          expiresAt.setDate(expiresAt.getDate() + 7);
          yield api_quotation.publishQuotation({ id: quotation.value.id, expiresAt: expiresAt.toISOString() });
          common_vendor.index.showToast({ title: "发布成功", icon: "success" });
          yield loadQuotationDetail();
        } catch (error) {
          console.error("发布报价失败:", error);
          common_vendor.index.showToast({ title: "发布失败", icon: "error" });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
    function toggleQuotationStatusItem() {
      return __async(this, null, function* () {
        if (!quotation.value)
          return;
        const actionText = "下架";
        const confirmText = "确定要将此报价下架吗？下架后报价将变为草稿状态。";
        const { confirm } = yield common_vendor.index.showModal({ title: `确认${actionText}`, content: confirmText });
        if (!confirm)
          return;
        common_vendor.index.showLoading({ title: `${actionText}中...` });
        try {
          yield api_quotation.toggleQuotationStatus(quotation.value.id);
          common_vendor.index.showToast({ title: `${actionText}成功`, icon: "success" });
          yield loadQuotationDetail();
        } catch (error) {
          console.error("下架失败:", error);
          common_vendor.index.showToast({ title: `${actionText}失败`, icon: "error" });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
    function deleteQuotationItem() {
      return __async(this, null, function* () {
        if (!quotation.value)
          return;
        const { confirm } = yield common_vendor.index.showModal({ title: "确认删除", content: "确定要删除这个报价草稿吗？" });
        if (!confirm)
          return;
        common_vendor.index.showLoading({ title: "删除中..." });
        try {
          yield api_quotation.deleteQuotation(quotation.value.id);
          common_vendor.index.showToast({ title: "删除成功", icon: "success" });
          setTimeout(() => common_vendor.index.navigateBack(), 1500);
        } catch (error) {
          console.error("删除报价失败:", error);
          common_vendor.index.showToast({ title: "删除失败", icon: "error" });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
    function contactPublisher() {
      if (!quotation.value)
        return;
      common_vendor.index.showToast({ title: "功能开发中", icon: "none" });
    }
    function callPhone(phoneNumber) {
      if (!phoneNumber)
        return;
      common_vendor.index.makePhoneCall({ phoneNumber });
    }
    function formatPrice(q) {
      if (q.priceType === "Fixed")
        return `¥ ${q.price.toFixed(2)}`;
      if (q.priceType === "Basis" && q.instrumentRef) {
        return `${q.instrumentRef.instrument_id} ${q.price >= 0 ? "+ " : ""}${q.price}`;
      }
      return q.price.toString();
    }
    function formatDateTime(dateTime) {
      const d = new Date(dateTime);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}-${String(d.getDate()).padStart(2, "0")} ${String(d.getHours()).padStart(2, "0")}:${String(d.getMinutes()).padStart(2, "0")}`;
    }
    function formatRemainingTime() {
      if (!quotation.value || quotation.value.status !== "Active")
        return "";
      if (quotation.value.isExpired)
        return "已过期";
      const { remainingHours } = quotation.value;
      if (remainingHours <= 0)
        return "即将过期";
      if (remainingHours < 24)
        return `剩余 ${remainingHours} 小时`;
      return `剩余 ${Math.floor(remainingHours / 24)} 天`;
    }
    common_vendor.onShareAppMessage(() => {
      var _a;
      if (!quotation.value)
        return {};
      const title = quotation.value.title;
      const path = `/pages/quotes/detail?id=${quotation.value.id}&from=share`;
      return {
        title,
        path,
        imageUrl: ((_a = quotation.value.user) == null ? void 0 : _a.headerImg) || "static/logo.svg"
      };
    });
    common_vendor.onShareTimeline(() => {
      var _a;
      if (!quotation.value)
        return {};
      const title = `${quotation.value.title} - ${quotation.value.commodityName}`;
      const query = `share=timeline&id=${quotation.value.id}&from=share`;
      return {
        title,
        query,
        imageUrl: ((_a = quotation.value.user) == null ? void 0 : _a.headerImg) || "static/logo.svg"
      };
    });
    function shareQuotation() {
      if (!quotation.value)
        return;
      common_vendor.index.showToast({
        title: "请点击右上角分享",
        icon: "none",
        duration: 2e3
      });
    }
    return (_ctx, _cache) => {
      var _a, _b, _c, _d;
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {
        b: common_vendor.p({
          ["custom-class"]: "loading-spinner"
        })
      } : quotation.value ? common_vendor.e({
        d: isPublicView.value && quotation.value.user
      }, isPublicView.value && quotation.value.user ? common_vendor.e({
        e: (_a = quotation.value.user) == null ? void 0 : _a.companyName
      }, ((_b = quotation.value.user) == null ? void 0 : _b.companyName) ? {
        f: common_vendor.p({
          title: "发布企业",
          value: quotation.value.user.companyName
        })
      } : {}, {
        g: (_c = quotation.value.user) == null ? void 0 : _c.nickName
      }, ((_d = quotation.value.user) == null ? void 0 : _d.nickName) ? {
        h: common_vendor.p({
          title: "联系人",
          value: quotation.value.user.nickName
        })
      } : {}, {
        i: common_vendor.t(quotation.value.user.phone),
        j: common_vendor.p({
          name: "phone",
          ["custom-class"]: "phone-icon"
        }),
        k: common_vendor.o(($event) => callPhone(quotation.value.user.phone)),
        l: common_vendor.p({
          title: "联系电话"
        }),
        m: common_vendor.p({
          border: true
        })
      }) : {}, {
        n: common_vendor.t(statusConfig[quotation.value.status].label),
        o: common_vendor.n(quotation.value.status.toLowerCase()),
        p: common_vendor.t(quotation.value.title),
        q: common_vendor.t(formatPrice(quotation.value)),
        r: common_vendor.t(quotation.value.commodityName || "-"),
        s: common_vendor.t(quotation.value.deliveryLocation),
        t: common_vendor.t(quotation.value.priceType === "Fixed" ? "一口价" : "基差报价"),
        v: quotation.value.priceType === "Basis" && quotation.value.instrumentRef
      }, quotation.value.priceType === "Basis" && quotation.value.instrumentRef ? {
        w: common_vendor.t(quotation.value.instrumentRef.instrument_name)
      } : {}, {
        x: quotation.value.brand
      }, quotation.value.brand ? {
        y: common_vendor.p({
          title: "品牌",
          value: quotation.value.brand
        })
      } : {}, {
        z: common_vendor.p({
          title: "发布时间",
          value: formatDateTime(quotation.value.createdAt)
        }),
        A: common_vendor.p({
          title: "过期时间",
          value: formatDateTime(quotation.value.expiresAt)
        }),
        B: quotation.value.status === "Active"
      }, quotation.value.status === "Active" ? {
        C: common_vendor.t(formatRemainingTime()),
        D: quotation.value.isExpired ? 1 : "",
        E: common_vendor.p({
          title: "剩余有效期"
        })
      } : {}, {
        F: quotation.value.specifications || quotation.value.description
      }, quotation.value.specifications || quotation.value.description ? common_vendor.e({
        G: quotation.value.specifications
      }, quotation.value.specifications ? {
        H: common_vendor.t(quotation.value.specifications),
        I: common_vendor.p({
          title: "规格说明"
        })
      } : {}, {
        J: quotation.value.description
      }, quotation.value.description ? {
        K: common_vendor.t(quotation.value.description),
        L: common_vendor.p({
          title: "补充说明"
        })
      } : {}) : {}, {
        M: isOwner.value
      }, isOwner.value ? common_vendor.e({
        N: canEdit.value
      }, canEdit.value ? {
        O: common_vendor.o(editQuotation),
        P: common_vendor.p({
          ["custom-class"]: "action-button",
          size: "large"
        })
      } : {}, {
        Q: canPublish.value
      }, canPublish.value ? {
        R: common_vendor.o(publishQuotationItem),
        S: common_vendor.p({
          ["custom-class"]: "action-button primary-button",
          size: "large"
        })
      } : {}, {
        T: canToggleStatus.value
      }, canToggleStatus.value ? {
        U: common_vendor.o(toggleQuotationStatusItem),
        V: common_vendor.p({
          ["custom-class"]: "action-button warning-button",
          size: "large"
        })
      } : {}, {
        W: canDelete.value
      }, canDelete.value ? {
        X: common_vendor.o(deleteQuotationItem),
        Y: common_vendor.p({
          ["custom-class"]: "action-button error-button",
          size: "large"
        })
      } : {}, {
        Z: canShare.value
      }, canShare.value ? {
        aa: common_vendor.o(shareQuotation),
        ab: common_vendor.p({
          ["custom-class"]: "action-button success-button",
          plain: true,
          size: "large"
        })
      } : {}) : common_vendor.e({
        ac: showContactButton.value
      }, showContactButton.value ? {
        ad: common_vendor.o(contactPublisher),
        ae: common_vendor.p({
          ["custom-class"]: "action-button primary-button",
          size: "large"
        })
      } : {}, {
        af: common_vendor.o(shareQuotation),
        ag: common_vendor.p({
          ["custom-class"]: "action-button success-button",
          plain: true,
          size: "large"
        })
      })) : {
        ah: common_vendor.p({
          src: "/static/images/error-404.png",
          width: "200rpx",
          height: "200rpx",
          mode: "aspectFit"
        }),
        ai: common_vendor.o(goBack),
        aj: common_vendor.p({
          ["custom-class"]: "primary-button"
        })
      }, {
        c: quotation.value
      });
    };
  }
}));
_sfc_defineComponent.__runtimeHooks = 6;
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_defineComponent, [["__scopeId", "data-v-af301bab"]]);
wx.createPage(MiniProgramPage);
