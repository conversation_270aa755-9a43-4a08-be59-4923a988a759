/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.header-section.data-v-f81ce579 {
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.header-content.data-v-f81ce579 {
  display: flex;
  align-items: center;
}
.filter-tabs.data-v-f81ce579 {
  display: flex;
  flex: 1;
  margin-right: 40rpx;
}
.filter-tab.data-v-f81ce579 {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
  position: relative;
}
.filter-tab.active.data-v-f81ce579 {
  color: #007aff;
  font-weight: 500;
  border-bottom: 4rpx solid #007aff;
}
.header-actions.data-v-f81ce579 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-left: 20rpx;
  margin-right: 20rpx;
}
.scroll-container.data-v-f81ce579 {
  flex: 1;
}
.list-container.data-v-f81ce579 {
  padding: 20rpx 0rpx 0;
}
.quotation-list.data-v-f81ce579 {
  margin: 0rpx 20rpx;
}
.quotation-list .quotation-card.data-v-f81ce579 {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}
.quotation-list .quotation-card .status-tag.data-v-f81ce579 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  left: auto;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.quotation-list .quotation-card .card-header.data-v-f81ce579 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 0 120rpx 0 0;
}
.quotation-list .quotation-card .card-header .quotation-title.data-v-f81ce579 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  line-height: 1.4;
}
.quotation-list .quotation-card .card-header .quotation-price.data-v-f81ce579 {
  font-size: 36rpx;
  font-weight: 700;
  color: #007aff;
  margin-left: 16rpx;
}
.quotation-list .quotation-card .card-content.data-v-f81ce579 {
  margin-bottom: 16rpx;
}
.quotation-list .quotation-card .card-content .info-row.data-v-f81ce579 {
  display: flex;
  margin-bottom: 8rpx;
  font-size: 26rpx;
}
.quotation-list .quotation-card .card-content .info-row .label.data-v-f81ce579 {
  color: #666;
  width: 80rpx;
}
.quotation-list .quotation-card .card-content .info-row .value.data-v-f81ce579 {
  color: #333;
  flex: 1;
}
.quotation-list .quotation-card .card-footer.data-v-f81ce579 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 24rpx;
}
.quotation-list .quotation-card .card-footer .create-time.data-v-f81ce579 {
  color: #999;
}
.quotation-list .quotation-card .card-footer .remaining-time.data-v-f81ce579 {
  color: #67C23A;
}
.quotation-list .quotation-card .card-footer .remaining-time.expired.data-v-f81ce579 {
  color: #F56C6C;
}
.quotation-list .quotation-card .action-buttons.data-v-f81ce579 {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}
.quotation-list .quotation-card .action-buttons.data-v-f81ce579 .wd-button {
  min-width: 120rpx;
}
.empty-state.data-v-f81ce579 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}
.empty-state .empty-text.data-v-f81ce579 {
  font-size: 28rpx;
  color: #999;
  margin: 24rpx 0 40rpx;
}
.loading-more.data-v-f81ce579 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}
.loading-more .loading-text.data-v-f81ce579 {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #999;
}
.no-more.data-v-f81ce579 {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #999;
}