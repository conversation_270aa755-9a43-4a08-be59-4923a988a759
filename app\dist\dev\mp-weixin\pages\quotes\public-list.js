"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const api_quotation = require("../../api/quotation.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_tag2 = common_vendor.resolveComponent("wd-tag");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_img2 + _easycom_wd_icon2 + _easycom_wd_tag2 + _easycom_wd_button2 + _easycom_wd_loading2 + _component_layout_default_uni)();
}
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_tag = () => "../../node-modules/wot-design-uni/components/wd-tag/wd-tag.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
if (!Math) {
  (_easycom_wd_img + _easycom_wd_icon + _easycom_wd_tag + _easycom_wd_button + QuotationCard + _easycom_wd_loading + QuotationListPoster)();
}
const QuotationCard = () => "../../components/marketplace/QuotationCard.js";
const QuotationListPoster = () => "../../components/marketplace/QuotationListPoster.js";
const _sfc_defineComponent = common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "UserHomePage"
}), {
  __name: "public-list",
  setup(__props) {
    const userId = common_vendor.ref();
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const userProfile = common_vendor.ref();
    const quotationList = common_vendor.ref([]);
    const currentPage = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const total = common_vendor.ref(0);
    const userStore = store_user.useUserStore();
    const showPoster = common_vendor.ref(false);
    const pageTitle = common_vendor.computed(() => {
      var _a, _b;
      if ((_a = userProfile.value) == null ? void 0 : _a.companyName) {
        return userProfile.value.companyName;
      }
      return ((_b = userProfile.value) == null ? void 0 : _b.nickName) || "用户主页";
    });
    common_vendor.computed(() => {
      return userProfile.value && userProfile.value.enable === 1;
    });
    const isOwner = common_vendor.computed(() => {
      return userStore.isLoggedIn && userStore.userInfo.ID === userId.value;
    });
    function loadUserProfile() {
      return __async(this, null, function* () {
        if (!userId.value)
          return;
        try {
          const res = yield api_user.getUserPublicProfile(userId.value);
          userProfile.value = res.data;
          common_vendor.index.setNavigationBarTitle({
            title: pageTitle.value
          });
        } catch (error) {
          console.error("加载用户档案失败:", error);
          common_vendor.index.showToast({
            title: "用户不存在",
            icon: "error"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        }
      });
    }
    function loadQuotationList(refresh = false) {
      return __async(this, null, function* () {
        if (!userId.value)
          return;
        if (refresh) {
          currentPage.value = 1;
          quotationList.value = [];
          isRefreshing.value = true;
        } else {
          isLoading.value = true;
        }
        try {
          const params = {
            page: currentPage.value,
            pageSize: pageSize.value,
            userID: userId.value
          };
          const res = yield api_quotation.getPublicQuotationList(params);
          const { list, total: totalCount } = res.data;
          if (refresh) {
            quotationList.value = list;
          } else {
            quotationList.value.push(...list);
          }
          total.value = totalCount;
          hasMore.value = quotationList.value.length < totalCount;
        } catch (error) {
          console.error("加载报价列表失败:", error);
          common_vendor.index.showToast({
            title: "加载失败",
            icon: "error"
          });
        } finally {
          isLoading.value = false;
          isRefreshing.value = false;
        }
      });
    }
    function loadMore() {
      return __async(this, null, function* () {
        if (!hasMore.value || isLoading.value)
          return;
        currentPage.value++;
        yield loadQuotationList();
      });
    }
    function onRefresh() {
      return __async(this, null, function* () {
        yield loadQuotationList(true);
      });
    }
    function viewQuotationDetail(quotation) {
      common_vendor.index.navigateTo({
        url: `/pages/quotes/detail?id=${quotation.id}&from=public`
      });
    }
    function handlePublisherClick() {
    }
    function handleManageClick() {
      common_vendor.index.navigateTo({
        url: "/pages/quotes/my-list"
      });
    }
    function handleGeneratePoster() {
      if (!userProfile.value || quotationList.value.length === 0) {
        common_vendor.index.showToast({
          title: "数据加载中，请稍后",
          icon: "none"
        });
        return;
      }
      showPoster.value = true;
    }
    function handlePosterSuccess(imagePath) {
      console.log("海报生成成功:", imagePath);
    }
    function handlePosterFail(error) {
      console.error("海报生成失败:", error);
    }
    common_vendor.onShareAppMessage(() => {
      if (!userProfile.value || !userId.value)
        return {};
      const displayName = userProfile.value.companyName || userProfile.value.nickName || "用户";
      const title = `快来看看 ${displayName} 的最新报价`;
      const path = `/pages/quotes/public-list?id=${userId.value}`;
      return {
        title,
        path
      };
    });
    common_vendor.onShareTimeline(() => {
      if (!userProfile.value || !userId.value)
        return {};
      const displayName = userProfile.value.companyName || userProfile.value.nickName || "用户";
      const title = `快来看看 ${displayName} 的最新报价`;
      const query = `share=timeline&id=${userId.value}`;
      return {
        title,
        query
      };
    });
    common_vendor.onLoad((options) => {
      if (options.share === "timeline" && options.id) {
        userId.value = parseInt(options.id);
        return;
      }
      if (options == null ? void 0 : options.id) {
        userId.value = parseInt(options.id);
      } else {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "error"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
        return;
      }
    });
    common_vendor.onMounted(() => __async(this, null, function* () {
      if (userId.value) {
        yield loadUserProfile();
        yield loadQuotationList();
      }
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: userProfile.value
      }, userProfile.value ? common_vendor.e({
        b: common_vendor.p({
          src: userProfile.value.headerImg,
          width: "120rpx",
          height: "120rpx",
          mode: "aspectFill",
          round: true,
          ["custom-class"]: "user-avatar"
        }),
        c: common_vendor.t(userProfile.value.companyName || userProfile.value.nickName),
        d: userProfile.value.companyName
      }, userProfile.value.companyName ? {
        e: common_vendor.t(userProfile.value.nickName)
      } : {}, {
        f: userProfile.value.companyAddress
      }, userProfile.value.companyAddress ? {
        g: common_vendor.p({
          name: "location",
          size: "24rpx"
        }),
        h: common_vendor.t(userProfile.value.companyAddress)
      } : {}, {
        i: common_vendor.t(userProfile.value.enable === 1 ? "正常" : "冻结"),
        j: common_vendor.p({
          type: userProfile.value.enable === 1 ? "success" : "danger",
          size: "small"
        }),
        k: isOwner.value
      }, isOwner.value ? {
        l: common_vendor.p({
          name: "edit",
          size: "24rpx"
        }),
        m: common_vendor.o(handleManageClick),
        n: common_vendor.p({
          type: "primary",
          size: "small",
          ["custom-class"]: "manage-btn"
        })
      } : {}) : {}, {
        o: common_vendor.t(total.value),
        p: quotationList.value.length > 0
      }, quotationList.value.length > 0 ? {
        q: common_vendor.f(quotationList.value, (quotation, k0, i0) => {
          return {
            a: quotation.id,
            b: common_vendor.o(viewQuotationDetail, quotation.id),
            c: common_vendor.o(handlePublisherClick, quotation.id),
            d: "3ff80c61-6-" + i0 + ",3ff80c61-0",
            e: common_vendor.p({
              quotation
            })
          };
        })
      } : !isLoading.value ? {} : {}, {
        r: !isLoading.value,
        s: isLoading.value && quotationList.value.length > 0
      }, isLoading.value && quotationList.value.length > 0 ? {
        t: common_vendor.p({
          size: "24rpx",
          ["custom-class"]: "loading-spinner"
        })
      } : {}, {
        v: !hasMore.value && quotationList.value.length > 0
      }, !hasMore.value && quotationList.value.length > 0 ? {} : {}, {
        w: quotationList.value.length > 0
      }, quotationList.value.length > 0 ? {} : {}, {
        x: isRefreshing.value,
        y: common_vendor.o(onRefresh),
        z: common_vendor.o(loadMore),
        A: quotationList.value.length > 0
      }, quotationList.value.length > 0 ? {
        B: common_vendor.p({
          name: "share",
          size: "32rpx"
        }),
        C: common_vendor.o(handleGeneratePoster),
        D: common_vendor.p({
          type: "primary",
          block: true,
          ["custom-class"]: "fixed-poster-btn"
        })
      } : {}, {
        E: common_vendor.o(handlePosterSuccess),
        F: common_vendor.o(handlePosterFail),
        G: common_vendor.o(($event) => showPoster.value = $event),
        H: common_vendor.p({
          ["user-profile"]: userProfile.value || {},
          ["quotation-list"]: quotationList.value,
          visible: showPoster.value
        })
      });
    };
  }
}));
_sfc_defineComponent.__runtimeHooks = 6;
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_defineComponent, [["__scopeId", "data-v-3ff80c61"]]);
wx.createPage(MiniProgramPage);
