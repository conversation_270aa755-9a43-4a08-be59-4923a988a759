/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
.wot-theme-dark .wd-action-sheet.data-v-904a1f1d {
  background-color: var(--wot-dark-background2, #1b1b1b);
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-action-sheet__action.data-v-904a1f1d {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
  background: var(--wot-dark-background2, #1b1b1b);
}
.wot-theme-dark .wd-action-sheet__action.data-v-904a1f1d:not(.wd-action-sheet__action--disabled):not(.wd-action-sheet__action--loading):active {
  background: var(--wot-dark-background4, #323233);
}
.wot-theme-dark .wd-action-sheet__action--disabled.data-v-904a1f1d {
  color: var(--wot-dark-color-gray, var(--wot-color-secondary, #595959));
}
.wot-theme-dark .wd-action-sheet__subname.data-v-904a1f1d {
  color: var(--wot-dark-color3, rgba(232, 230, 227, 0.8));
}
.wot-theme-dark .wd-action-sheet__cancel.data-v-904a1f1d {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
  background: var(--wot-dark-background4, #323233);
}
.wot-theme-dark .wd-action-sheet__cancel.data-v-904a1f1d:active {
  background: var(--wot-dark-background5, #646566);
}
.wot-theme-dark .wd-action-sheet.data-v-904a1f1d .wd-action-sheet__close {
  color: var(--wot-dark-color3, rgba(232, 230, 227, 0.8));
}
.wot-theme-dark .wd-action-sheet__panel-title.data-v-904a1f1d {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-action-sheet__header.data-v-904a1f1d {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.data-v-904a1f1d .wd-action-sheet__popup {
  border-radius: var(--wot-action-sheet-radius, 16px) var(--wot-action-sheet-radius, 16px) 0 0;
}
.wd-action-sheet.data-v-904a1f1d {
  background-color: var(--wot-color-white, rgb(255, 255, 255));
  padding-bottom: 1px;
}
.data-v-904a1f1d  .wd-action-sheet__popup {
  border-radius: var(--wot-action-sheet-radius, 16px) var(--wot-action-sheet-radius, 16px) 0 0;
}
.wd-action-sheet__actions.data-v-904a1f1d {
  padding: 8px 0;
  max-height: 50vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.wd-action-sheet__action.data-v-904a1f1d {
  position: relative;
  display: block;
  width: 100%;
  height: var(--wot-action-sheet-action-height, 48px);
  line-height: var(--wot-action-sheet-action-height, 48px);
  color: var(--wot-action-sheet-color, rgba(0, 0, 0, 0.85));
  font-size: var(--wot-action-sheet-fs, var(--wot-fs-title, 16px));
  text-align: center;
  border: none;
  background: var(--wot-action-sheet-bg, var(--wot-color-white, rgb(255, 255, 255)));
  outline: none;
}
.wd-action-sheet__action.data-v-904a1f1d:after {
  display: none;
}
.wd-action-sheet__action.data-v-904a1f1d:not(.wd-action-sheet__action--disabled):not(.wd-action-sheet__action--loading):active {
  background: var(--wot-action-sheet-active-color, var(--wot-color-bg, #f5f5f5));
}
.wd-action-sheet__action--disabled.data-v-904a1f1d {
  color: var(--wot-action-sheet-disabled-color, rgba(0, 0, 0, 0.25));
  cursor: not-allowed;
}
.wd-action-sheet__action--loading.data-v-904a1f1d {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: initial;
}
.data-v-904a1f1d  .wd-action-sheet__action-loading {
  width: var(--wot-action-sheet-loading-size, 20px);
  height: var(--wot-action-sheet-loading-size, 20px);
}
.wd-action-sheet__name.data-v-904a1f1d {
  display: inline-block;
}
.wd-action-sheet__subname.data-v-904a1f1d {
  display: inline-block;
  margin-left: 4px;
  font-size: var(--wot-action-sheet-subname-fs, var(--wot-fs-secondary, 12px));
  color: var(--wot-action-sheet-subname-color, rgba(0, 0, 0, 0.45));
}
.wd-action-sheet__cancel.data-v-904a1f1d {
  display: block;
  width: calc(100% - 48px);
  line-height: var(--wot-action-sheet-cancel-height, 44px);
  padding: 0;
  color: var(--wot-action-sheet-cancel-color, #131415);
  font-size: var(--wot-action-sheet-fs, var(--wot-fs-title, 16px));
  text-align: center;
  border-radius: var(--wot-action-sheet-cancel-radius, 22px);
  border: none;
  background: var(--wot-action-sheet-cancel-bg, rgb(240, 240, 240));
  outline: none;
  margin: 0 auto 24px;
  font-weight: var(--wot-action-sheet-weight, 500);
}
.wd-action-sheet__cancel.data-v-904a1f1d:active {
  background: var(--wot-action-sheet-active-color, var(--wot-color-bg, #f5f5f5));
}
.wd-action-sheet__cancel.data-v-904a1f1d:after {
  display: none;
}
.wd-action-sheet__header.data-v-904a1f1d {
  color: var(--wot-action-sheet-color, rgba(0, 0, 0, 0.85));
  position: relative;
  height: var(--wot-action-sheet-title-height, 64px);
  line-height: var(--wot-action-sheet-title-height, 64px);
  text-align: center;
  font-size: var(--wot-action-sheet-title-fs, var(--wot-fs-title, 16px));
  font-weight: var(--wot-action-sheet-weight, 500);
}
.data-v-904a1f1d  .wd-action-sheet__close {
  position: absolute;
  top: var(--wot-action-sheet-close-top, 25px);
  right: var(--wot-action-sheet-close-right, 15px);
  color: var(--wot-action-sheet-close-color, rgba(0, 0, 0, 0.65));
  font-size: var(--wot-action-sheet-close-fs, var(--wot-fs-title, 16px));
  transform: rotate(-45deg);
  line-height: 1.1;
}
.wd-action-sheet__panels.data-v-904a1f1d {
  height: 84px;
  overflow-y: hidden;
}
.wd-action-sheet__panels.data-v-904a1f1d:first-of-type {
  margin-top: 20px;
}
.wd-action-sheet__panels.data-v-904a1f1d:last-of-type {
  margin-bottom: 12px;
}
.wd-action-sheet__panels-content.data-v-904a1f1d {
  display: flex;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.wd-action-sheet__panel.data-v-904a1f1d {
  width: 88px;
  flex: 0 0 auto;
  display: inline-block;
  padding: var(--wot-action-sheet-panel-padding, 12px 0 11px);
}
.wd-action-sheet__panel-img.data-v-904a1f1d {
  display: block;
  width: var(--wot-action-sheet-panel-img-fs, 40px);
  height: var(--wot-action-sheet-panel-img-fs, 40px);
  margin: 0 auto;
  margin-bottom: 7px;
  border-radius: var(--wot-action-sheet-panel-img-radius, 4px);
}
.wd-action-sheet__panel-title.data-v-904a1f1d {
  font-size: var(--wot-action-sheet-subname-fs, var(--wot-fs-secondary, 12px));
  line-height: 1.2;
  text-align: center;
  color: var(--wot-action-sheet-color, rgba(0, 0, 0, 0.85));
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}