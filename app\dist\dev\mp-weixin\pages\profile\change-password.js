"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const api_auth = require("../../api/auth.js");
const utils_toast = require("../../utils/toast.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_icon2 + _easycom_wd_input2 + _easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_input + _easycom_wd_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "change-password",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const formData = common_vendor.ref({
      oldPassword: "",
      newPassword: "",
      confirmPassword: ""
    });
    const submitting = common_vendor.ref(false);
    const isFormValid = common_vendor.computed(() => {
      const { oldPassword, newPassword, confirmPassword } = formData.value;
      return oldPassword.length >= 6 && newPassword.length >= 6 && confirmPassword.length >= 6 && newPassword === confirmPassword;
    });
    const showPasswordMismatchError = common_vendor.computed(() => {
      const { newPassword, confirmPassword } = formData.value;
      return confirmPassword.length > 0 && newPassword.length > 0 && newPassword !== confirmPassword;
    });
    const validatePassword = (password) => {
      if (password.length < 6) {
        return "密码长度至少6位";
      }
      return "";
    };
    const handleSubmit = () => __async(this, null, function* () {
      var _a;
      const { oldPassword, newPassword, confirmPassword } = formData.value;
      if (!oldPassword) {
        utils_toast.toast.error("请输入当前密码");
        return;
      }
      if (!newPassword) {
        utils_toast.toast.error("请输入新密码");
        return;
      }
      if (!confirmPassword) {
        utils_toast.toast.error("请确认新密码");
        return;
      }
      const passwordError = validatePassword(newPassword);
      if (passwordError) {
        utils_toast.toast.error(passwordError);
        return;
      }
      if (newPassword !== confirmPassword) {
        utils_toast.toast.error("两次输入的新密码不一致");
        return;
      }
      if (oldPassword === newPassword) {
        utils_toast.toast.error("新密码不能与当前密码相同");
        return;
      }
      submitting.value = true;
      try {
        const response = yield api_auth.changePassword({
          oldPassword,
          newPassword
        });
        if (response.code === 0) {
          utils_toast.toast.success("密码修改成功，请重新登录");
          setTimeout(() => __async(this, null, function* () {
            yield userStore.logout();
            common_vendor.index.reLaunch({
              url: "/pages/login/index"
            });
          }), 1500);
        } else {
          utils_toast.toast.error(response.data.msg || "密码修改失败");
        }
      } catch (error) {
        console.error("修改密码失败:", error);
        if ((_a = error.data) == null ? void 0 : _a.msg) {
          utils_toast.toast.error(error.data.msg);
        } else if (error.errMsg) {
          utils_toast.toast.error(error.errMsg);
        } else {
          utils_toast.toast.error("密码修改失败，请重试");
        }
      } finally {
        submitting.value = false;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          name: "lock-on",
          size: "48rpx",
          color: "#667eea"
        }),
        b: common_vendor.o(($event) => formData.value.oldPassword = $event),
        c: common_vendor.p({
          placeholder: "请输入当前密码",
          clearable: true,
          ["show-password"]: true,
          ["custom-class"]: "password-input",
          modelValue: formData.value.oldPassword
        }),
        d: common_vendor.o(($event) => formData.value.newPassword = $event),
        e: common_vendor.p({
          placeholder: "请输入新密码",
          clearable: true,
          ["show-password"]: true,
          ["custom-class"]: "password-input",
          modelValue: formData.value.newPassword
        }),
        f: common_vendor.o(($event) => formData.value.confirmPassword = $event),
        g: common_vendor.p({
          placeholder: "请再次输入新密码",
          clearable: true,
          ["show-password"]: true,
          ["custom-class"]: "password-input",
          modelValue: formData.value.confirmPassword
        }),
        h: showPasswordMismatchError.value
      }, showPasswordMismatchError.value ? {} : {}, {
        i: common_vendor.t(submitting.value ? "修改中..." : "确认修改"),
        j: common_vendor.o(handleSubmit),
        k: common_vendor.p({
          type: "primary",
          size: "large",
          ["custom-class"]: "submit-btn",
          loading: submitting.value,
          disabled: !isFormValid.value
        }),
        l: common_vendor.p({
          name: "info",
          size: "32rpx",
          color: "#f56c6c"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-667d2844"]]);
wx.createPage(MiniProgramPage);
