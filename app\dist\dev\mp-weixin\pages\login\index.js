"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_auth = require("../../api/auth.js");
const store_user = require("../../store/user.js");
const utils_toast = require("../../utils/toast.js");
const utils_index = require("../../utils/index.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  _easycom_wd_button();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const loginType = common_vendor.ref("username");
    const phoneForm = common_vendor.ref({
      phone: "",
      code: ""
    });
    const usernameForm = common_vendor.ref({
      username: "",
      password: "",
      captcha: "",
      captchaId: ""
    });
    const captchaData = common_vendor.ref({
      captchaId: "",
      picPath: "",
      captchaLength: 0,
      openCaptcha: false
    });
    const isSending = common_vendor.ref(false);
    const isLoggingIn = common_vendor.ref(false);
    common_vendor.ref(false);
    const isCountdown = common_vendor.ref(false);
    const countdown = common_vendor.ref(60);
    const phoneError = common_vendor.ref("");
    const codeError = common_vendor.ref("");
    const usernameError = common_vendor.ref("");
    const passwordError = common_vendor.ref("");
    const userStore = store_user.useUserStore();
    const canSendCode = common_vendor.computed(() => {
      return /^1[3-9]\d{9}$/.test(phoneForm.value.phone);
    });
    const canPhoneLogin = common_vendor.computed(() => {
      return canSendCode.value && /^\d{6}$/.test(phoneForm.value.code);
    });
    const canUsernameLogin = common_vendor.computed(() => {
      return usernameForm.value.username.trim() && usernameForm.value.password.trim();
    });
    const countdownText = common_vendor.computed(() => {
      return isCountdown.value ? `${countdown.value}s后重发` : "发送验证码";
    });
    common_vendor.watch(() => phoneForm.value.phone, () => {
      phoneError.value = "";
    });
    common_vendor.watch(() => phoneForm.value.code, () => {
      codeError.value = "";
    });
    common_vendor.watch(() => usernameForm.value.username, () => {
      usernameError.value = "";
    });
    common_vendor.watch(() => usernameForm.value.password, () => {
      passwordError.value = "";
    });
    function switchLoginType(type) {
      loginType.value = type;
      phoneError.value = "";
      codeError.value = "";
      usernameError.value = "";
      passwordError.value = "";
      if (type === "username") {
        fetchCaptcha();
      }
    }
    function fetchCaptcha() {
      return __async(this, null, function* () {
        try {
          const res = yield api_auth.getCaptcha();
          if (res.code === 0) {
            captchaData.value = res.data;
            usernameForm.value.captchaId = res.data.captchaId;
            usernameForm.value.captcha = "";
          }
        } catch (error) {
          console.error("获取验证码失败:", error);
          utils_toast.toast.error("获取验证码失败");
        }
      });
    }
    function refreshCaptcha() {
      fetchCaptcha();
    }
    common_vendor.onMounted(() => {
      if (loginType.value === "username") {
        fetchCaptcha();
      }
    });
    function handleSendCode() {
      return __async(this, null, function* () {
        if (!canSendCode.value) {
          utils_toast.toast.error("请输入正确的手机号");
          return;
        }
        try {
          isSending.value = true;
          yield userStore.sendVerificationCode(phoneForm.value.phone);
          startCountdown();
        } catch (error) {
          console.error("发送验证码失败:", error);
          utils_toast.toast.error(error.message || "发送验证码失败");
        } finally {
          isSending.value = false;
        }
      });
    }
    function startCountdown() {
      isCountdown.value = true;
      countdown.value = 60;
      const timer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          isCountdown.value = false;
          clearInterval(timer);
        }
      }, 1e3);
    }
    function handlePhoneLogin() {
      return __async(this, null, function* () {
        if (!canPhoneLogin.value) {
          utils_toast.toast.error("请填写完整的登录信息");
          return;
        }
        try {
          isLoggingIn.value = true;
          const loginData = {
            phone: phoneForm.value.phone,
            code: phoneForm.value.code
          };
          yield userStore.phoneLogin(loginData);
          utils_index.navigateToPage({
            url: "/pages/index/index"
          });
        } catch (error) {
          console.error("手机号登录失败:", error);
          utils_toast.toast.error(error.message || "登录失败");
        } finally {
          isLoggingIn.value = false;
        }
      });
    }
    function handleUsernameLogin() {
      return __async(this, null, function* () {
        if (!canUsernameLogin.value) {
          utils_toast.toast.error("请填写完整的登录信息");
          return;
        }
        try {
          isLoggingIn.value = true;
          const loginData = {
            username: usernameForm.value.username,
            password: usernameForm.value.password,
            captcha: usernameForm.value.captcha,
            captchaId: usernameForm.value.captchaId
          };
          yield userStore.usernameLogin(loginData);
          utils_index.navigateToPage({
            url: "/pages/index/index"
          });
        } catch (error) {
          console.error("用户名密码登录失败:", error);
          utils_toast.toast.error(error.message || "登录失败");
        } finally {
          isLoggingIn.value = false;
        }
      });
    }
    function showAgreement() {
      common_vendor.index.showModal({
        title: "用户协议",
        content: "这里是用户协议的内容...",
        showCancel: false
      });
    }
    function showPrivacy() {
      common_vendor.index.showModal({
        title: "隐私政策",
        content: "这里是隐私政策的内容...",
        showCancel: false
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0,
        b: loginType.value === "username" ? 1 : "",
        c: common_vendor.o(($event) => switchLoginType("username")),
        d: loginType.value === "phone" ? 1 : "",
        e: common_vendor.o(($event) => switchLoginType("phone")),
        f: loginType.value === "phone"
      }, loginType.value === "phone" ? {
        g: phoneForm.value.phone,
        h: common_vendor.o(($event) => phoneForm.value.phone = $event.detail.value),
        i: phoneForm.value.code,
        j: common_vendor.o(($event) => phoneForm.value.code = $event.detail.value),
        k: common_vendor.t(countdownText.value),
        l: common_vendor.o(handleSendCode),
        m: common_vendor.p({
          disabled: !canSendCode.value || isCountdown.value,
          loading: isSending.value,
          type: "text",
          size: "small"
        }),
        n: common_vendor.o(handlePhoneLogin),
        o: common_vendor.p({
          type: "primary",
          block: true,
          loading: isLoggingIn.value,
          disabled: !canPhoneLogin.value
        })
      } : {}, {
        p: loginType.value === "username"
      }, loginType.value === "username" ? common_vendor.e({
        q: usernameForm.value.username,
        r: common_vendor.o(($event) => usernameForm.value.username = $event.detail.value),
        s: "password",
        t: usernameForm.value.password,
        v: common_vendor.o(($event) => usernameForm.value.password = $event.detail.value),
        w: captchaData.value.openCaptcha
      }, captchaData.value.openCaptcha ? common_vendor.e({
        x: captchaData.value.captchaLength,
        y: usernameForm.value.captcha,
        z: common_vendor.o(($event) => usernameForm.value.captcha = $event.detail.value),
        A: captchaData.value.picPath
      }, captchaData.value.picPath ? {
        B: captchaData.value.picPath
      } : {}, {
        C: common_vendor.o(refreshCaptcha)
      }) : {}, {
        D: common_vendor.o(handleUsernameLogin),
        E: common_vendor.p({
          type: "primary",
          block: true,
          loading: isLoggingIn.value,
          disabled: !canUsernameLogin.value
        })
      }) : {}, {
        F: common_vendor.o(showAgreement),
        G: common_vendor.o(showPrivacy),
        H: common_vendor.o(showAgreement),
        I: common_vendor.o(showPrivacy)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-45258083"]]);
wx.createPage(MiniProgramPage);
