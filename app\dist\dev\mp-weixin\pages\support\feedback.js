"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_toast = require("../../utils/toast.js");
const utils_fileUpload = require("../../utils/fileUpload.js");
if (!Array) {
  const _easycom_wd_textarea2 = common_vendor.resolveComponent("wd-textarea");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_textarea2 + _easycom_wd_input2 + _easycom_wd_icon2 + _easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_textarea = () => "../../node-modules/wot-design-uni/components/wd-textarea/wd-textarea.js";
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (_easycom_wd_textarea + _easycom_wd_input + _easycom_wd_icon + _easycom_wd_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "feedback",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const feedbackContent = common_vendor.ref("");
    const contactInfo = common_vendor.ref(userStore.userInfo.phone || "");
    const uploadedImages = common_vendor.ref([]);
    const submitting = common_vendor.ref(false);
    const chooseImage = () => __async(this, null, function* () {
      try {
        const filePath = yield utils_fileUpload.FileUploadUtil.chooseImage({
          count: 3 - uploadedImages.value.length,
          sizeType: ["compressed"],
          sourceType: ["album", "camera"]
        });
        const files = Array.isArray(filePath) ? filePath : [filePath];
        for (const file of files) {
          uploadedImages.value.push({
            url: file,
            file
          });
        }
      } catch (error) {
        console.error("选择图片失败:", error);
        utils_toast.toast.error(error.message || "选择图片失败");
      }
    });
    const previewImage = (index) => {
      const urls = uploadedImages.value.map((img) => img.url);
      common_vendor.index.previewImage({
        current: index,
        urls
      });
    };
    const removeImage = (index) => {
      uploadedImages.value.splice(index, 1);
    };
    const submitFeedback = () => __async(this, null, function* () {
      if (!feedbackContent.value.trim()) {
        utils_toast.toast.error("请输入反馈内容");
        return;
      }
      submitting.value = true;
      try {
        const imageUrls = [];
        for (const image of uploadedImages.value) {
          if (image.file) {
            try {
              const fileInfo = yield utils_fileUpload.FileUploadUtil.uploadFile(
                image.file,
                userStore.token,
                {
                  classId: "0"
                }
              );
              imageUrls.push(fileInfo.url);
            } catch (error) {
              console.error("图片上传失败:", error);
            }
          }
        }
        const feedbackData = {
          content: feedbackContent.value.trim(),
          contact: contactInfo.value.trim(),
          images: imageUrls
        };
        console.log("提交反馈数据:", feedbackData);
        yield new Promise((resolve) => setTimeout(resolve, 2e3));
        utils_toast.toast.success("反馈提交成功，感谢您的宝贵意见！");
        feedbackContent.value = "";
        contactInfo.value = userStore.userInfo.phone || "";
        uploadedImages.value = [];
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        console.error("提交反馈失败:", error);
        utils_toast.toast.error(error.message || "提交失败，请重试");
      } finally {
        submitting.value = false;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => feedbackContent.value = $event),
        b: common_vendor.p({
          placeholder: "请详细描述您遇到的问题或建议，我们会认真对待每一条反馈",
          maxlength: 500,
          ["show-word-limit"]: true,
          ["auto-height"]: true,
          ["min-height"]: 200,
          clearable: true,
          modelValue: feedbackContent.value
        }),
        c: common_vendor.o(($event) => contactInfo.value = $event),
        d: common_vendor.p({
          placeholder: "请输入您的手机号或邮箱，方便我们联系您",
          clearable: true,
          maxlength: 50,
          modelValue: contactInfo.value
        }),
        e: common_vendor.f(uploadedImages.value, (image, index, i0) => {
          return {
            a: image.url,
            b: common_vendor.o(($event) => previewImage(index), index),
            c: "781c7fb7-3-" + i0 + ",781c7fb7-0",
            d: common_vendor.o(($event) => removeImage(index), index),
            e: index
          };
        }),
        f: common_vendor.p({
          name: "close",
          size: "24rpx",
          color: "#fff"
        }),
        g: uploadedImages.value.length < 3
      }, uploadedImages.value.length < 3 ? {
        h: common_vendor.p({
          name: "camera",
          size: "48rpx",
          color: "#c0c4cc"
        }),
        i: common_vendor.o(chooseImage)
      } : {}, {
        j: common_vendor.t(submitting.value ? "提交中..." : "提交反馈"),
        k: common_vendor.o(submitFeedback),
        l: common_vendor.p({
          type: "primary",
          size: "large",
          ["custom-class"]: "submit-btn",
          loading: submitting.value,
          disabled: !feedbackContent.value.trim()
        }),
        m: common_vendor.p({
          name: "info",
          size: "32rpx",
          color: "#909399"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-781c7fb7"]]);
wx.createPage(MiniProgramPage);
