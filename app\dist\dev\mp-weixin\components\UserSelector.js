"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_user = require("../api/user.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_search2 = common_vendor.resolveComponent("wd-search");
  const _easycom_wd_radio2 = common_vendor.resolveComponent("wd-radio");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_icon2 + _easycom_wd_input2 + _easycom_wd_search2 + _easycom_wd_radio2 + _easycom_wd_popup2)();
}
const _easycom_wd_icon = () => "../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_input = () => "../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_search = () => "../node-modules/wot-design-uni/components/wd-search/wd-search.js";
const _easycom_wd_radio = () => "../node-modules/wot-design-uni/components/wd-radio/wd-radio.js";
const _easycom_wd_popup = () => "../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_input + _easycom_wd_search + _easycom_wd_radio + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "UserSelector"
}), {
  __name: "UserSelector",
  props: {
    modelValue: {
      type: Number,
      default: 0
    },
    label: {
      type: String,
      default: "选择用户"
    },
    labelWidth: {
      type: String,
      default: "160rpx"
    },
    placeholder: {
      type: String,
      default: "请选择用户"
    },
    required: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  emits: ["update:modelValue", "change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const showModal = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const searchKeyword = common_vendor.ref("");
    const selectedUser = common_vendor.ref(null);
    const availableUsers = common_vendor.ref([]);
    common_vendor.watch(() => props.modelValue, (newId) => __async(this, null, function* () {
      if (newId && newId > 0) {
        try {
          const res = yield api_user.getSelectableProfile(newId);
          selectedUser.value = res.data;
        } catch (error) {
          console.error("获取用户信息失败:", error);
          selectedUser.value = null;
        }
      } else {
        selectedUser.value = null;
      }
    }), { immediate: true });
    function initializeData() {
      return __async(this, null, function* () {
        try {
          isLoading.value = true;
          const res = yield api_user.getSelectableList({
            search: searchKeyword.value || void 0,
            page: 1,
            pageSize: 200
          });
          availableUsers.value = res.data.list;
        } catch (error) {
          console.error("获取用户列表失败:", error);
          common_vendor.index.showToast({ title: "数据加载失败，请重试", icon: "error" });
          availableUsers.value = [];
        } finally {
          isLoading.value = false;
        }
      });
    }
    function showUserPicker() {
      if (!availableUsers.value.length) {
        initializeData();
      }
      showModal.value = true;
    }
    function searchUsers() {
      initializeData();
    }
    function handleUserSelection(user) {
      selectedUser.value = user;
      showModal.value = false;
      emit("update:modelValue", user.ID);
      emit("change", user);
    }
    function removeUser() {
      selectedUser.value = null;
      emit("update:modelValue", 0);
      emit("change", null);
    }
    const displayValue = common_vendor.computed(() => {
      if (selectedUser.value) {
        return `${selectedUser.value.nickName} (${selectedUser.value.phone})`;
      }
      return "";
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {
        b: common_vendor.p({
          name: "loading",
          ["custom-class"]: "loading-icon"
        })
      } : {
        c: common_vendor.p({
          name: "arrow-right",
          ["custom-class"]: "arrow-icon"
        })
      }, {
        d: common_vendor.o(showUserPicker),
        e: common_vendor.o(removeUser),
        f: common_vendor.p({
          ["model-value"]: displayValue.value,
          label: __props.label,
          ["label-width"]: __props.labelWidth,
          placeholder: __props.placeholder,
          required: __props.required,
          disabled: __props.disabled,
          clearable: __props.clearable && !!selectedUser.value,
          readonly: true
        }),
        g: common_vendor.t(__props.label),
        h: common_vendor.o(searchUsers),
        i: common_vendor.o(($event) => searchKeyword.value = $event),
        j: common_vendor.p({
          placeholder: "搜索用户",
          ["custom-class"]: "dj-search",
          modelValue: searchKeyword.value
        }),
        k: common_vendor.f(availableUsers.value, (user, k0, i0) => {
          var _a;
          return {
            a: common_vendor.t(user.nickName),
            b: common_vendor.t(user.phone),
            c: "d36d9223-5-" + i0 + ",d36d9223-3",
            d: common_vendor.p({
              value: ((_a = selectedUser.value) == null ? void 0 : _a.ID) === user.ID,
              ["custom-class"]: "dj-radio"
            }),
            e: user.ID,
            f: common_vendor.o(($event) => handleUserSelection(user), user.ID)
          };
        }),
        l: availableUsers.value.length === 0 && !isLoading.value
      }, availableUsers.value.length === 0 && !isLoading.value ? {} : {}, {
        m: isLoading.value
      }, isLoading.value ? {} : {}, {
        n: common_vendor.o(($event) => showModal.value = $event),
        o: common_vendor.p({
          position: "bottom",
          ["custom-style"]: "height: 70%",
          ["custom-class"]: "dj-popup",
          modelValue: showModal.value
        })
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d36d9223"]]);
wx.createComponent(Component);
