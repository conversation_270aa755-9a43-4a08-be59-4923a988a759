<layout-default-uni class="data-v-e50356ae" u-s="{{['d']}}" u-i="e50356ae-0" bind:__l="__l"><view class="test-page data-v-e50356ae"><view class="header data-v-e50356ae"><text class="title data-v-e50356ae">Instrument Store 测试</text></view><view class="status-section data-v-e50356ae"><text class="section-title data-v-e50356ae">Store状态</text><view class="status-item data-v-e50356ae"><text class="data-v-e50356ae">已加载: {{a}}</text></view><view class="status-item data-v-e50356ae"><text class="data-v-e50356ae">加载中: {{b}}</text></view><view class="status-item data-v-e50356ae"><text class="data-v-e50356ae">数据新鲜: {{c}}</text></view><view class="status-item data-v-e50356ae"><text class="data-v-e50356ae">合约数量: {{d}}</text></view><view wx:if="{{e}}" class="status-item error data-v-e50356ae"><text class="data-v-e50356ae">错误: {{f}}</text></view></view><view class="button-section data-v-e50356ae"><wd-button wx:if="{{i}}" class="data-v-e50356ae" u-s="{{['d']}}" bindclick="{{h}}" u-i="e50356ae-1,e50356ae-0" bind:__l="__l" u-p="{{i}}">{{g}}</wd-button><wd-button wx:if="{{l}}" class="data-v-e50356ae" u-s="{{['d']}}" bindclick="{{k}}" u-i="e50356ae-2,e50356ae-0" bind:__l="__l" u-p="{{l}}">{{j}}</wd-button><wd-button wx:if="{{n}}" class="data-v-e50356ae" u-s="{{['d']}}" bindclick="{{m}}" u-i="e50356ae-3,e50356ae-0" bind:__l="__l" u-p="{{n}}"> 清空结果 </wd-button></view><view class="results-section data-v-e50356ae"><text class="section-title data-v-e50356ae">测试结果</text><view class="results-container data-v-e50356ae"><view wx:for="{{o}}" wx:for-item="result" wx:key="b" class="{{['result-item', 'data-v-e50356ae', result.c && 'error']}}"><text class="result-text data-v-e50356ae">{{result.a}}</text></view><view wx:if="{{p}}" class="empty-results data-v-e50356ae"><text class="data-v-e50356ae">暂无测试结果</text></view></view></view></view></layout-default-uni>