{"libVersion": "3.8.12", "projectname": "unibest", "condition": {"miniprogram": {"list": [{"name": "登录界面", "pathName": "pages/login/index", "query": "", "scene": null, "launchMode": "default"}]}}, "setting": {"urlCheck": false, "coverView": false, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": false, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": true}}