/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.cancel-dialog.data-v-e269c6f4 {
  width: 600rpx;
  background: white;
  padding: 40rpx;
}
.cancel-dialog .dialog-header.data-v-e269c6f4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.cancel-dialog .dialog-header .dialog-title.data-v-e269c6f4 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.cancel-dialog .contract-info.data-v-e269c6f4 {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
}
.cancel-dialog .contract-info .info-item.data-v-e269c6f4 {
  display: flex;
  margin-bottom: 12rpx;
}
.cancel-dialog .contract-info .info-item.data-v-e269c6f4:last-child {
  margin-bottom: 0;
}
.cancel-dialog .contract-info .info-item .label.data-v-e269c6f4 {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}
.cancel-dialog .contract-info .info-item .value.data-v-e269c6f4 {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}
.cancel-dialog .quantity-section.data-v-e269c6f4, .cancel-dialog .reason-section.data-v-e269c6f4 {
  margin-bottom: 30rpx;
}
.cancel-dialog .quantity-section .section-title.data-v-e269c6f4, .cancel-dialog .reason-section .section-title.data-v-e269c6f4 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.cancel-dialog .quantity-section .quantity-input-row.data-v-e269c6f4, .cancel-dialog .reason-section .quantity-input-row.data-v-e269c6f4 {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.cancel-dialog .quantity-section .quantity-tip.data-v-e269c6f4, .cancel-dialog .reason-section .quantity-tip.data-v-e269c6f4 {
  font-size: 24rpx;
  color: #999;
}
.cancel-dialog .dialog-actions.data-v-e269c6f4 {
  display: flex;
  justify-content: flex-end;
  margin-top: 40rpx;
}