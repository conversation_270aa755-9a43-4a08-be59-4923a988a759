"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toast = require("../../utils/toast.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (UserSelector + _easycom_wd_button)();
}
const UserSelector = () => "../../components/UserSelector.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "test-user-selector",
  setup(__props) {
    const selectedUserId = common_vendor.ref(0);
    const selectedUserInfo = common_vendor.ref(null);
    function handleUserChange(user) {
      selectedUserInfo.value = user;
      if (user) {
        utils_toast.toast.success(`选择了用户：${user.nickName}`);
      } else {
        utils_toast.toast.info("已清除用户选择");
      }
    }
    function resetSelection() {
      selectedUserId.value = 0;
      selectedUserInfo.value = null;
    }
    function simulateSetUser() {
      selectedUserId.value = 1;
    }
    return (_ctx, _cache) => {
      var _a, _b;
      return {
        a: common_vendor.o(handleUserChange),
        b: common_vendor.o(($event) => selectedUserId.value = $event),
        c: common_vendor.p({
          label: "选择用户",
          placeholder: "请选择一个用户",
          modelValue: selectedUserId.value
        }),
        d: common_vendor.t(selectedUserId.value || "未选择"),
        e: common_vendor.t(((_a = selectedUserInfo.value) == null ? void 0 : _a.nickName) || "未选择"),
        f: common_vendor.t(((_b = selectedUserInfo.value) == null ? void 0 : _b.phone) || "未选择"),
        g: common_vendor.o(resetSelection),
        h: common_vendor.p({
          type: "info",
          size: "small"
        }),
        i: common_vendor.o(simulateSetUser),
        j: common_vendor.p({
          type: "primary",
          size: "small"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3f647531"]]);
wx.createPage(MiniProgramPage);
