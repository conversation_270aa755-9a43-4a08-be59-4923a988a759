"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
const __default__ = {
  name: "wd-img",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.imgProps,
  emits: ["error", "click", "load"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const rootStyle = common_vendor.computed(() => {
      const style = {};
      if (common_vendor.isDef(props.height)) {
        style["height"] = common_vendor.addUnit(props.height);
      }
      if (common_vendor.isDef(props.width)) {
        style["width"] = common_vendor.addUnit(props.width);
      }
      if (common_vendor.isDef(props.radius)) {
        style["border-radius"] = common_vendor.addUnit(props.radius);
        style["overflow"] = "hidden";
      }
      return `${common_vendor.objToStyle(style)}${props.customStyle}`;
    });
    const rootClass = common_vendor.computed(() => {
      return `wd-img  ${props.round ? "is-round" : ""} ${props.customClass}`;
    });
    const status = common_vendor.ref("loading");
    function handleError(event) {
      status.value = "error";
      emit("error", event);
    }
    function handleClick(event) {
      if (props.enablePreview && props.src && status.value == "success") {
        common_vendor.index.previewImage({
          urls: [props.previewSrc || props.src]
        });
      }
      emit("click", event);
    }
    function handleLoad(event) {
      status.value = "success";
      emit("load", event);
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.n(`wd-img__image ${_ctx.customImage}`),
        b: common_vendor.s(status.value !== "success" ? "width: 0;height: 0;" : ""),
        c: _ctx.src,
        d: _ctx.mode,
        e: _ctx.showMenuByLongpress,
        f: _ctx.lazyLoad,
        g: common_vendor.o(handleLoad),
        h: common_vendor.o(handleError),
        i: status.value === "loading"
      }, status.value === "loading" ? {} : {}, {
        j: status.value === "error"
      }, status.value === "error" ? {} : {}, {
        k: common_vendor.n(rootClass.value),
        l: common_vendor.o(handleClick),
        m: common_vendor.s(rootStyle.value)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2cf12824"]]);
wx.createComponent(Component);
