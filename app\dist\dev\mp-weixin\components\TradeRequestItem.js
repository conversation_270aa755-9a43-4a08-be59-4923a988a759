"use strict";
const common_vendor = require("../common/vendor.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  _easycom_wd_button2();
}
const _easycom_wd_button = () => "../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  _easycom_wd_button();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "TradeRequestItem",
  props: {
    request: {},
    mode: { default: void 0 },
    isSetterMode: { type: Boolean, default: false }
  },
  emits: ["fill", "reject", "convertToSimulation", "convertToTrade", "cancel"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    function getTradeRequestStatusText(status) {
      const statusMap = {
        Executing: "执行中",
        Completed: "已完成",
        Rejected: "已拒绝",
        Cancelled: "已取消",
        Expired: "已过期"
      };
      return statusMap[status] || status;
    }
    function getTradeRequestStatusClass(status) {
      const statusClassMap = {
        Executing: "text-blue-500 bg-blue-50",
        Completed: "text-green-500 bg-green-50",
        Rejected: "text-red-600 bg-red-50",
        Cancelled: "text-gray-500 bg-gray-50",
        Expired: "text-orange-500 bg-orange-50"
      };
      return statusClassMap[status] || "text-gray-500 bg-gray-50";
    }
    function getExecutionModeText(mode) {
      const modeMap = {
        AUTOMATIC: "自动",
        MANUAL: "手动",
        SIMULATED: "模拟"
      };
      return modeMap[mode] || mode;
    }
    function getExecutionModeClass(mode) {
      const modeClassMap = {
        AUTOMATIC: "text-purple-500 bg-purple-50",
        MANUAL: "text-blue-500 bg-blue-50",
        SIMULATED: "text-orange-500 bg-orange-50"
      };
      return modeClassMap[mode] || "text-gray-500 bg-gray-50";
    }
    const formatTime = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit"
      });
    };
    const formatDateTime = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit"
      });
    };
    const requestTypeText = common_vendor.computed(() => {
      return props.request.requestType === "PointPrice" ? "点价" : "洗基差";
    });
    const createTime = common_vendor.computed(() => {
      return formatTime(props.request.CreatedAt);
    });
    const statusText = common_vendor.computed(() => {
      return getTradeRequestStatusText(props.request.status);
    });
    const statusClass = common_vendor.computed(() => {
      return getTradeRequestStatusClass(props.request.status);
    });
    const executionModeText = common_vendor.computed(() => {
      return getExecutionModeText(props.request.executionMode);
    });
    const executionModeClass = common_vendor.computed(() => {
      return getExecutionModeClass(props.request.executionMode);
    });
    const remainingQuantity = common_vendor.computed(() => {
      return props.request.requestedQuantity - props.request.executedQuantity;
    });
    const actualMode = common_vendor.computed(() => {
      if (props.mode) {
        return props.mode;
      }
      return props.isSetterMode ? "setter" : "viewer";
    });
    const canOperate = common_vendor.computed(() => {
      return props.request.status === "Executing";
    });
    const isManualMode = common_vendor.computed(() => {
      return props.request.executionMode === "MANUAL";
    });
    const isAutoOrSimMode = common_vendor.computed(() => {
      return props.request.executionMode === "AUTOMATIC" || props.request.executionMode === "SIMULATED";
    });
    const handleFill = () => {
      emit("fill", props.request);
    };
    const handleReject = () => {
      emit("reject", props.request);
    };
    const handleConvertToSimulation = () => {
      emit("convertToSimulation", props.request);
    };
    const handleConvertToTrade = () => {
      emit("convertToTrade", props.request);
    };
    const handleCancel = () => {
      emit("cancel", props.request);
    };
    const hasExecuted = common_vendor.computed(() => {
      return props.request.executedQuantity > 0;
    });
    const expiresTime = common_vendor.computed(() => {
      if (!props.request.expiresAt)
        return null;
      return formatDateTime(props.request.expiresAt);
    });
    const isExpiringSoon = common_vendor.computed(() => {
      if (!props.request.expiresAt || props.request.status !== "Executing")
        return false;
      const expireTime = new Date(props.request.expiresAt);
      const now = /* @__PURE__ */ new Date();
      const timeDiff = expireTime.getTime() - now.getTime();
      return timeDiff > 0 && timeDiff <= 60 * 60 * 1e3;
    });
    return (_ctx, _cache) => {
      var _a, _b, _c;
      return common_vendor.e({
        a: common_vendor.t(requestTypeText.value),
        b: actualMode.value !== "viewer"
      }, actualMode.value !== "viewer" ? {
        c: common_vendor.t(_ctx.request.ID)
      } : {}, {
        d: common_vendor.t(createTime.value),
        e: actualMode.value !== "viewer"
      }, actualMode.value !== "viewer" ? {
        f: common_vendor.t(executionModeText.value),
        g: common_vendor.n(executionModeClass.value)
      } : {}, {
        h: common_vendor.t(statusText.value),
        i: common_vendor.n(statusClass.value),
        j: actualMode.value === "setter"
      }, actualMode.value === "setter" ? {
        k: common_vendor.t(((_a = _ctx.request.pricer) == null ? void 0 : _a.nickName) || ((_b = _ctx.request.pricer) == null ? void 0 : _b.userName) || `用户${_ctx.request.pricerID}`),
        l: common_vendor.t(((_c = _ctx.request.instrument) == null ? void 0 : _c.instrument_name) || `合约${_ctx.request.instrumentRefID}`)
      } : {}, {
        m: common_vendor.t(_ctx.request.requestedQuantity),
        n: _ctx.request.requestedPrice
      }, _ctx.request.requestedPrice ? {
        o: common_vendor.t(_ctx.request.requestedPrice.toFixed(2))
      } : {}, {
        p: hasExecuted.value
      }, hasExecuted.value ? {
        q: common_vendor.t(_ctx.request.executedQuantity),
        r: common_vendor.t(_ctx.request.executedPrice.toFixed(2))
      } : {}, {
        s: actualMode.value === "setter" && canOperate.value && remainingQuantity.value > 0
      }, actualMode.value === "setter" && canOperate.value && remainingQuantity.value > 0 ? {
        t: common_vendor.t(remainingQuantity.value)
      } : {}, {
        v: actualMode.value !== "setter"
      }, actualMode.value !== "setter" ? {
        w: common_vendor.t(executionModeText.value)
      } : {}, {
        x: _ctx.request.status === "Rejected" && _ctx.request.rejectionReason
      }, _ctx.request.status === "Rejected" && _ctx.request.rejectionReason ? {
        y: common_vendor.t(_ctx.request.rejectionReason)
      } : {}, {
        z: _ctx.request.status === "Executing" && expiresTime.value
      }, _ctx.request.status === "Executing" && expiresTime.value ? common_vendor.e({
        A: isExpiringSoon.value ? 1 : "",
        B: common_vendor.t(expiresTime.value),
        C: isExpiringSoon.value ? 1 : "",
        D: isExpiringSoon.value
      }, isExpiringSoon.value ? {} : {}) : {}, {
        E: _ctx.request.notes
      }, _ctx.request.notes ? {
        F: common_vendor.t(_ctx.request.notes)
      } : {}, {
        G: canOperate.value
      }, canOperate.value ? common_vendor.e({
        H: actualMode.value === "pricer"
      }, actualMode.value === "pricer" ? {
        I: common_vendor.o(handleCancel),
        J: common_vendor.p({
          type: "warning",
          size: "small"
        })
      } : actualMode.value === "setter" ? common_vendor.e({
        L: isManualMode.value
      }, isManualMode.value ? {
        M: common_vendor.o(handleFill),
        N: common_vendor.p({
          type: "success",
          size: "small"
        }),
        O: common_vendor.o(handleReject),
        P: common_vendor.p({
          type: "error",
          size: "small"
        }),
        Q: common_vendor.o(handleConvertToSimulation),
        R: common_vendor.p({
          type: "warning",
          size: "small",
          disabled: true
        }),
        S: common_vendor.o(handleConvertToTrade),
        T: common_vendor.p({
          type: "primary",
          size: "small",
          disabled: true
        })
      } : isAutoOrSimMode.value ? {
        V: common_vendor.o(handleReject),
        W: common_vendor.p({
          type: "error",
          size: "small"
        })
      } : {}, {
        U: isAutoOrSimMode.value
      }) : {}, {
        K: actualMode.value === "setter"
      }) : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0e90c9d7"]]);
wx.createComponent(Component);
