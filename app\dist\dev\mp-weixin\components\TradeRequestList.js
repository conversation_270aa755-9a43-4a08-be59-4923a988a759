"use strict";
const common_vendor = require("../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
if (!Math) {
  TradeRequestItem();
}
const TradeRequestItem = () => "./TradeRequestItem.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "TradeRequestList",
  props: {
    requests: {},
    requestType: {},
    loading: { type: Boolean, default: false },
    refreshing: { type: Boolean, default: false },
    mode: { default: void 0 },
    isSetterMode: { type: Boolean, default: false }
  },
  emits: ["fill", "reject", "convertToSimulation", "convertToTrade", "cancel", "refresh"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const actualMode = common_vendor.computed(() => {
      if (props.mode) {
        return props.mode;
      }
      return props.isSetterMode ? "setter" : "viewer";
    });
    const isPointPrice = common_vendor.computed(() => {
      return props.requestType === "PointPrice";
    });
    common_vendor.computed(() => {
      if (actualMode.value === "setter") {
        return "交易请求管理";
      } else if (actualMode.value === "pricer") {
        return "我的交易请求";
      } else if (actualMode.value === "viewer") {
        return "交易请求查看";
      }
      return `当日${isPointPrice.value ? "点价" : "洗基差"}请求`;
    });
    const isEmpty = common_vendor.computed(() => {
      return !props.loading && props.requests.length === 0;
    });
    const emptyMessage = common_vendor.computed(() => {
      if (actualMode.value === "setter") {
        return "暂无交易请求";
      } else if (actualMode.value === "pricer") {
        return "暂无发起的交易请求";
      } else if (actualMode.value === "viewer") {
        return "暂无交易记录";
      }
      return `暂无${isPointPrice.value ? "点价" : "洗基差"}记录`;
    });
    common_vendor.computed(() => {
      if (actualMode.value !== "setter" && actualMode.value !== "pricer")
        return 0;
      return props.requests.filter((req) => req.status === "Executing").length;
    });
    const handleFill = (request) => {
      if (actualMode.value === "setter") {
        emit("fill", request);
      }
    };
    const handleReject = (request) => {
      if (actualMode.value === "setter") {
        emit("reject", request);
      }
    };
    const handleConvertToSimulation = (request) => {
      if (actualMode.value === "setter") {
        emit("convertToSimulation", request);
      }
    };
    const handleConvertToTrade = (request) => {
      if (actualMode.value === "setter") {
        emit("convertToTrade", request);
      }
    };
    const handleCancel = (request) => {
      if (actualMode.value === "pricer") {
        emit("cancel", request);
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: _ctx.loading
      }, _ctx.loading ? {} : isEmpty.value ? {
        c: common_vendor.p({
          d: "M20 6h-2.18c.11-.31.18-.65.18-1a2.996 2.996 0 0 0-5.5-1.65l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"
        }),
        d: common_vendor.p({
          viewBox: "0 0 24 24",
          fill: "currentColor"
        }),
        e: common_vendor.t(emptyMessage.value)
      } : {
        f: common_vendor.f(_ctx.requests, (request, k0, i0) => {
          return {
            a: request.ID,
            b: common_vendor.o(handleFill, request.ID),
            c: common_vendor.o(handleReject, request.ID),
            d: common_vendor.o(handleConvertToSimulation, request.ID),
            e: common_vendor.o(handleConvertToTrade, request.ID),
            f: common_vendor.o(handleCancel, request.ID),
            g: "a812eeaf-2-" + i0,
            h: common_vendor.p({
              request,
              mode: actualMode.value
            })
          };
        })
      }, {
        b: isEmpty.value
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a812eeaf"]]);
wx.createComponent(Component);
