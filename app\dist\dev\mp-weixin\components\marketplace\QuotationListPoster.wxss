/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.poster-generator.data-v-ab939cb2 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}
.loading-overlay.data-v-ab939cb2 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  -webkit-backdrop-filter: blur(4rpx);
          backdrop-filter: blur(4rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}
.loading-content.data-v-ab939cb2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 60rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.loading-content .loading-text.data-v-ab939cb2 {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #606266;
  font-weight: 500;
}
.poster-content.data-v-ab939cb2 {
  position: fixed;
  left: -9999rpx;
  top: -9999rpx;
  visibility: hidden;
  opacity: 0;
  z-index: -1;
}
.preview-popup.data-v-ab939cb2 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-overlay.data-v-ab939cb2 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(8rpx);
          backdrop-filter: blur(8rpx);
}
.preview-wrapper.data-v-ab939cb2 {
  position: relative;
  z-index: 10001;
  width: 90%;
  height: 90%;
  max-width: 800rpx;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}
.preview-scroll.data-v-ab939cb2 {
  flex: 1;
  overflow: hidden;
}
.preview-image-container.data-v-ab939cb2 {
  padding: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%;
}
.preview-full-image.data-v-ab939cb2 {
  width: 100%;
  max-width: 100%;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.preview-actions.data-v-ab939cb2 {
  display: flex;
  gap: 24rpx;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.98);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}
.preview-actions.data-v-ab939cb2 .preview-action-btn {
  flex: 1;
  border-radius: 50rpx;
  font-weight: 500;
}