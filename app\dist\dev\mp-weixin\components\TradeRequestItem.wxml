<view class="trade-request-item card bg-white rounded-lg shadow-sm p-4 mb-3 data-v-0e90c9d7"><view class="card-header flex justify-between items-center mb-3 data-v-0e90c9d7"><view class="request-info data-v-0e90c9d7"><text class="request-type text-base font-semibold text-gray-800 data-v-0e90c9d7">{{a}}</text><text wx:if="{{b}}" class="request-id text-xs text-gray-500 ml-2 data-v-0e90c9d7">#{{c}}</text><text class="request-time text-xs text-gray-500 ml-2 data-v-0e90c9d7">{{d}}</text></view><view class="badges flex gap-2 data-v-0e90c9d7"><view wx:if="{{e}}" class="{{['execution-mode-badge', 'px-2', 'py-1', 'rounded-full', 'text-xs', 'font-medium', 'data-v-0e90c9d7', g]}}">{{f}}</view><view class="{{['status-badge', 'px-2', 'py-1', 'rounded-full', 'text-xs', 'font-medium', 'data-v-0e90c9d7', i]}}">{{h}}</view></view></view><view class="card-body mb-3 data-v-0e90c9d7"><view wx:if="{{j}}" class="basic-info grid grid-cols-2 gap-4 mb-3 data-v-0e90c9d7"><view class="info-item data-v-0e90c9d7"><text class="info-label text-xs text-gray-500 block mb-1 data-v-0e90c9d7">点价方</text><text class="info-value text-sm font-medium text-gray-800 data-v-0e90c9d7">{{k}}</text></view><view class="info-item data-v-0e90c9d7"><text class="info-label text-xs text-gray-500 block mb-1 data-v-0e90c9d7">期货合约</text><text class="info-value text-sm font-medium text-gray-800 data-v-0e90c9d7">{{l}}</text></view></view><view class="trade-info grid grid-cols-2 gap-4 data-v-0e90c9d7"><view class="request-section data-v-0e90c9d7"><text class="section-label text-xs text-gray-500 block mb-1 data-v-0e90c9d7">请求</text><view class="section-content data-v-0e90c9d7"><text class="quantity text-sm font-medium text-gray-800 data-v-0e90c9d7">{{m}} 手</text><text wx:if="{{n}}" class="price text-sm text-gray-600 ml-1 data-v-0e90c9d7"> @ {{o}}</text></view></view><view class="execution-section data-v-0e90c9d7"><text class="section-label text-xs text-gray-500 block mb-1 data-v-0e90c9d7">成交</text><view class="section-content data-v-0e90c9d7"><view wx:if="{{p}}" class="data-v-0e90c9d7"><text class="quantity text-sm font-medium text-gray-800 data-v-0e90c9d7">{{q}} 手</text><text class="price text-sm text-gray-600 ml-1 data-v-0e90c9d7">@ {{r}}</text></view><text wx:else class="no-execution text-sm text-gray-400 data-v-0e90c9d7">-</text></view><view wx:if="{{s}}" class="remaining text-xs text-orange-500 mt-1 data-v-0e90c9d7"> 剩余 {{t}} 手 </view></view></view></view><view class="card-footer data-v-0e90c9d7"><view class="additional-info flex flex-wrap gap-4 text-xs text-gray-500 mb-3 data-v-0e90c9d7"><view wx:if="{{v}}" class="execution-mode data-v-0e90c9d7"><text class="info-label data-v-0e90c9d7">执行模式:</text><text class="info-value ml-1 data-v-0e90c9d7">{{w}}</text></view><view wx:if="{{x}}" class="rejection-reason data-v-0e90c9d7"><text class="info-label text-red-500 data-v-0e90c9d7">拒绝原因:</text><text class="info-value ml-1 text-red-500 data-v-0e90c9d7">{{y}}</text></view><view wx:if="{{z}}" class="expires-time data-v-0e90c9d7"><text class="{{['info-label', 'data-v-0e90c9d7', A && 'text-orange-500']}}">过期时间:</text><text class="{{['info-value', 'ml-1', 'data-v-0e90c9d7', C && 'text-orange-500']}}">{{B}}</text><text wx:if="{{D}}" class="warning-text text-orange-500 ml-1 data-v-0e90c9d7">(即将过期)</text></view></view><view wx:if="{{E}}" class="notes mb-3 data-v-0e90c9d7"><text class="info-label text-xs text-gray-500 data-v-0e90c9d7">备注:</text><text class="info-value text-xs text-gray-600 ml-1 data-v-0e90c9d7">{{F}}</text></view><view wx:if="{{G}}" class="action-buttons data-v-0e90c9d7"><view wx:if="{{H}}" class="pricer-actions flex gap-2 data-v-0e90c9d7"><wd-button wx:if="{{J}}" class="data-v-0e90c9d7" u-s="{{['d']}}" bindclick="{{I}}" u-i="0e90c9d7-0" bind:__l="__l" u-p="{{J}}"> 取消 </wd-button></view><block wx:elif="{{K}}"><view wx:if="{{L}}" class="manual-actions flex gap-2 flex-wrap data-v-0e90c9d7"><wd-button wx:if="{{N}}" class="data-v-0e90c9d7" u-s="{{['d']}}" bindclick="{{M}}" u-i="0e90c9d7-1" bind:__l="__l" u-p="{{N}}"> 成交 </wd-button><wd-button wx:if="{{P}}" class="data-v-0e90c9d7" u-s="{{['d']}}" bindclick="{{O}}" u-i="0e90c9d7-2" bind:__l="__l" u-p="{{P}}"> 拒绝 </wd-button><wd-button wx:if="{{R}}" class="data-v-0e90c9d7" u-s="{{['d']}}" bindclick="{{Q}}" u-i="0e90c9d7-3" bind:__l="__l" u-p="{{R}}"> 转模拟 </wd-button><wd-button wx:if="{{T}}" class="data-v-0e90c9d7" u-s="{{['d']}}" bindclick="{{S}}" u-i="0e90c9d7-4" bind:__l="__l" u-p="{{T}}"> 转交易 </wd-button></view><view wx:elif="{{U}}" class="auto-sim-actions flex gap-2 data-v-0e90c9d7"><wd-button wx:if="{{W}}" class="data-v-0e90c9d7" u-s="{{['d']}}" bindclick="{{V}}" u-i="0e90c9d7-5" bind:__l="__l" u-p="{{W}}"> 拒绝 </wd-button></view></block></view></view></view>