<layout-default-uni class="data-v-f7b919ed" u-s="{{['d']}}" u-i="f7b919ed-0" bind:__l="__l"><view class="page-container gradient-bg-primary data-v-f7b919ed"><view class="page-header data-v-f7b919ed"><text class="page-title data-v-f7b919ed">合同管理</text><view class="header-actions data-v-f7b919ed"><view class="view-switch data-v-f7b919ed"><wd-button wx:if="{{b}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindclick="{{a}}" u-i="f7b919ed-1,f7b919ed-0" bind:__l="__l" u-p="{{b}}"> 汇总视图 </wd-button><wd-button wx:if="{{d}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindclick="{{c}}" u-i="f7b919ed-2,f7b919ed-0" bind:__l="__l" u-p="{{d}}"> 明细视图 </wd-button></view><wd-button wx:if="{{f}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindclick="{{e}}" u-i="f7b919ed-3,f7b919ed-0" bind:__l="__l" u-p="{{f}}"><text class="create-icon data-v-f7b919ed">+</text> 新建合同 </wd-button></view></view><contract-summary wx:if="{{g}}" class="data-v-f7b919ed" bindinstrumentClick="{{h}}" u-i="f7b919ed-4,f7b919ed-0" bind:__l="__l" u-p="{{i}}"/><view wx:if="{{j}}" class="filter-tabs data-v-f7b919ed"><wd-tabs wx:if="{{n}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindchange="{{l}}" u-i="f7b919ed-5,f7b919ed-0" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"><wd-tab wx:for="{{k}}" wx:for-item="tab" wx:key="a" class="data-v-f7b919ed" u-i="{{tab.b}}" bind:__l="__l" u-p="{{tab.c}}"></wd-tab></wd-tabs></view><scroll-view wx:if="{{o}}" class="scroll-container data-v-f7b919ed" scroll-y refresher-enabled refresher-triggered="{{x}}" bindrefresherrefresh="{{y}}" bindscrolltolower="{{z}}"><view class="contract-list data-v-f7b919ed"><contract-card wx:for="{{p}}" wx:for-item="contract" wx:key="b" class="data-v-f7b919ed" u-s="{{['actions']}}" bindclick="{{contract.c}}" u-i="{{contract.d}}" bind:__l="__l" u-p="{{contract.e}}"><view wx:for="{{contract.a}}" wx:for-item="v1" wx:key="A" slot="{{v1.B}}"><view class="contract-actions data-v-f7b919ed" catchtap="{{v1.z}}"><wd-button wx:if="{{v1.a}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindclick="{{v1.b}}" u-i="{{v1.c}}" bind:__l="__l" u-p="{{v1.d}}"> 激活 </wd-button><wd-button wx:if="{{v1.e}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindclick="{{v1.f}}" u-i="{{v1.g}}" bind:__l="__l" u-p="{{v1.h}}"> 挂起 </wd-button><wd-button wx:if="{{v1.i}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindclick="{{v1.j}}" u-i="{{v1.k}}" bind:__l="__l" u-p="{{v1.l}}"> 取消 </wd-button><wd-button wx:if="{{v1.m}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindclick="{{v1.n}}" u-i="{{v1.o}}" bind:__l="__l" u-p="{{v1.p}}"> 编辑 </wd-button><wd-button wx:if="{{v1.q}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindclick="{{v1.r}}" u-i="{{v1.s}}" bind:__l="__l" u-p="{{v1.t}}"> 删除 </wd-button><wd-button wx:if="{{v1.v}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindclick="{{v1.w}}" u-i="{{v1.x}}" bind:__l="__l" u-p="{{v1.y}}"> 取消记录 </wd-button></view></view></contract-card></view><view wx:if="{{q}}" class="empty-state data-v-f7b919ed"><text class="data-v-f7b919ed">暂无合同数据</text><wd-button wx:if="{{s}}" class="data-v-f7b919ed" u-s="{{['d']}}" bindclick="{{r}}" u-i="f7b919ed-14,f7b919ed-0" bind:__l="__l" u-p="{{s}}">创建第一个合同</wd-button></view><view wx:if="{{t}}" class="loading-more data-v-f7b919ed"><wd-loading wx:if="{{v}}" class="data-v-f7b919ed" u-i="f7b919ed-15,f7b919ed-0" bind:__l="__l" u-p="{{v}}"/><text class="data-v-f7b919ed">加载中...</text></view><view wx:if="{{w}}" class="no-more data-v-f7b919ed"><text class="data-v-f7b919ed">没有更多数据了</text></view></scroll-view><view wx:if="{{A}}" class="loading-state data-v-f7b919ed"><wd-loading class="data-v-f7b919ed" u-i="f7b919ed-16,f7b919ed-0" bind:__l="__l"/><text class="data-v-f7b919ed">加载中...</text></view></view><cancel-contract-dialog wx:if="{{D}}" class="data-v-f7b919ed" bindconfirm="{{B}}" u-i="f7b919ed-17,f7b919ed-0" bind:__l="__l" bindupdateModelValue="{{C}}" u-p="{{D}}"/></layout-default-uni>