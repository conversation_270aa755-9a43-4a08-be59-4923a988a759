/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.select-input-wrapper.data-v-01d49153 {
  position: relative;
  z-index: 1;
}
.select-input-wrapper.dropdown-active.data-v-01d49153 {
  z-index: 9998;
}
.select-input-wrapper .dropdown-container.data-v-01d49153 {
  position: absolute;
  top: calc(100% + 4rpx);
  left: var(--label-width, 160rpx);
  right: 0;
  z-index: 9999;
  background: #f8f9fa;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  max-height: 400rpx;
  overflow-y: auto;
}
.select-input-wrapper .dropdown-container .dropdown-content .dropdown-item.data-v-01d49153 {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}
.select-input-wrapper .dropdown-container .dropdown-content .dropdown-item.data-v-01d49153:hover {
  background-color: #f0f9ff;
  border-left: 3rpx solid #667eea;
}
.select-input-wrapper .dropdown-container .dropdown-content .dropdown-item.data-v-01d49153:first-child {
  border-radius: 8rpx 8rpx 0 0;
}
.select-input-wrapper .dropdown-container .dropdown-content .dropdown-item.data-v-01d49153:last-child {
  border-bottom: none;
  border-radius: 0 0 8rpx 8rpx;
}
.select-input-wrapper .dropdown-container .dropdown-content .dropdown-item .option-label.data-v-01d49153 {
  font-size: 28rpx;
  color: #303133;
  line-height: 1.4;
  display: block;
  font-weight: 500;
}
.select-input-wrapper .dropdown-container .dropdown-content .dropdown-item .option-description.data-v-01d49153 {
  font-size: 24rpx;
  color: #606266;
  margin-top: 8rpx;
  display: block;
  line-height: 1.3;
}