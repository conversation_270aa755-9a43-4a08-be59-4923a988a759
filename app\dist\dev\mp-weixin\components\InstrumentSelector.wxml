<view class="instrument-selector-wrapper data-v-c01cf354"><wd-input wx:if="{{f}}" class="data-v-c01cf354" u-s="{{['suffix']}}" bindclick="{{d}}" bindclear="{{e}}" u-i="c01cf354-0" bind:__l="__l" u-p="{{f}}"><view class="input-suffix data-v-c01cf354" slot="suffix"><wd-icon wx:if="{{a}}" class="data-v-c01cf354" u-i="c01cf354-1,c01cf354-0" bind:__l="__l" u-p="{{b}}"/><wd-icon wx:else class="data-v-c01cf354" u-i="c01cf354-2,c01cf354-0" bind:__l="__l" u-p="{{c||''}}"/></view></wd-input><wd-popup wx:if="{{w}}" class="data-v-c01cf354" u-s="{{['d']}}" u-i="c01cf354-3" bind:__l="__l" bindupdateModelValue="{{v}}" u-p="{{w}}"><view class="instrument-picker data-v-c01cf354"><view class="picker-header data-v-c01cf354"><view class="header-content data-v-c01cf354"><wd-button wx:if="{{g}}" class="data-v-c01cf354" u-s="{{['d']}}" bindclick="{{h}}" u-i="c01cf354-4,c01cf354-3" bind:__l="__l" u-p="{{i}}"> 返回 </wd-button><view class="title-section data-v-c01cf354"><text class="picker-title data-v-c01cf354">{{j}}</text><text wx:if="{{k}}" class="breadcrumb data-v-c01cf354">{{l}}</text></view></view></view><view class="picker-content data-v-c01cf354"><wd-search wx:if="{{o}}" class="data-v-c01cf354" bindsearch="{{m}}" u-i="c01cf354-5,c01cf354-3" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"/><view class="instrument-list data-v-c01cf354"><view wx:for="{{p}}" wx:for-item="item" wx:key="e" class="instrument-item data-v-c01cf354" bindtap="{{item.f}}"><view class="item-info data-v-c01cf354"><text class="item-name data-v-c01cf354">{{item.a}}</text><text wx:if="{{q}}" class="item-product data-v-c01cf354">{{item.b}}</text></view><view class="item-arrow data-v-c01cf354"><text wx:if="{{r}}" class="data-v-c01cf354">></text><wd-radio wx:else class="data-v-c01cf354" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d||''}}"/></view></view></view><view wx:if="{{s}}" class="empty-list data-v-c01cf354"><text class="data-v-c01cf354">暂无数据</text></view><view wx:if="{{t}}" class="loading-indicator data-v-c01cf354"><text class="data-v-c01cf354">加载中...</text></view></view></view></wd-popup></view>