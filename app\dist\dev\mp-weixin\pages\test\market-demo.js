"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../store/index.js");
const store_socket = require("../../store/socket.js");
const store_market = require("../../store/market.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "market-demo",
  setup(__props) {
    const socketStore = store_socket.useSocketStore();
    const marketStore = store_market.useMarketStore();
    const symbol = common_vendor.ref("rb2511");
    const exchange = common_vendor.ref("SHFE");
    function subscribeMarket() {
      if (!symbol.value || !exchange.value) {
        common_vendor.index.showToast({
          title: "请输入合约代码和交易所",
          icon: "none"
        });
        return;
      }
      marketStore.subscribe(symbol.value.trim(), exchange.value.trim());
    }
    function unsubscribeMarket() {
      if (!symbol.value) {
        common_vendor.index.showToast({
          title: "请输入合约代码",
          icon: "none"
        });
        return;
      }
      marketStore.unsubscribe(symbol.value.trim());
    }
    function unsubscribeSymbol(symbolToUnsubscribe) {
      marketStore.unsubscribe(symbolToUnsubscribe);
    }
    function formatTime(datetime) {
      if (!datetime || datetime === "None")
        return "--";
      const date = new Date(datetime);
      return date.toLocaleTimeString();
    }
    common_vendor.onMounted(() => {
      if (!socketStore.isConnected) {
        socketStore.connect();
      }
    });
    common_vendor.onUnmounted(() => {
      marketStore.clearAll();
      marketStore.cleanupEventHandlers();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(common_vendor.unref(socketStore).isConnected ? "已连接" : "未连接"),
        b: common_vendor.unref(socketStore).isConnected ? 1 : "",
        c: symbol.value,
        d: common_vendor.o(($event) => symbol.value = $event.detail.value),
        e: exchange.value,
        f: common_vendor.o(($event) => exchange.value = $event.detail.value),
        g: common_vendor.o(subscribeMarket),
        h: !common_vendor.unref(socketStore).isConnected || !symbol.value || !exchange.value,
        i: common_vendor.o(unsubscribeMarket),
        j: !common_vendor.unref(socketStore).isConnected || !symbol.value,
        k: common_vendor.unref(marketStore).getSubscribedSymbols().length === 0
      }, common_vendor.unref(marketStore).getSubscribedSymbols().length === 0 ? {} : {
        l: common_vendor.f(common_vendor.unref(marketStore).getSubscribedSymbols(), (subscribedSymbol, k0, i0) => {
          return {
            a: common_vendor.t(subscribedSymbol),
            b: common_vendor.o(($event) => unsubscribeSymbol(subscribedSymbol), subscribedSymbol),
            c: subscribedSymbol
          };
        })
      }, {
        m: Object.keys(common_vendor.unref(marketStore).getAllMarketData()).length === 0
      }, Object.keys(common_vendor.unref(marketStore).getAllMarketData()).length === 0 ? {} : {
        n: common_vendor.f(common_vendor.unref(marketStore).getAllMarketData(), (data, symbol2, i0) => {
          return {
            a: common_vendor.t(data.symbol),
            b: common_vendor.t(data.exchange),
            c: common_vendor.t(formatTime(data.datetime)),
            d: common_vendor.t(data.last_price),
            e: common_vendor.t(data.last_volume),
            f: common_vendor.t(data.turnover),
            g: common_vendor.t(data.open_interest),
            h: common_vendor.t(data.bid_price_1),
            i: common_vendor.t(data.bid_volume_1),
            j: common_vendor.t(data.ask_price_1),
            k: common_vendor.t(data.ask_volume_1),
            l: symbol2
          };
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-af17903f"]]);
wx.createPage(MiniProgramPage);
