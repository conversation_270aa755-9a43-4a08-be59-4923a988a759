/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.test-page.data-v-e50356ae {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-e50356ae {
  text-align: center;
  margin-bottom: 30rpx;
}
.header .title.data-v-e50356ae {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.status-section.data-v-e50356ae, .results-section.data-v-e50356ae {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.status-section .section-title.data-v-e50356ae, .results-section .section-title.data-v-e50356ae {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.status-item.data-v-e50356ae {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}
.status-item.data-v-e50356ae:last-child {
  border-bottom: none;
}
.status-item.error.data-v-e50356ae {
  color: #ff4757;
}
.status-item text.data-v-e50356ae {
  font-size: 28rpx;
}
.button-section.data-v-e50356ae {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.button-section .test-button.data-v-e50356ae {
  width: 100%;
}
.results-container.data-v-e50356ae {
  max-height: 800rpx;
  overflow-y: auto;
}
.result-item.data-v-e50356ae {
  padding: 15rpx;
  margin-bottom: 10rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #28a745;
}
.result-item.error.data-v-e50356ae {
  border-left-color: #dc3545;
  background: #fff5f5;
}
.result-item .result-text.data-v-e50356ae {
  font-size: 26rpx;
  line-height: 1.4;
  word-break: break-all;
}
.empty-results.data-v-e50356ae {
  text-align: center;
  padding: 40rpx;
  color: #999;
}
.empty-results text.data-v-e50356ae {
  font-size: 28rpx;
}