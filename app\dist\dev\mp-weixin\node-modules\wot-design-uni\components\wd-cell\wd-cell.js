"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  wdIcon();
}
const wdIcon = () => "../wd-icon/wd-icon.js";
const __default__ = {
  name: "wd-cell",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.cellProps,
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const cell = common_vendor.useCell();
    const isBorder = common_vendor.computed(() => {
      return Boolean(common_vendor.isDef(props.border) ? props.border : cell.border.value);
    });
    const { parent: form } = common_vendor.useParent(common_vendor.FORM_KEY);
    const errorMessage = common_vendor.computed(() => {
      if (form && props.prop && form.errorMessages && form.errorMessages[props.prop]) {
        return form.errorMessages[props.prop];
      } else {
        return "";
      }
    });
    const isRequired = common_vendor.computed(() => {
      let formRequired = false;
      if (form && form.props.rules) {
        const rules = form.props.rules;
        for (const key in rules) {
          if (Object.prototype.hasOwnProperty.call(rules, key) && key === props.prop && Array.isArray(rules[key])) {
            formRequired = rules[key].some((rule) => rule.required);
          }
        }
      }
      return props.required || props.rules.some((rule) => rule.required) || formRequired;
    });
    function onClick() {
      const url = props.to;
      if (props.clickable || props.isLink) {
        emit("click");
      }
      if (url && props.isLink) {
        if (props.replace) {
          common_vendor.index.redirectTo({ url });
        } else {
          common_vendor.index.navigateTo({ url });
        }
      }
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: _ctx.icon
      }, _ctx.icon ? {
        b: common_vendor.p({
          name: _ctx.icon,
          ["custom-class"]: `wd-cell__icon  ${_ctx.customIconClass}`
        })
      } : {}, {
        c: _ctx.title
      }, _ctx.title ? {
        d: common_vendor.t(_ctx.title),
        e: common_vendor.n(_ctx.customTitleClass)
      } : {}, {
        f: _ctx.label
      }, _ctx.label ? {
        g: common_vendor.t(_ctx.label),
        h: common_vendor.n(`wd-cell__label ${_ctx.customLabelClass}`)
      } : {}, {
        i: common_vendor.n(isRequired.value ? "is-required" : ""),
        j: common_vendor.s(_ctx.titleWidth ? "min-width:" + _ctx.titleWidth + ";max-width:" + _ctx.titleWidth + ";" : ""),
        k: common_vendor.t(_ctx.value),
        l: common_vendor.n(`wd-cell__value ${_ctx.customValueClass}`),
        m: _ctx.isLink
      }, _ctx.isLink ? {
        n: common_vendor.p({
          ["custom-class"]: "wd-cell__arrow-right",
          name: "arrow-right"
        })
      } : {}, {
        o: errorMessage.value
      }, errorMessage.value ? {
        p: common_vendor.t(errorMessage.value)
      } : {}, {
        q: common_vendor.n(_ctx.vertical ? "is-vertical" : ""),
        r: common_vendor.n(isBorder.value ? "is-border" : ""),
        s: common_vendor.n(_ctx.size ? "is-" + _ctx.size : ""),
        t: common_vendor.n(_ctx.center ? "is-center" : ""),
        v: common_vendor.n(_ctx.customClass),
        w: common_vendor.s(_ctx.customStyle),
        x: _ctx.isLink || _ctx.clickable ? "is-hover" : "none",
        y: common_vendor.o(onClick)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a65b3963"]]);
wx.createComponent(Component);
