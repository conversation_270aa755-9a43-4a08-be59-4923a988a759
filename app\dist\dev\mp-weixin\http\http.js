"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
require("../store/index.js");
const store_user = require("../store/user.js");
let is401DialogShowing = false;
function handleHttpError(error, hideErrorToast = false) {
  const { statusCode, data, errMsg, code } = error;
  let title = "请求失败";
  if (statusCode === 401) {
    title = "登录已过期，请重新登录";
    if (is401DialogShowing) {
      console.log("401对话框已经在显示中，跳过重复弹窗");
      return;
    }
    try {
      is401DialogShowing = true;
      common_vendor.index.showModal({
        title: "登录提示",
        content: "您当前没有登录，请登录后查看",
        showCancel: true,
        cancelText: "取消",
        confirmText: "去登录",
        success: (res) => {
          is401DialogShowing = false;
          if (res.confirm) {
            common_vendor.index.reLaunch({
              url: "/pages/login/index"
            });
          }
          const userStore = store_user.useUserStore();
          userStore.clearUserInfo();
        },
        fail: () => {
          is401DialogShowing = false;
          const userStore = store_user.useUserStore();
          userStore.clearUserInfo();
        }
      });
    } catch (e) {
      is401DialogShowing = false;
      console.warn("处理401错误失败:", e);
      try {
        const userStore = store_user.useUserStore();
        userStore.clearUserInfo();
      } catch (storeError) {
        console.warn("清理用户信息失败:", storeError);
      }
    }
    return;
  }
  if (code && code !== 0) {
    title = (data == null ? void 0 : data.msg) || errMsg || "操作失败";
  } else {
    switch (statusCode) {
      case 403:
        title = "没有权限访问该资源";
        break;
      case 404:
        title = "请求的资源不存在";
        break;
      case 500:
        title = "服务器内部错误，请稍后重试";
        break;
      case 502:
        title = "网关错误，请稍后重试";
        break;
      case 503:
        title = "服务暂不可用，请稍后重试";
        break;
      case 0:
        title = "网络连接失败，请检查网络设置";
        break;
      default:
        if (statusCode >= 400) {
          title = (data == null ? void 0 : data.msg) || errMsg || "请求错误，请检查网络连接";
        } else {
          title = errMsg || "未知错误";
        }
    }
  }
  if (!hideErrorToast) {
    common_vendor.index.showToast({
      icon: "none",
      title,
      duration: 3e3
      // 延长显示时间让用户能看清错误信息
    });
  }
}
function http(options) {
  return new Promise((resolve, reject) => {
    common_vendor.index.request(__spreadProps(__spreadValues({}, options), {
      dataType: "json",
      // 响应成功
      success(res) {
        const responseData = res.data;
        if (res.statusCode < 200 || res.statusCode >= 300) {
          const error = {
            statusCode: res.statusCode,
            data: responseData,
            errMsg: (responseData == null ? void 0 : responseData.msg) || `HTTP ${res.statusCode} 错误`,
            code: responseData == null ? void 0 : responseData.code
          };
          handleHttpError(error, options.hideErrorToast);
          reject(error);
          return;
        }
        if (responseData.code && responseData.code !== 0) {
          const error = {
            statusCode: res.statusCode,
            data: responseData,
            errMsg: responseData.msg || "操作失败",
            code: responseData.code
          };
          handleHttpError(error, options.hideErrorToast);
          reject(error);
          return;
        }
        resolve(responseData);
      },
      // 响应失败
      fail() {
        const error = {
          statusCode: 0,
          errMsg: "网络连接失败，请检查网络设置"
        };
        handleHttpError(error, options.hideErrorToast);
        reject(error);
      }
    }));
  });
}
function httpGet(url, query, header, options) {
  return http(__spreadValues({
    url,
    query,
    method: "GET",
    header
  }, options));
}
function httpPost(url, data, query, header, options) {
  return http(__spreadValues({
    url,
    query,
    data,
    method: "POST",
    header
  }, options));
}
function httpPut(url, data, query, header, options) {
  return http(__spreadValues({
    url,
    data,
    query,
    method: "PUT",
    header
  }, options));
}
function httpDelete(url, query, header, options) {
  return http(__spreadValues({
    url,
    query,
    method: "DELETE",
    header
  }, options));
}
http.get = httpGet;
http.post = httpPost;
http.put = httpPut;
http.delete = httpDelete;
http.Get = httpGet;
http.Post = httpPost;
http.Put = httpPut;
http.Delete = httpDelete;
exports.http = http;
exports.httpGet = httpGet;
