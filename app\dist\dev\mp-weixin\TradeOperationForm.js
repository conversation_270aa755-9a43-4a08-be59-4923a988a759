"use strict";
const common_vendor = require("./common/vendor.js");
const utils_toast = require("./utils/toast.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_button = () => "./node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  _easycom_wd_button();
}
const minQuantity = 1;
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "TradeOperationForm",
  props: {
    isPointPrice: { type: Boolean },
    availableQuantity: {},
    weightedPrice: {},
    limitUpPrice: { default: void 0 },
    limitDownPrice: { default: void 0 },
    loading: { type: Boolean }
  },
  emits: ["submit", "priceClick"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emit = __emit;
    const props = __props;
    const tradeVolume = common_vendor.ref(null);
    const requestedPrice = common_vendor.ref(null);
    const maxQuantity = common_vendor.computed(() => props.availableQuantity);
    const minPrice = common_vendor.computed(() => props.limitDownPrice);
    const maxPrice = common_vendor.computed(() => props.limitUpPrice);
    const isButtonDisabled = common_vendor.computed(() => {
      if (!tradeVolume.value)
        return true;
      if (props.availableQuantity === 0)
        return true;
      if (tradeVolume.value > props.availableQuantity)
        return true;
      if (tradeVolume.value < minQuantity)
        return true;
      if (!requestedPrice.value)
        return true;
      if (requestedPrice.value) {
        if (minPrice.value && requestedPrice.value < minPrice.value)
          return true;
        if (maxPrice.value && requestedPrice.value > maxPrice.value)
          return true;
      }
      if (props.loading)
        return true;
      return false;
    });
    const buttonText = common_vendor.computed(() => {
      if (props.loading)
        return "提交中...";
      if (props.availableQuantity === 0) {
        return `暂无可用${props.isPointPrice ? "基差" : "固定价"}合同`;
      }
      return props.isPointPrice ? "确认点价" : "确认洗基差";
    });
    const executionPrice = common_vendor.computed(() => {
      if (props.isPointPrice) {
        if (!requestedPrice.value)
          return "--";
        return (props.weightedPrice + requestedPrice.value).toFixed(2);
      } else {
        if (!requestedPrice.value)
          return "--";
        return (props.weightedPrice - requestedPrice.value).toFixed(2);
      }
    });
    const quantityInputRules = common_vendor.computed(() => {
      const rules = [];
      if (tradeVolume.value) {
        if (tradeVolume.value < minQuantity) {
          rules.push(`数量不能小于 ${minQuantity} 手`);
        }
        if (tradeVolume.value > maxQuantity.value) {
          rules.push(`数量不能大于 ${maxQuantity.value} 手`);
        }
      }
      return rules;
    });
    const priceInputRules = common_vendor.computed(() => {
      const rules = [];
      if (requestedPrice.value) {
        if (minPrice.value && requestedPrice.value < minPrice.value) {
          rules.push(`价格不能低于跌停价 ${minPrice.value.toFixed(2)}`);
        }
        if (maxPrice.value && requestedPrice.value > maxPrice.value) {
          rules.push(`价格不能高于涨停价 ${maxPrice.value.toFixed(2)}`);
        }
      }
      return rules;
    });
    const handleSubmit = () => {
      if (!tradeVolume.value || tradeVolume.value <= 0) {
        utils_toast.toast.error("请输入有效的数量");
        return;
      }
      if (tradeVolume.value < minQuantity) {
        utils_toast.toast.error(`数量不能小于 ${minQuantity} 手`);
        return;
      }
      if (tradeVolume.value > props.availableQuantity) {
        utils_toast.toast.error(`操作数量不能超过可用总量 ${props.availableQuantity} 手`);
        return;
      }
      if (props.availableQuantity === 0) {
        utils_toast.toast.error("暂无可用合同，无法进行操作");
        return;
      }
      if (!requestedPrice.value || requestedPrice.value <= 0) {
        utils_toast.toast.error("需要输入有效的价格");
        return;
      }
      if (requestedPrice.value) {
        if (minPrice.value && requestedPrice.value < minPrice.value) {
          utils_toast.toast.error(`价格不能低于跌停价 ${minPrice.value.toFixed(2)}`);
          return;
        }
        if (maxPrice.value && requestedPrice.value > maxPrice.value) {
          utils_toast.toast.error(`价格不能高于涨停价 ${maxPrice.value.toFixed(2)}`);
          return;
        }
      }
      const submitData = {
        quantity: tradeVolume.value,
        price: requestedPrice.value
      };
      emit("submit", submitData);
    };
    const handlePriceClick = (price) => {
      requestedPrice.value = price;
      emit("priceClick", price);
    };
    const increaseQuantity = () => {
      if (!tradeVolume.value) {
        tradeVolume.value = minQuantity;
      } else if (tradeVolume.value < maxQuantity.value) {
        tradeVolume.value += 1;
      }
    };
    const decreaseQuantity = () => {
      if (!tradeVolume.value) {
        tradeVolume.value = minQuantity;
      } else if (tradeVolume.value > minQuantity) {
        tradeVolume.value -= 1;
      }
    };
    const increasePrice = () => {
      if (!requestedPrice.value) {
        requestedPrice.value = minPrice.value || 0;
      } else if (!maxPrice.value || requestedPrice.value < maxPrice.value) {
        requestedPrice.value = Number((requestedPrice.value + 0.5).toFixed(1));
      }
    };
    const decreasePrice = () => {
      if (!requestedPrice.value) {
        requestedPrice.value = minPrice.value || 0;
      } else if (!minPrice.value || requestedPrice.value > minPrice.value) {
        requestedPrice.value = Number((requestedPrice.value - 0.5).toFixed(1));
      }
    };
    const resetForm = () => {
      tradeVolume.value = null;
      requestedPrice.value = null;
    };
    __expose({
      resetForm,
      handlePriceClick
    });
    return (_ctx, _cache) => {
      var _a, _b;
      return common_vendor.e({
        a: common_vendor.t(minQuantity),
        b: common_vendor.o(decreaseQuantity),
        c: minQuantity,
        d: maxQuantity.value,
        e: tradeVolume.value,
        f: common_vendor.o(common_vendor.m(($event) => tradeVolume.value = $event.detail.value, {
          number: true
        })),
        g: common_vendor.o(increaseQuantity),
        h: common_vendor.t(maxQuantity.value),
        i: quantityInputRules.value.length > 0
      }, quantityInputRules.value.length > 0 ? {
        j: common_vendor.f(quantityInputRules.value, (rule, k0, i0) => {
          return {
            a: common_vendor.t(rule),
            b: rule
          };
        })
      } : {}, {
        k: common_vendor.t(_ctx.isPointPrice ? "点价价格" : "洗基差价格"),
        l: common_vendor.t(((_a = minPrice.value) == null ? void 0 : _a.toFixed(0)) || "--"),
        m: common_vendor.o(decreasePrice),
        n: minPrice.value,
        o: maxPrice.value,
        p: requestedPrice.value,
        q: common_vendor.o(common_vendor.m(($event) => requestedPrice.value = $event.detail.value, {
          number: true
        })),
        r: common_vendor.o(increasePrice),
        s: common_vendor.t(((_b = maxPrice.value) == null ? void 0 : _b.toFixed(0)) || "--"),
        t: priceInputRules.value.length > 0
      }, priceInputRules.value.length > 0 ? {
        v: common_vendor.f(priceInputRules.value, (rule, k0, i0) => {
          return {
            a: common_vendor.t(rule),
            b: rule
          };
        })
      } : {}, {
        w: requestedPrice.value
      }, requestedPrice.value ? {
        x: common_vendor.t(_ctx.isPointPrice ? "执行后价格" : "预估基差价格"),
        y: common_vendor.t(executionPrice.value)
      } : {}, {
        z: common_vendor.t(buttonText.value),
        A: common_vendor.o(handleSubmit),
        B: common_vendor.p({
          type: _ctx.isPointPrice ? "success" : "primary",
          block: true,
          loading: _ctx.loading,
          disabled: isButtonDisabled.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f4a108d6"]]);
exports.MiniProgramPage = MiniProgramPage;
