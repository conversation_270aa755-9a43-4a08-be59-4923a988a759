/**
 * 全局样式文件
 * 提供统一的页面容器样式，供各个页面复用
 */

// 主要的页面背景渐变
.gradient-bg-primary {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

// 页面容器样式
.page-container {
  min-height: 100vh;
}

// 通用卡片样式
.common-card {
  margin: 0 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
}