"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_toast = require("../../utils/toast.js");
const utils_fileUpload = require("../../utils/fileUpload.js");
const utils_imageUrl = require("../../utils/imageUrl.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  const _component_layout_tabbar_uni = common_vendor.resolveComponent("layout-tabbar-uni");
  (_easycom_wd_icon2 + _easycom_wd_input2 + _easycom_wd_button2 + _easycom_wd_popup2 + _component_layout_tabbar_uni)();
}
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_input + _easycom_wd_button + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const { avatarUrl } = utils_imageUrl.useUserStoreAvatar();
    const showEditPopup = common_vendor.ref(false);
    const editField = common_vendor.ref("");
    const editFieldLabel = common_vendor.ref("");
    const editValue = common_vendor.ref("");
    const saving = common_vendor.ref(false);
    const showAvatarCropper = common_vendor.ref(false);
    const selectedImagePath = common_vendor.ref("");
    const uploading = common_vendor.ref(false);
    common_vendor.onMounted(() => __async(this, null, function* () {
      try {
        yield userStore.getUserProfile();
      } catch (error) {
        console.error("获取用户信息失败:", error);
      }
    }));
    const handleAvatarClick = () => __async(this, null, function* () {
      try {
        const filePath = yield utils_fileUpload.FileUploadUtil.chooseImage({
          count: 1,
          sizeType: ["compressed"],
          // 优先使用压缩版本
          sourceType: ["album", "camera"]
        });
        selectedImagePath.value = filePath;
        showAvatarCropper.value = true;
      } catch (error) {
        console.error("选择图片失败:", error);
        utils_toast.toast.error(error.message || "选择图片失败");
      }
    });
    const closeAvatarCropper = () => {
      showAvatarCropper.value = false;
      selectedImagePath.value = "";
    };
    const uploadAvatar = () => __async(this, null, function* () {
      if (!selectedImagePath.value) {
        utils_toast.toast.error("请先选择图片");
        return;
      }
      uploading.value = true;
      try {
        console.log("开始上传头像，文件路径:", selectedImagePath.value);
        const isValid = yield utils_fileUpload.FileUploadUtil.validateImage(selectedImagePath.value);
        if (!isValid) {
          return;
        }
        const fileInfo = yield utils_fileUpload.FileUploadUtil.uploadFile(
          selectedImagePath.value,
          userStore.token,
          {
            classId: "0",
            onProgress: (progress) => {
              console.log("上传进度:", progress + "%");
            }
          }
        );
        console.log("文件上传成功，获取到的URL:", fileInfo.url);
        yield userStore.updateUserProfile({
          headerImg: fileInfo.url
        });
        closeAvatarCropper();
        utils_toast.toast.success("头像更新成功");
        console.log("头像更新流程完成");
      } catch (error) {
        console.error("头像上传失败:", error);
        utils_toast.toast.error(error.message || "头像上传失败，请重试");
      } finally {
        uploading.value = false;
      }
    });
    const editFieldHandler = (field) => {
      const fieldMap = {
        nickName: "昵称",
        companyName: "企业名称"
      };
      editField.value = field;
      editFieldLabel.value = fieldMap[field];
      editValue.value = userStore.userInfo[field] || "";
      showEditPopup.value = true;
    };
    const closeEditPopup = () => {
      showEditPopup.value = false;
      editField.value = "";
      editFieldLabel.value = "";
      editValue.value = "";
    };
    const saveEdit = () => __async(this, null, function* () {
      if (!editValue.value.trim()) {
        utils_toast.toast.error(`${editFieldLabel.value}不能为空`);
        return;
      }
      saving.value = true;
      try {
        const updateData = {
          [editField.value]: editValue.value.trim()
        };
        yield userStore.updateUserProfile(updateData);
        closeEditPopup();
        utils_toast.toast.success("保存成功");
      } catch (error) {
        console.error("保存失败:", error);
        utils_toast.toast.error("保存失败，请重试");
      } finally {
        saving.value = false;
      }
    });
    const goToChangePassword = () => {
      common_vendor.index.navigateTo({
        url: "/pages/profile/change-password"
      });
    };
    const goToHelpCenter = () => {
      common_vendor.index.navigateTo({
        url: "/pages/support/content-viewer?key=help&title=帮助中心"
      });
    };
    const goToFeedback = () => {
      common_vendor.index.navigateTo({
        url: "/pages/support/feedback"
      });
    };
    const goToAbout = () => {
      common_vendor.index.navigateTo({
        url: "/pages/support/about"
      });
    };
    const goToPrivacyPolicy = () => {
      common_vendor.index.navigateTo({
        url: "/pages/support/content-viewer?key=privacy&title=隐私政策"
      });
    };
    const goToTermsOfService = () => {
      common_vendor.index.navigateTo({
        url: "/pages/support/content-viewer?key=terms&title=服务条款"
      });
    };
    const handleLogout = () => {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        success: (res) => __async(this, null, function* () {
          if (res.confirm) {
            yield userStore.logout();
            common_vendor.index.reLaunch({
              url: "/pages/login/index"
            });
          }
        })
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(avatarUrl),
        b: common_vendor.p({
          name: "camera",
          size: "20rpx",
          color: "#fff"
        }),
        c: common_vendor.o(handleAvatarClick),
        d: common_vendor.t(common_vendor.unref(userStore).userInfo.nickName || "未设置昵称"),
        e: common_vendor.t(common_vendor.unref(userStore).userInfo.userName),
        f: common_vendor.p({
          name: "user",
          size: "32rpx",
          color: "#667eea"
        }),
        g: common_vendor.t(common_vendor.unref(userStore).userInfo.nickName || "未设置"),
        h: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        i: common_vendor.o(($event) => editFieldHandler("nickName")),
        j: common_vendor.p({
          name: "phone",
          size: "32rpx",
          color: "#667eea"
        }),
        k: common_vendor.t(common_vendor.unref(userStore).userInfo.phone || "未绑定"),
        l: common_vendor.p({
          name: "home",
          size: "32rpx",
          color: "#667eea"
        }),
        m: common_vendor.t(common_vendor.unref(userStore).userInfo.companyName || "未设置"),
        n: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        o: common_vendor.o(($event) => editFieldHandler("companyName")),
        p: common_vendor.p({
          name: "lock",
          size: "32rpx",
          color: "#667eea"
        }),
        q: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        r: common_vendor.o(goToChangePassword),
        s: common_vendor.p({
          name: "help",
          size: "32rpx",
          color: "#667eea"
        }),
        t: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        v: common_vendor.o(goToHelpCenter),
        w: common_vendor.p({
          name: "edit",
          size: "32rpx",
          color: "#667eea"
        }),
        x: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        y: common_vendor.o(goToFeedback),
        z: common_vendor.p({
          name: "user-talk",
          size: "32rpx",
          color: "#667eea"
        }),
        A: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        B: common_vendor.o(goToAbout),
        C: common_vendor.p({
          name: "error-circle",
          size: "32rpx",
          color: "#667eea"
        }),
        D: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        E: common_vendor.o(goToPrivacyPolicy),
        F: common_vendor.p({
          name: "gift",
          size: "32rpx",
          color: "#667eea"
        }),
        G: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        H: common_vendor.o(goToTermsOfService),
        I: common_vendor.p({
          name: "logout",
          size: "32rpx",
          color: "#f56c6c"
        }),
        J: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        K: common_vendor.o(handleLogout),
        L: common_vendor.t(editFieldLabel.value),
        M: common_vendor.o(closeEditPopup),
        N: common_vendor.p({
          name: "close",
          size: "32rpx"
        }),
        O: common_vendor.o(($event) => editValue.value = $event),
        P: common_vendor.p({
          placeholder: `请输入${editFieldLabel.value}`,
          clearable: true,
          maxlength: 50,
          modelValue: editValue.value
        }),
        Q: common_vendor.o(closeEditPopup),
        R: common_vendor.p({
          type: "default",
          size: "large",
          ["custom-class"]: "cancel-btn"
        }),
        S: common_vendor.o(saveEdit),
        T: common_vendor.p({
          type: "primary",
          size: "large",
          ["custom-class"]: "save-btn",
          loading: saving.value
        }),
        U: common_vendor.o(($event) => showEditPopup.value = $event),
        V: common_vendor.p({
          position: "bottom",
          ["safe-area-inset-bottom"]: true,
          modelValue: showEditPopup.value
        }),
        W: common_vendor.o(closeAvatarCropper),
        X: common_vendor.p({
          name: "close",
          size: "32rpx"
        }),
        Y: selectedImagePath.value
      }, selectedImagePath.value ? {
        Z: selectedImagePath.value
      } : {}, {
        aa: common_vendor.o(closeAvatarCropper),
        ab: common_vendor.p({
          type: "default",
          size: "large",
          ["custom-class"]: "cancel-btn"
        }),
        ac: common_vendor.t(uploading.value ? "上传中..." : "确认上传"),
        ad: common_vendor.o(uploadAvatar),
        ae: common_vendor.p({
          type: "primary",
          size: "large",
          ["custom-class"]: "upload-btn",
          loading: uploading.value
        }),
        af: common_vendor.o(($event) => showAvatarCropper.value = $event),
        ag: common_vendor.p({
          position: "bottom",
          ["safe-area-inset-bottom"]: true,
          modelValue: showAvatarCropper.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f97f9319"]]);
wx.createPage(MiniProgramPage);
