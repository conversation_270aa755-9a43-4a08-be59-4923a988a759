/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.combination-selector .combination-card.data-v-98d348b7 {
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  cursor: pointer;
}
.combination-selector .combination-card.data-v-98d348b7:hover {
  border-color: #3b82f6;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
  transform: translateY(-2rpx);
}
.combination-selector .combination-card .combination-content.data-v-98d348b7 {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  position: relative;
}
.combination-selector .combination-card .combination-content .combination-left.data-v-98d348b7,
.combination-selector .combination-card .combination-content .combination-right.data-v-98d348b7 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.combination-selector .combination-card .combination-content .combination-left .combination-label.data-v-98d348b7,
.combination-selector .combination-card .combination-content .combination-right .combination-label.data-v-98d348b7 {
  font-size: 22rpx;
  color: #64748b;
  margin-bottom: 8rpx;
  font-weight: 400;
}
.combination-selector .combination-card .combination-content .combination-left .combination-name.data-v-98d348b7,
.combination-selector .combination-card .combination-content .combination-right .combination-name.data-v-98d348b7 {
  font-size: 28rpx;
  color: #1e293b;
  font-weight: 600;
  text-align: center;
  line-height: 1.3;
}
.combination-selector .combination-card .combination-content .combination-divider.data-v-98d348b7 {
  width: 2rpx;
  height: 60rpx;
  background: linear-gradient(to bottom, transparent, #cbd5e1, transparent);
  margin: 0 20rpx;
}