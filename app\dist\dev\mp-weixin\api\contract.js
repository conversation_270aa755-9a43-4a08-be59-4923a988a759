"use strict";
const http_http = require("../http/http.js");
function createContract(data) {
  return http_http.http.post("/dianjia/contract", data);
}
function updateContract(data) {
  return http_http.http.put(`/dianjia/contract/${data.id}`, data);
}
function deleteContract(contractId) {
  return http_http.http.delete(`/dianjia/contract/${contractId}`);
}
function getContractsAsSetter(params) {
  return http_http.http.get("/dianjia/contracts/as-setter", params);
}
function getContractsAsPricer(params) {
  return http_http.http.get("/dianjia/contracts/as-pricer", params);
}
function getContractDetail(contractId) {
  return http_http.http.get(`/dianjia/contract/${contractId}`);
}
function getContractCancelRecords(contractId) {
  return http_http.http.get(`/dianjia/contract/${contractId}/cancel-records`);
}
function activateContract(contractId) {
  return http_http.http.post(`/dianjia/contract/${contractId}/activate`);
}
function deactivateContract(contractId) {
  return http_http.http.post(`/dianjia/contract/${contractId}/deactivate`);
}
function cancelContract(contractId, data) {
  return http_http.http.post(`/dianjia/contract/${contractId}/cancel`, data);
}
exports.activateContract = activateContract;
exports.cancelContract = cancelContract;
exports.createContract = createContract;
exports.deactivateContract = deactivateContract;
exports.deleteContract = deleteContract;
exports.getContractCancelRecords = getContractCancelRecords;
exports.getContractDetail = getContractDetail;
exports.getContractsAsPricer = getContractsAsPricer;
exports.getContractsAsSetter = getContractsAsSetter;
exports.updateContract = updateContract;
