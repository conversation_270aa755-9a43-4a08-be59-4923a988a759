package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router"
	"github.com/gin-gonic/gin"
)

// 占位方法，保证文件可以正确加载，避免go空变量检测报错，请勿删除。
func holder(routers ...*gin.RouterGroup) {
	_ = routers
	_ = router.RouterGroupApp
}

func initBizRouter(routers ...*gin.RouterGroup) {
	privateGroup := routers[0]
	publicGroup := routers[1]

	dianjiaRouter := router.RouterGroupApp.Dianjia
	{
		dianjiaRouter.InitAuthRouter(publicGroup) // 初始化认证路由
		dianjiaRouter.InitContractRouter(privateGroup) // 初始化合同路由
		dianjiaRouter.InitTradeRequestRouter(privateGroup) // 初始化交易请求路由
		dianjiaRouter.InitCommodityRouter(privateGroup) // 初始化商品路由
		dianjiaRouter.InitInstrumentRouter(privateGroup) // 初始化期货合约路由
		dianjiaRouter.InitQuotationRouter(privateGroup, publicGroup) // 初始化报价路由（包括私有和公开路由）
	}

	holder(publicGroup, privateGroup)
}
