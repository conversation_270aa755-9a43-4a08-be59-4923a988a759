"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_contract = require("../../api/contract.js");
if (!Array) {
  const _easycom_wd_tag2 = common_vendor.resolveComponent("wd-tag");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_tag2 + _easycom_wd_loading2 + _component_layout_default_uni)();
}
const _easycom_wd_tag = () => "../../node-modules/wot-design-uni/components/wd-tag/wd-tag.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
if (!Math) {
  (_easycom_wd_tag + _easycom_wd_loading)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "cancel-records",
  setup(__props) {
    const router = common_vendor.useRouter();
    const cancelRecords = common_vendor.ref([]);
    const contractInfo = common_vendor.ref(null);
    const loading = common_vendor.ref(false);
    const contractId = common_vendor.ref(0);
    function loadCancelRecords() {
      return __async(this, null, function* () {
        if (loading.value || !contractId.value)
          return;
        loading.value = true;
        try {
          const [contractResponse, recordsResponse] = yield Promise.all([
            api_contract.getContractDetail(contractId.value),
            api_contract.getContractCancelRecords(contractId.value)
          ]);
          if (contractResponse.code === 0) {
            contractInfo.value = contractResponse.data;
          }
          if (recordsResponse.code === 0) {
            cancelRecords.value = recordsResponse.data || [];
          } else {
            common_vendor.index.showToast({
              title: recordsResponse.msg || "获取取消记录失败",
              icon: "error"
            });
          }
        } catch (error) {
          console.error("获取取消记录失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        } finally {
          loading.value = false;
        }
      });
    }
    function getStatusType(status) {
      const statusMap = {
        Unexecuted: "warning",
        Executing: "success",
        Pending: "warning",
        Completed: "primary",
        Cancelled: "danger"
      };
      return statusMap[status] || "warning";
    }
    function getStatusText(status) {
      const statusMap = {
        Unexecuted: "未执行",
        Executing: "执行中",
        Pending: "待处理",
        Completed: "已完成",
        Cancelled: "已取消"
      };
      return statusMap[status] || status;
    }
    function formatDateTime(dateStr) {
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    }
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      if (options.contractId) {
        contractId.value = parseInt(options.contractId);
        loadCancelRecords();
      } else {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "error"
        });
        setTimeout(() => {
          router.back();
        }, 1500);
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: contractInfo.value
      }, contractInfo.value ? {
        b: common_vendor.t(contractInfo.value.contractCode),
        c: common_vendor.t(contractInfo.value.totalQuantity),
        d: common_vendor.t(contractInfo.value.remainingQuantity),
        e: common_vendor.t(contractInfo.value.totalQuantity - contractInfo.value.remainingQuantity)
      } : {}, {
        f: common_vendor.f(cancelRecords.value, (record, k0, i0) => {
          var _a, _b;
          return common_vendor.e({
            a: common_vendor.t(formatDateTime(record.CreatedAt)),
            b: common_vendor.t(getStatusText(record.contractStatusAfterCancel)),
            c: "5442d9ac-1-" + i0 + ",5442d9ac-0",
            d: common_vendor.p({
              type: getStatusType(record.contractStatusAfterCancel)
            }),
            e: common_vendor.t(record.cancelQuantity),
            f: common_vendor.t(record.beforeCancelRemainingQuantity),
            g: common_vendor.t(record.afterCancelRemainingQuantity),
            h: record.reason
          }, record.reason ? {
            i: common_vendor.t(record.reason)
          } : {}, {
            j: common_vendor.t(((_a = record.user) == null ? void 0 : _a.nickName) || ((_b = record.user) == null ? void 0 : _b.userName) || `用户${record.userID}`),
            k: record.ID
          });
        }),
        g: !cancelRecords.value.length && !loading.value
      }, !cancelRecords.value.length && !loading.value ? {} : {}, {
        h: loading.value
      }, loading.value ? {} : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5442d9ac"]]);
wx.createPage(MiniProgramPage);
