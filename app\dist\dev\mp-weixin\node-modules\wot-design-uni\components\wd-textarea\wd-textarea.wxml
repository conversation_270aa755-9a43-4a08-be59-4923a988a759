<view class="{{['data-v-324b46d3', Y]}}" style="{{Z}}"><view wx:if="{{a}}" class="{{['data-v-324b46d3', h]}}" style="{{i}}"><view wx:if="{{b}}" class="wd-textarea__prefix data-v-324b46d3"><wd-icon wx:if="{{c}}" class="data-v-324b46d3" bindclick="{{d}}" u-i="324b46d3-0" bind:__l="__l" u-p="{{e}}"/><slot wx:else name="prefix"></slot></view><view class="wd-textarea__label-inner data-v-324b46d3"><text wx:if="{{f}}" class="data-v-324b46d3">{{g}}</text><slot wx:else name="label"></slot></view></view><view class="{{['data-v-324b46d3', X]}}"><block wx:if="{{r0}}"><textarea class="{{['data-v-324b46d3', j]}}" show-count="{{false}}" placeholder="{{k}}" disabled="{{l}}" maxlength="{{m}}" focus="{{n}}" auto-focus="{{o}}" placeholder-style="{{p}}" placeholder-class="{{q}}" auto-height="{{r}}" cursor-spacing="{{s}}" fixed="{{t}}" cursor="{{v}}" show-confirm-bar="{{w}}" selection-start="{{x}}" selection-end="{{y}}" adjust-position="{{z}}" hold-keyboard="{{A}}" confirm-type="{{B}}" confirm-hold="{{C}}" disable-default-padding="{{D}}" ignoreCompositionEvent="{{E}}" inputmode="{{F}}" bindinput="{{G}}" bindfocus="{{H}}" bindblur="{{I}}" bindconfirm="{{J}}" bindlinechange="{{K}}" bindkeyboardheightchange="{{L}}" value="{{M}}"/></block><view wx:if="{{N}}" class="wd-textarea__error-message data-v-324b46d3">{{O}}</view><view wx:if="{{P}}" class="wd-textarea__readonly-mask data-v-324b46d3"/><view class="wd-textarea__suffix data-v-324b46d3"><wd-icon wx:if="{{Q}}" class="data-v-324b46d3" bindclick="{{R}}" u-i="324b46d3-1" bind:__l="__l" u-p="{{S}}"/><view wx:if="{{T}}" class="wd-textarea__count data-v-324b46d3"><text class="{{['data-v-324b46d3', V]}}">{{U}}</text> /{{W}}</view></view></view></view>