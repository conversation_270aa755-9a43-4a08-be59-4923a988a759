/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-header.data-v-5442d9ac {
  margin-bottom: 30rpx;
}
.page-header .page-title.data-v-5442d9ac {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.contract-info-card.data-v-5442d9ac {
  padding: 30rpx;
}
.contract-info-card .info-row.data-v-5442d9ac {
  display: flex;
  margin-bottom: 12rpx;
}
.contract-info-card .info-row .label.data-v-5442d9ac {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}
.contract-info-card .info-row .value.data-v-5442d9ac {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}
.records-list .record-card.data-v-5442d9ac {
  padding: 30rpx;
}
.records-list .record-card .record-header.data-v-5442d9ac {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.records-list .record-card .record-header .record-time.data-v-5442d9ac {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.records-list .record-card .record-details .detail-row.data-v-5442d9ac {
  display: flex;
  margin-bottom: 12rpx;
}
.records-list .record-card .record-details .detail-row .label.data-v-5442d9ac {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}
.records-list .record-card .record-details .detail-row .value.data-v-5442d9ac {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}
.records-list .record-card .record-details .detail-row .value.highlight.data-v-5442d9ac {
  color: #f56c6c;
  font-weight: bold;
}
.empty-state.data-v-5442d9ac, .loading-state.data-v-5442d9ac {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
  gap: 30rpx;
}