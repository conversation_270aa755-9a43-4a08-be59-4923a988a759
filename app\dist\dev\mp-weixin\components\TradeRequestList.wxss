/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.trade-request-list .list-header.data-v-a812eeaf {
  background-color: #fafbfc;
  border-bottom: 1rpx solid #e4e7ed;
}
.trade-request-list .list-header h2.data-v-a812eeaf {
  font-size: 32rpx;
  color: #303133;
  font-weight: 600;
}
.trade-request-list .list-header.flex .header-info h2.data-v-a812eeaf {
  font-size: 36rpx;
  color: #303133;
  font-weight: 600;
  margin-bottom: 4rpx;
}
.trade-request-list .loading-state .loading-spinner.data-v-a812eeaf {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #409eff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin-a812eeaf 1s linear infinite;
}
@keyframes spin-a812eeaf {
to {
    transform: rotate(360deg);
}
}
.trade-request-list .empty-state .empty-icon.data-v-a812eeaf {
  width: 128rpx;
  height: 128rpx;
  opacity: 0.3;
}
.trade-request-list .request-list.data-v-a812eeaf .trade-request-item:last-child {
  margin-bottom: 0;
}
@media (max-width: 750rpx) {
.trade-request-list .list-header.data-v-a812eeaf {
    padding: 12rpx 16rpx;
}
.trade-request-list .list-header h2.data-v-a812eeaf {
    font-size: 28rpx;
}
.trade-request-list .request-list.data-v-a812eeaf {
    padding: 12rpx 16rpx;
}
}