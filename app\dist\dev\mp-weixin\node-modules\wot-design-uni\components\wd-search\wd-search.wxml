<view class="{{['data-v-4ebafd4c', C]}}" style="{{D}}"><view class="wd-search__block data-v-4ebafd4c"><slot name="prefix"></slot><view class="wd-search__field data-v-4ebafd4c"><view wx:if="{{a}}" style="{{e}}" class="wd-search__cover data-v-4ebafd4c" bindtap="{{f}}"><wd-icon wx:if="{{b}}" class="data-v-4ebafd4c" u-i="4ebafd4c-0" bind:__l="__l" u-p="{{b}}"></wd-icon><text class="{{['data-v-4ebafd4c', d]}}">{{c}}</text></view><wd-icon wx:if="{{g}}" class="data-v-4ebafd4c" u-i="4ebafd4c-1" bind:__l="__l" u-p="{{h}}"></wd-icon><input wx:if="{{i}}" placeholder="{{j}}" placeholder-class="{{k}}" placeholder-style="{{l}}" confirm-type="search" class="{{['data-v-4ebafd4c', 'wd-search__input', m]}}" bindfocus="{{n}}" bindinput="{{o}}" bindblur="{{p}}" bindconfirm="{{q}}" disabled="{{r}}" maxlength="{{s}}" focus="{{t}}" value="{{v}}"/><wd-icon wx:if="{{w}}" class="data-v-4ebafd4c" bindclick="{{x}}" u-i="4ebafd4c-2" bind:__l="__l" u-p="{{y}}"/></view></view><block wx:if="{{z}}"><block wx:if="{{$slots.suffix}}"><slot name="suffix"></slot></block><block wx:else><view class="wd-search__cancel data-v-4ebafd4c" bindtap="{{B}}">{{A}}</view></block></block></view>