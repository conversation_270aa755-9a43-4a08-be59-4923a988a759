<layout-default-uni class="data-v-3f647531" u-s="{{['d']}}" u-i="3f647531-0" bind:__l="__l"><view class="test-page p-4 data-v-3f647531"><view class="page-header mb-6 data-v-3f647531"><text class="page-title text-xl font-bold text-gray-800 data-v-3f647531"> 用户选择器测试页面 </text></view><view class="test-section mb-8 data-v-3f647531"><view class="section-title mb-4 data-v-3f647531"><text class="text-lg font-semibold text-gray-700 data-v-3f647531">基础使用</text></view><user-selector wx:if="{{c}}" class="data-v-3f647531" bindchange="{{a}}" u-i="3f647531-1,3f647531-0" bind:__l="__l" bindupdateModelValue="{{b}}" u-p="{{c}}"/></view><view class="info-section mb-6 data-v-3f647531"><view class="section-title mb-4 data-v-3f647531"><text class="text-lg font-semibold text-gray-700 data-v-3f647531">选择结果</text></view><view class="info-card bg-white p-4 rounded-lg shadow-sm data-v-3f647531"><view class="info-item mb-2 data-v-3f647531"><text class="label font-medium text-gray-600 data-v-3f647531">用户ID: </text><text class="value text-gray-800 data-v-3f647531">{{d}}</text></view><view class="info-item mb-2 data-v-3f647531"><text class="label font-medium text-gray-600 data-v-3f647531">用户昵称: </text><text class="value text-gray-800 data-v-3f647531">{{e}}</text></view><view class="info-item data-v-3f647531"><text class="label font-medium text-gray-600 data-v-3f647531">用户手机: </text><text class="value text-gray-800 data-v-3f647531">{{f}}</text></view></view></view><view class="action-section data-v-3f647531"><view class="section-title mb-4 data-v-3f647531"><text class="text-lg font-semibold text-gray-700 data-v-3f647531">测试操作</text></view><view class="action-buttons flex gap-4 data-v-3f647531"><wd-button wx:if="{{h}}" class="data-v-3f647531" u-s="{{['d']}}" bindclick="{{g}}" u-i="3f647531-2,3f647531-0" bind:__l="__l" u-p="{{h}}"> 重置选择 </wd-button><wd-button wx:if="{{j}}" class="data-v-3f647531" u-s="{{['d']}}" bindclick="{{i}}" u-i="3f647531-3,3f647531-0" bind:__l="__l" u-p="{{j}}"> 模拟设置用户 </wd-button></view></view></view></layout-default-uni>