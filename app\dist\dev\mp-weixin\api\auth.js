"use strict";
require("../common/vendor.js");
const http_http = require("../http/http.js");
function sendLoginCode(data) {
  return http_http.http.post("/user/sendLoginCode", data);
}
function loginByPhone(data) {
  return http_http.http.post("/user/loginByPhone", data);
}
function getProfile() {
  return http_http.http.get("/user/getProfile");
}
function updateProfile(data) {
  return http_http.http.put("/user/updateProfile", data);
}
function loginByWechat(data) {
  return http_http.http.post("/user/loginByWechat", data);
}
function changePassword(data) {
  const requestData = {
    password: data.oldPassword,
    // 后端期望的字段名是 password
    newPassword: data.newPassword
    // 新密码字段名保持一致
  };
  return http_http.http.post("/user/changePassword", requestData);
}
function loginByUsername(data) {
  return http_http.http.post("/user/login", data);
}
function getCaptcha() {
  return http_http.http.post("/base/captcha");
}
exports.changePassword = changePassword;
exports.getCaptcha = getCaptcha;
exports.getProfile = getProfile;
exports.loginByPhone = loginByPhone;
exports.loginByUsername = loginByUsername;
exports.loginByWechat = loginByWechat;
exports.sendLoginCode = sendLoginCode;
exports.updateProfile = updateProfile;
