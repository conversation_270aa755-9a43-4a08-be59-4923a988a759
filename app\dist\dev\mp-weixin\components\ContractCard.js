"use strict";
const common_vendor = require("../common/vendor.js");
if (!Array) {
  const _easycom_wd_tag2 = common_vendor.resolveComponent("wd-tag");
  _easycom_wd_tag2();
}
const _easycom_wd_tag = () => "../node-modules/wot-design-uni/components/wd-tag/wd-tag.js";
if (!Math) {
  _easycom_wd_tag();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "ContractCard",
  props: {
    contract: {},
    userRole: {}
  },
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    function handleClick() {
      emit("click", props.contract);
    }
    function getUserDisplayName() {
      var _a, _b, _c, _d;
      if (props.userRole === "setter") {
        return ((_a = props.contract.pricer) == null ? void 0 : _a.nickName) || ((_b = props.contract.pricer) == null ? void 0 : _b.userName) || `用户${props.contract.pricerID}`;
      } else {
        return ((_c = props.contract.setter) == null ? void 0 : _c.nickName) || ((_d = props.contract.setter) == null ? void 0 : _d.userName) || `用户${props.contract.setterID}`;
      }
    }
    function getPriceDisplay() {
      if (props.contract.priceType === "basis") {
        return `基差 ${props.contract.priceValue > 0 ? "+" : ""}${props.contract.priceValue}`;
      } else {
        return `固定价 ${props.contract.priceValue}`;
      }
    }
    function getStatusType(status) {
      const typeMap = {
        "Unexecuted": "warning",
        "Executing": "success",
        "Pending": "warning",
        "Completed": "primary",
        "Cancelled": "danger"
      };
      return typeMap[status] || "primary";
    }
    function getStatusText(status) {
      const textMap = {
        "Unexecuted": "未执行",
        "Executing": "执行中",
        "Pending": "待处理",
        "Completed": "已完成",
        "Cancelled": "已取消"
      };
      return textMap[status] || status;
    }
    function getContractTypeTagType(priceType) {
      return priceType === "basis" ? "primary" : "success";
    }
    function getContractTypeLabel(priceType) {
      return priceType === "basis" ? "基差" : "固定价";
    }
    function formatDate(dateStr) {
      const date = new Date(dateStr);
      return `${date.getMonth() + 1}-${date.getDate()}`;
    }
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: common_vendor.t(_ctx.contract.contractCode),
        b: common_vendor.t(getStatusText(_ctx.contract.status)),
        c: common_vendor.p({
          type: getStatusType(_ctx.contract.status),
          size: "small"
        }),
        d: common_vendor.t(getContractTypeLabel(_ctx.contract.priceType)),
        e: common_vendor.p({
          type: getContractTypeTagType(_ctx.contract.priceType),
          size: "small"
        }),
        f: _ctx.contract.frozenQuantity > 0
      }, _ctx.contract.frozenQuantity > 0 ? {
        g: common_vendor.t(_ctx.contract.frozenQuantity),
        h: common_vendor.p({
          type: "warning",
          size: "small"
        })
      } : {}, {
        i: common_vendor.t(getUserDisplayName()),
        j: common_vendor.t(((_a = _ctx.contract.instrument) == null ? void 0 : _a.instrument_name) || `合约${_ctx.contract.instrumentRefID}`),
        k: common_vendor.t(formatDate(_ctx.contract.CreatedAt)),
        l: common_vendor.t(getPriceDisplay()),
        m: common_vendor.t(_ctx.contract.totalQuantity),
        n: common_vendor.t(_ctx.contract.remainingQuantity),
        o: common_vendor.t(_ctx.contract.frozenQuantity),
        p: _ctx.contract.frozenQuantity > 0 ? 1 : "",
        q: _ctx.contract.frozenQuantity > 0 ? 1 : "",
        r: common_vendor.t(_ctx.contract.remainingQuantity - _ctx.contract.frozenQuantity),
        s: _ctx.contract.remarks
      }, _ctx.contract.remarks ? {
        t: common_vendor.t(_ctx.contract.remarks)
      } : {}, {
        v: common_vendor.r("actions", {
          contract: _ctx.contract
        }),
        w: common_vendor.o(handleClick)
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4947044"]]);
wx.createComponent(Component);
