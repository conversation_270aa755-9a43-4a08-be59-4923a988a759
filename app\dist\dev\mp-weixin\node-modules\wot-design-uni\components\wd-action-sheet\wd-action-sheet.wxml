<view class="data-v-904a1f1d"><wd-popup wx:if="{{v}}" class="data-v-904a1f1d" u-s="{{['d']}}" bindenter="{{o}}" bindclose="{{p}}" bindafterEnter="{{q}}" bindafterLeave="{{r}}" bindclickModal="{{s}}" u-i="904a1f1d-0" bind:__l="__l" bindupdateModelValue="{{t}}" u-p="{{v}}"><view class="{{['data-v-904a1f1d', m]}}" style="{{n}}"><view wx:if="{{a}}" class="{{['data-v-904a1f1d', e]}}">{{b}} <wd-icon wx:if="{{d}}" class="data-v-904a1f1d" bindclick="{{c}}" u-i="904a1f1d-1,904a1f1d-0" bind:__l="__l" u-p="{{d}}"/></view><view wx:if="{{f}}" class="wd-action-sheet__actions data-v-904a1f1d"><button wx:for="{{g}}" wx:for-item="action" wx:key="g" class="{{['data-v-904a1f1d', action.h]}}" style="{{action.i}}" bindtap="{{action.j}}"><wd-loading wx:if="{{action.a}}" class="data-v-904a1f1d" u-i="{{action.b}}" bind:__l="__l" u-p="{{action.c}}"/><view wx:else class="wd-action-sheet__name data-v-904a1f1d">{{action.d}}</view><view wx:if="{{action.e}}" class="wd-action-sheet__subname data-v-904a1f1d">{{action.f}}</view></button></view><view wx:if="{{h}}" class="data-v-904a1f1d"><view wx:for="{{i}}" wx:for-item="panel" wx:key="b" class="wd-action-sheet__panels data-v-904a1f1d"><view class="wd-action-sheet__panels-content data-v-904a1f1d"><view wx:for="{{panel.a}}" wx:for-item="col" wx:key="c" class="wd-action-sheet__panel data-v-904a1f1d" bindtap="{{col.d}}"><image class="wd-action-sheet__panel-img data-v-904a1f1d" src="{{col.a}}"/><view class="wd-action-sheet__panel-title data-v-904a1f1d">{{col.b}}</view></view></view></view></view><slot/><button wx:if="{{j}}" class="wd-action-sheet__cancel data-v-904a1f1d" bindtap="{{l}}">{{k}}</button></view></wd-popup></view>