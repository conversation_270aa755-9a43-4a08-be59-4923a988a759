"use strict";
const TABBAR_MAP = {
  CUSTOM_TABBAR_WITH_CACHE: 2
};
const selectedTabbarStrategy = TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE;
const tabbarList = [
  {
    pagePath: "pages/workspace/index",
    text: "工作台",
    icon: "home",
    // 选用 UI 框架自带的 icon 时，iconType 为 uiLib
    iconType: "uiLib"
  },
  {
    pagePath: "pages/quotes/my-list",
    text: "报价管理",
    icon: "list",
    iconType: "uiLib"
  },
  {
    pagePath: "pages/trade/execute",
    text: "交易中心",
    icon: "thumb-up",
    iconType: "uiLib"
  },
  {
    pagePath: "pages/profile/index",
    text: "我的",
    icon: "user",
    iconType: "uiLib"
  }
];
exports.TABBAR_MAP = TABBAR_MAP;
exports.selectedTabbarStrategy = selectedTabbarStrategy;
exports.tabbarList = tabbarList;
