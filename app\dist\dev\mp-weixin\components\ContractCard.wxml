<view class="contract-card data-v-e4947044" bindtap="{{w}}"><view class="contract-header data-v-e4947044"><view class="header-main data-v-e4947044"><text class="contract-code data-v-e4947044">{{a}}</text><view class="header-tags data-v-e4947044"><wd-tag wx:if="{{c}}" class="data-v-e4947044" u-s="{{['d']}}" u-i="e4947044-0" bind:__l="__l" u-p="{{c}}">{{b}}</wd-tag><wd-tag wx:if="{{e}}" class="data-v-e4947044" u-s="{{['d']}}" u-i="e4947044-1" bind:__l="__l" u-p="{{e}}">{{d}}</wd-tag><wd-tag wx:if="{{f}}" class="data-v-e4947044" u-s="{{['d']}}" u-i="e4947044-2" bind:__l="__l" u-p="{{h}}"> 冻结{{g}}</wd-tag></view></view></view><view class="contract-info data-v-e4947044"><view class="user-contract-info data-v-e4947044"><text class="user-name data-v-e4947044">{{i}}</text><text class="instrument-name data-v-e4947044">{{j}}</text><text class="create-time data-v-e4947044">{{k}}</text></view><view class="price-quantity-section data-v-e4947044"><view class="price-section data-v-e4947044"><text class="price-value data-v-e4947044">{{l}}</text></view><view class="quantity-section data-v-e4947044"><view class="quantity-row data-v-e4947044"><view class="quantity-item data-v-e4947044"><text class="quantity-number data-v-e4947044">{{m}}</text><text class="quantity-label data-v-e4947044">总量</text></view><view class="quantity-item data-v-e4947044"><text class="quantity-number data-v-e4947044">{{n}}</text><text class="quantity-label data-v-e4947044">剩余</text></view><view class="quantity-item data-v-e4947044"><text class="{{['quantity-number', 'data-v-e4947044', p && 'warning']}}">{{o}}</text><text class="{{['quantity-label', 'data-v-e4947044', q && 'warning']}}">冻结</text></view><view class="quantity-item data-v-e4947044"><text class="quantity-number data-v-e4947044">{{r}}</text><text class="quantity-label data-v-e4947044">可用</text></view></view></view></view><view wx:if="{{s}}" class="remarks-section data-v-e4947044"><text class="remarks-text data-v-e4947044">{{t}}</text></view></view><slot name="actions"></slot></view>