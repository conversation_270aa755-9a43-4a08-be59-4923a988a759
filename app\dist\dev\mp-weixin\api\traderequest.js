"use strict";
const http_http = require("../http/http.js");
const createTradeRequest = (data) => {
  return http_http.http.post("/dianjia/traderequests", data);
};
const cancelTradeRequest = (id) => {
  return http_http.http.post(`/dianjia/traderequests/${id}/cancel`);
};
const rejectTradeRequest = (id, data) => {
  return http_http.http.post(`/dianjia/traderequests/${id}/reject`, data);
};
const manualFeedback = (id, data) => {
  return http_http.http.post(`/dianjia/traderequests/${id}/feedback`, data);
};
const getTradeRequestsForPricer = (params) => {
  return http_http.http.get("/dianjia/traderequests/as-pricer", params);
};
const getTradeRequestsForSetter = (params) => {
  return http_http.http.get("/dianjia/traderequests/as-setter", params);
};
const getMyTradeRequestsAsPricer = (params) => {
  return getTradeRequestsForPricer(params);
};
const getMyTradeRequestsAsSetter = (params) => {
  return getTradeRequestsForSetter(params);
};
const pointPrice = (setterID, instrumentRefID, requestedQuantity, requestedPrice, executionMode, expiresAt) => {
  return createTradeRequest({
    setterID,
    instrumentRefID,
    requestType: "PointPrice",
    requestedQuantity,
    requestedPrice,
    executionMode,
    expiresAt
  });
};
const basisWash = (setterID, instrumentRefID, requestedQuantity, requestedPrice, executionMode, expiresAt) => {
  return createTradeRequest({
    setterID,
    instrumentRefID,
    requestType: "BasisWash",
    requestedQuantity,
    requestedPrice,
    executionMode,
    expiresAt
  });
};
exports.basisWash = basisWash;
exports.cancelTradeRequest = cancelTradeRequest;
exports.getMyTradeRequestsAsPricer = getMyTradeRequestsAsPricer;
exports.getMyTradeRequestsAsSetter = getMyTradeRequestsAsSetter;
exports.manualFeedback = manualFeedback;
exports.pointPrice = pointPrice;
exports.rejectTradeRequest = rejectTradeRequest;
