"use strict";
const common_vendor = require("../common/vendor.js");
const store_user = require("../store/user.js");
function getFullImageUrl(imageUrl, defaultImage) {
  if (!imageUrl || imageUrl.trim() === "") {
    return defaultImage || "/static/images/default-avatar.png";
  }
  if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
    return imageUrl;
  }
  const baseUrl = "http://localhost:8888";
  const normalizedBaseUrl = baseUrl.endsWith("/") ? baseUrl.slice(0, -1) : baseUrl;
  const normalizedImageUrl = imageUrl.startsWith("/") ? imageUrl : `/${imageUrl}`;
  return `${normalizedBaseUrl}${normalizedImageUrl}`;
}
function getUserAvatarUrl(headerImg) {
  return getFullImageUrl(headerImg, "/static/images/default-avatar.png");
}
function isFullUrl(url) {
  if (!url)
    return false;
  return url.startsWith("http://") || url.startsWith("https://");
}
function useUserStoreAvatar() {
  const userStore = store_user.useUserStore();
  const avatarUrl = common_vendor.computed(() => getUserAvatarUrl(userStore.userInfo.headerImg));
  return {
    avatarUrl,
    userInfo: userStore.userInfo,
    isFullUrl: common_vendor.computed(() => isFullUrl(userStore.userInfo.headerImg))
  };
}
exports.useUserStoreAvatar = useUserStoreAvatar;
