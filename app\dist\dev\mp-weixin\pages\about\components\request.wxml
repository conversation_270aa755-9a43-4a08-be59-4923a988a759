<view class="p-6 text-center"><view class="my-2"> pages 里面的 vue 文件会扫描成页面，将自动添加到 pages.json 里面。 </view><view class="my-2 text-green-400"> 但是 pages/components 里面的 vue 不会。 </view><view class="my-6 text-center"><button type="primary" size="mini" class="w-160px" bindtap="{{a}}"> 发送请求 </button></view><view class="h-16"><view wx:if="{{b}}"> loading... </view><block wx:else><view class="text-xl"> 请求数据如下 </view><view class="text-green leading-8">{{c}}</view></block></view><view class="my-6 text-center"><button type="warn" size="mini" class="w-160px" disabled="{{d}}" bindtap="{{e}}"> 重置数据 </button></view></view>