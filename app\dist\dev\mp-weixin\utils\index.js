"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
const utils_platform = require("./platform.js");
const layouts_fgTabbar_tabbarList = require("../layouts/fg-tabbar/tabbarList.js");
const pages = [
  {
    path: "pages/workspace/index",
    type: "home",
    layout: "tabbar",
    style: {
      navigationBarTitleText: "工作台"
    }
  },
  {
    path: "pages/index/index",
    type: "home",
    layout: "tabbar",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "首页"
    }
  },
  {
    path: "pages/about/about",
    type: "page",
    layout: "tabbar",
    style: {
      navigationBarTitleText: "关于"
    }
  },
  {
    path: "pages/about/alova",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "Alova 请求演示"
    }
  },
  {
    path: "pages/about/vue-query",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "Vue Query 请求演示"
    }
  },
  {
    path: "pages/contract/cancel-records",
    type: "page",
    style: {
      navigationBarTitleText: "取消记录"
    }
  },
  {
    path: "pages/contract/detail",
    type: "page",
    style: {
      navigationBarTitleText: "合同详情"
    }
  },
  {
    path: "pages/contract/form",
    type: "page",
    style: {
      navigationBarTitleText: "合同表单"
    }
  },
  {
    path: "pages/contract/list",
    type: "page",
    style: {
      navigationBarTitleText: "合同管理"
    }
  },
  {
    path: "pages/contract/pricer-list",
    type: "page",
    style: {
      navigationBarTitleText: "合同中心"
    }
  },
  {
    path: "pages/contract/setter-list",
    type: "page",
    style: {
      navigationBarTitleText: "合同管理"
    }
  },
  {
    path: "pages/login/index",
    type: "page",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "登录"
    }
  },
  {
    path: "pages/profile/change-password",
    type: "page",
    style: {
      navigationBarTitleText: "修改密码"
    }
  },
  {
    path: "pages/profile/index",
    type: "page",
    layout: "tabbar",
    style: {
      navigationBarTitleText: "账户中心"
    }
  },
  {
    path: "pages/quotes/detail",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "报价详情",
      navigationStyle: "default"
    }
  },
  {
    path: "pages/quotes/edit",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "编辑报价"
    }
  },
  {
    path: "pages/quotes/marketplace",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "报价市场",
      navigationStyle: "default"
    }
  },
  {
    path: "pages/quotes/my-list",
    type: "page",
    layout: "tabbar",
    style: {
      navigationBarTitleText: "报价管理",
      navigationStyle: "default"
    }
  },
  {
    path: "pages/quotes/public-list",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "用户主页"
    }
  },
  {
    path: "pages/support/about",
    type: "page",
    style: {
      navigationBarTitleText: "关于我们"
    }
  },
  {
    path: "pages/support/content-viewer",
    type: "page",
    style: {
      navigationBarTitleText: "内容详情"
    }
  },
  {
    path: "pages/support/feedback",
    type: "page",
    style: {
      navigationBarTitleText: "意见反馈"
    }
  },
  {
    path: "pages/test/debug-select-input",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "SelectInput 调试"
    }
  },
  {
    path: "pages/test/market-demo",
    type: "page"
  },
  {
    path: "pages/test/test-instrument-store",
    type: "page",
    style: {
      navigationBarTitleText: "测试Instrument Store"
    }
  },
  {
    path: "pages/test/test-user-selector",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "用户选择器测试",
      navigationStyle: "default"
    }
  },
  {
    path: "pages/trade/CombinationSelector",
    type: "page"
  },
  {
    path: "pages/trade/execute",
    type: "page",
    layout: "tabbar",
    style: {
      navigationBarTitleText: "交易执行"
    }
  },
  {
    path: "pages/trade/pricer-management",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "我的交易请求",
      enablePullDownRefresh: true,
      backgroundTextStyle: "dark",
      onReachBottomDistance: 50,
      backgroundColor: "#f7f8fa",
      pullDownRefresh: {
        color: "#409eff",
        offset: 80
      }
    }
  },
  {
    path: "pages/trade/setter-management",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "交易请求管理",
      enablePullDownRefresh: true,
      backgroundTextStyle: "dark",
      onReachBottomDistance: 50,
      backgroundColor: "#f7f8fa",
      pullDownRefresh: {
        color: "#409eff",
        offset: 80
      }
    }
  },
  {
    path: "pages/trade/TradeOperationForm",
    type: "page"
  }
];
const subPackages = [
  {
    root: "pages-sub",
    pages: [
      {
        path: "demo/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "分包页面"
        }
      }
    ]
  }
];
function getLastPage() {
  const pages2 = getCurrentPages();
  return pages2[pages2.length - 1];
}
function getAllPages(key = "needLogin") {
  const mainPages = pages.filter((page) => !key || page[key]).map((page) => __spreadProps(__spreadValues({}, page), {
    path: `/${page.path}`
  }));
  const subPages = [];
  subPackages.forEach((subPageObj) => {
    const { root } = subPageObj;
    subPageObj.pages.filter((page) => !key || page[key]).forEach((page) => {
      subPages.push(__spreadProps(__spreadValues({}, page), {
        path: `/${root}/${page.path}`
      }));
    });
  });
  const result = [...mainPages, ...subPages];
  return result;
}
const getNeedLoginPages = () => getAllPages("needLogin").map((page) => page.path);
getAllPages("needLogin").map((page) => page.path);
function getEnvBaseUrl() {
  let baseUrl = "http://localhost:8888";
  const VITE_SERVER_BASEURL__WEIXIN_DEVELOP = "http://127.0.0.1:8888";
  const VITE_SERVER_BASEURL__WEIXIN_TRIAL = "https://ukw0y1.laf.run";
  const VITE_SERVER_BASEURL__WEIXIN_RELEASE = "https://ukw0y1.laf.run";
  if (utils_platform.isMpWeixin) {
    const {
      miniProgram: { envVersion }
    } = common_vendor.index.getAccountInfoSync();
    switch (envVersion) {
      case "develop":
        baseUrl = VITE_SERVER_BASEURL__WEIXIN_DEVELOP;
        break;
      case "trial":
        baseUrl = VITE_SERVER_BASEURL__WEIXIN_TRIAL;
        break;
      case "release":
        baseUrl = VITE_SERVER_BASEURL__WEIXIN_RELEASE;
        break;
    }
  }
  return baseUrl;
}
function isTabBarPage(url) {
  if (!url || url === "/" || url === "") {
    const homePages = pages.filter((page) => page.type === "home");
    for (const homePage of homePages) {
      const homePagePath = `/${homePage.path}`;
      if (layouts_fgTabbar_tabbarList.tabbarList.some((item) => `/${item.pagePath}` === homePagePath)) {
        return true;
      }
    }
    return false;
  }
  const normalizedUrl = url.startsWith("/") ? url : `/${url}`;
  const path = normalizedUrl.split("?")[0];
  return layouts_fgTabbar_tabbarList.tabbarList.some((item) => `/${item.pagePath}` === path);
}
function navigateToPage(options) {
  let { url } = options;
  const { success, fail, complete } = options;
  try {
    if (!url || url === "/" || url === "") {
      const homePage = pages.find((page) => page.type === "home");
      if (homePage) {
        url = `/${homePage.path}`;
      } else {
        url = `/${layouts_fgTabbar_tabbarList.tabbarList[0].pagePath}`;
      }
    }
    if (isTabBarPage(url)) {
      common_vendor.index.switchTab({
        url,
        success,
        fail: (error) => {
          console.error("switchTab 失败:", error);
          fail == null ? void 0 : fail(error);
        },
        complete
      });
    } else {
      common_vendor.index.navigateTo({
        url,
        success,
        fail: (error) => {
          console.error("navigateTo 失败:", error);
          fail == null ? void 0 : fail(error);
        },
        complete
      });
    }
  } catch (error) {
    console.error("页面跳转异常:", error);
    fail == null ? void 0 : fail(error);
    complete == null ? void 0 : complete();
  }
}
exports.getEnvBaseUrl = getEnvBaseUrl;
exports.getLastPage = getLastPage;
exports.getNeedLoginPages = getNeedLoginPages;
exports.navigateToPage = navigateToPage;
