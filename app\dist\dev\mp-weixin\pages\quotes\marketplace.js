"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_quotation = require("../../api/quotation.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_action_sheet2 = common_vendor.resolveComponent("wd-action-sheet");
  const _easycom_wd_tag2 = common_vendor.resolveComponent("wd-tag");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_icon2 + _easycom_wd_input2 + _easycom_wd_img2 + _easycom_wd_button2 + _easycom_wd_loading2 + _easycom_wd_action_sheet2 + _easycom_wd_tag2 + _easycom_wd_popup2 + _component_layout_default_uni)();
}
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_action_sheet = () => "../../node-modules/wot-design-uni/components/wd-action-sheet/wd-action-sheet.js";
const _easycom_wd_tag = () => "../../node-modules/wot-design-uni/components/wd-tag/wd-tag.js";
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_input + QuotationCard + _easycom_wd_img + _easycom_wd_button + _easycom_wd_loading + _easycom_wd_action_sheet + _easycom_wd_tag + _easycom_wd_popup)();
}
const QuotationCard = () => "../../components/marketplace/QuotationCard.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "QuotationMarketplace"
}), {
  __name: "marketplace",
  setup(__props) {
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const searchKeyword = common_vendor.ref("");
    const showFilterPanel = common_vendor.ref(false);
    const selectedPriceType = common_vendor.ref("");
    const showActionSheet = common_vendor.ref(false);
    const selectedQuotation = common_vendor.ref(null);
    const quotationList = common_vendor.ref([]);
    const currentPage = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const total = common_vendor.ref(0);
    const priceTypeOptions = [
      { label: "全部类型", value: "" },
      { label: "一口价", value: "Fixed" },
      { label: "基差报价", value: "Basis" }
    ];
    const hasActiveFilters = common_vendor.computed(() => {
      return selectedPriceType.value || searchKeyword.value.trim();
    });
    function loadQuotationList(refresh = false) {
      return __async(this, null, function* () {
        if (refresh) {
          currentPage.value = 1;
          quotationList.value = [];
          isRefreshing.value = true;
        } else {
          isLoading.value = true;
        }
        try {
          const params = {
            page: currentPage.value,
            pageSize: pageSize.value
          };
          if (selectedPriceType.value) {
            params.priceType = selectedPriceType.value;
          }
          if (searchKeyword.value.trim()) {
            params.keyword = searchKeyword.value.trim();
          }
          const res = yield api_quotation.getPublicQuotationList(params);
          const { list, total: totalCount } = res.data;
          if (refresh) {
            quotationList.value = list;
          } else {
            quotationList.value.push(...list);
          }
          total.value = totalCount;
          hasMore.value = quotationList.value.length < totalCount;
        } catch (error) {
          console.error("加载报价列表失败:", error);
          common_vendor.index.showToast({
            title: "加载失败",
            icon: "error"
          });
        } finally {
          isLoading.value = false;
          isRefreshing.value = false;
        }
      });
    }
    function loadMore() {
      return __async(this, null, function* () {
        if (!hasMore.value || isLoading.value)
          return;
        currentPage.value++;
        yield loadQuotationList();
      });
    }
    function onRefresh() {
      return __async(this, null, function* () {
        yield loadQuotationList(true);
      });
    }
    function onSearch() {
      return __async(this, null, function* () {
        yield loadQuotationList(true);
      });
    }
    function clearSearch() {
      return __async(this, null, function* () {
        searchKeyword.value = "";
        yield loadQuotationList(true);
      });
    }
    function showFilters() {
      showFilterPanel.value = true;
    }
    function onPriceTypeFilter(priceType) {
      return __async(this, null, function* () {
        selectedPriceType.value = priceType;
        showFilterPanel.value = false;
        yield loadQuotationList(true);
      });
    }
    function clearFilters() {
      return __async(this, null, function* () {
        selectedPriceType.value = "";
        searchKeyword.value = "";
        showFilterPanel.value = false;
        yield loadQuotationList(true);
      });
    }
    const actionSheetOptions = [
      { name: "查看详情", value: "detail" },
      { name: "发起点价", value: "contact" }
    ];
    function showQuotationActions(quotation) {
      selectedQuotation.value = quotation;
      showActionSheet.value = true;
    }
    function handleActionSelect({ item }) {
      showActionSheet.value = false;
      if (!selectedQuotation.value)
        return;
      switch (item.value) {
        case "detail":
          viewDetail(selectedQuotation.value);
          break;
        case "contact":
          contactPublisher(selectedQuotation.value);
          break;
      }
      selectedQuotation.value = null;
    }
    function viewDetail(quotation) {
      common_vendor.index.navigateTo({
        url: `/pages/quotes/detail?id=${quotation.id}&from=marketplace`
      });
    }
    function contactPublisher(_quotation) {
      common_vendor.index.showToast({
        title: "功能开发中",
        icon: "none"
      });
    }
    function handlePublisherClick(userID) {
      common_vendor.index.navigateTo({
        url: `/pages/quotes/public-list?id=${userID}`
      });
    }
    common_vendor.onMounted(() => __async(this, null, function* () {
      yield loadQuotationList();
    }));
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: common_vendor.p({
          name: "search",
          size: "32rpx",
          ["custom-class"]: "search-icon"
        }),
        b: common_vendor.o(onSearch),
        c: common_vendor.o(clearSearch),
        d: common_vendor.o(($event) => searchKeyword.value = $event),
        e: common_vendor.p({
          placeholder: "搜索报价标题、企业名称...",
          clearable: true,
          ["custom-class"]: "search-input",
          modelValue: searchKeyword.value
        }),
        f: common_vendor.p({
          name: "filter",
          size: "32rpx",
          ["custom-class"]: "filter-icon"
        }),
        g: hasActiveFilters.value
      }, hasActiveFilters.value ? {} : {}, {
        h: common_vendor.o(showFilters),
        i: hasActiveFilters.value
      }, hasActiveFilters.value ? common_vendor.e({
        j: selectedPriceType.value
      }, selectedPriceType.value ? {
        k: common_vendor.t((_a = priceTypeOptions.find((opt) => opt.value === selectedPriceType.value)) == null ? void 0 : _a.label),
        l: common_vendor.o(($event) => onPriceTypeFilter("")),
        m: common_vendor.p({
          name: "close",
          size: "24rpx",
          ["custom-class"]: "tag-close-icon"
        })
      } : {}) : {}, {
        n: quotationList.value.length > 0
      }, quotationList.value.length > 0 ? {
        o: common_vendor.f(quotationList.value, (quotation, k0, i0) => {
          return {
            a: quotation.id,
            b: common_vendor.o(showQuotationActions, quotation.id),
            c: common_vendor.o(handlePublisherClick, quotation.id),
            d: "b051b919-5-" + i0 + ",b051b919-0",
            e: common_vendor.p({
              quotation
            })
          };
        })
      } : !isLoading.value ? common_vendor.e({
        q: common_vendor.p({
          src: "/static/images/empty-market.png",
          width: "200rpx",
          height: "200rpx",
          mode: "aspectFit",
          ["custom-class"]: "empty-image"
        }),
        r: common_vendor.t(hasActiveFilters.value ? "没有找到匹配的报价" : "市场暂无报价"),
        s: hasActiveFilters.value
      }, hasActiveFilters.value ? {
        t: common_vendor.o(clearFilters),
        v: common_vendor.p({
          type: "primary",
          ["custom-class"]: "empty-button"
        })
      } : {}) : {}, {
        p: !isLoading.value,
        w: isLoading.value && quotationList.value.length > 0
      }, isLoading.value && quotationList.value.length > 0 ? {
        x: common_vendor.p({
          size: "24rpx",
          ["custom-class"]: "loading-spinner"
        })
      } : {}, {
        y: !hasMore.value && quotationList.value.length > 0
      }, !hasMore.value && quotationList.value.length > 0 ? {} : {}, {
        z: isRefreshing.value,
        A: common_vendor.o(onRefresh),
        B: common_vendor.o(loadMore),
        C: common_vendor.o(handleActionSelect),
        D: common_vendor.o(($event) => showActionSheet.value = $event),
        E: common_vendor.p({
          actions: actionSheetOptions,
          ["cancel-text"]: "取消",
          ["custom-class"]: "action-sheet",
          modelValue: showActionSheet.value
        }),
        F: common_vendor.o(clearFilters),
        G: common_vendor.p({
          type: "text",
          ["custom-class"]: "reset-button"
        }),
        H: common_vendor.f(priceTypeOptions, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.o(($event) => onPriceTypeFilter(option.value), option.value),
            d: "b051b919-12-" + i0 + ",b051b919-10",
            e: common_vendor.p({
              type: selectedPriceType.value === option.value ? "primary" : "default",
              ["custom-class"]: "filter-tag-item"
            })
          };
        }),
        I: common_vendor.o(($event) => showFilterPanel.value = $event),
        J: common_vendor.p({
          position: "right",
          ["z-index"]: 999,
          ["custom-style"]: "width: 75vw; height: 100vh;",
          ["custom-class"]: "filter-popup",
          modelValue: showFilterPanel.value
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b051b919"]]);
wx.createPage(MiniProgramPage);
