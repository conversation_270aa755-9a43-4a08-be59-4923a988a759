"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_contract = require("../../api/contract.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_tab2 = common_vendor.resolveComponent("wd-tab");
  const _easycom_wd_tabs2 = common_vendor.resolveComponent("wd-tabs");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_button2 + _easycom_wd_tab2 + _easycom_wd_tabs2 + _easycom_wd_loading2 + _component_layout_default_uni)();
}
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_tab = () => "../../node-modules/wot-design-uni/components/wd-tab/wd-tab.js";
const _easycom_wd_tabs = () => "../../node-modules/wot-design-uni/components/wd-tabs/wd-tabs.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
if (!Math) {
  (_easycom_wd_button + _easycom_wd_tab + _easycom_wd_tabs + ContractSummary + ContractCard + _easycom_wd_loading)();
}
const ContractCard = () => "../../components/ContractCard.js";
const ContractSummary = () => "../../components/ContractSummary.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "pricer-list",
  setup(__props) {
    const router = common_vendor.useRouter();
    const contractList = common_vendor.ref([]);
    const currentView = common_vendor.ref("summary");
    const loading = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const currentPage = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const total = common_vendor.ref(0);
    const hasMore = common_vendor.ref(true);
    let currentRequest = null;
    const activeTab = common_vendor.ref("all");
    const statusTabs = common_vendor.ref([
      { label: "全部", value: "all" },
      { label: "未执行", value: "Unexecuted" },
      { label: "执行中", value: "Executing" },
      { label: "已完成", value: "Completed" },
      { label: "已取消", value: "Cancelled" }
    ]);
    const summaryContractList = common_vendor.ref([]);
    function loadSummaryData() {
      return __async(this, null, function* () {
        try {
          const response = yield api_contract.getContractsAsPricer({
            status: "Executing",
            pageSize: 1e3
            // 获取足够多的数据进行汇总
          });
          if (response.code === 0) {
            summaryContractList.value = response.data.list || [];
          }
        } catch (error) {
          console.error("获取汇总数据失败:", error);
        }
      });
    }
    function loadContractList(refresh = false) {
      return __async(this, null, function* () {
        if (refresh) {
          currentPage.value = 1;
          contractList.value = [];
          hasMore.value = true;
          isRefreshing.value = true;
          if (currentRequest) {
            currentRequest = null;
          }
        } else {
          if (isLoading.value || !hasMore.value)
            return;
          isLoading.value = true;
        }
        try {
          const params = {
            page: currentPage.value,
            pageSize: pageSize.value,
            status: activeTab.value === "all" ? void 0 : activeTab.value
          };
          const requestPromise = api_contract.getContractsAsPricer(params);
          currentRequest = requestPromise;
          const response = yield requestPromise;
          if (currentRequest !== requestPromise) {
            return;
          }
          if (response.code === 0) {
            const list = response.data.list || [];
            const totalCount = response.data.total || 0;
            if (refresh) {
              contractList.value = list;
            } else {
              contractList.value.push(...list);
            }
            total.value = totalCount;
            hasMore.value = contractList.value.length < totalCount;
          } else {
            common_vendor.index.showToast({
              title: response.msg || "获取合同列表失败",
              icon: "error"
            });
          }
        } catch (error) {
          console.error("获取合同列表失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        } finally {
          loading.value = false;
          isLoading.value = false;
          isRefreshing.value = false;
        }
      });
    }
    function loadMore() {
      return __async(this, null, function* () {
        if (!hasMore.value || isLoading.value)
          return;
        currentPage.value++;
        yield loadContractList();
      });
    }
    function onRefresh() {
      return __async(this, null, function* () {
        yield loadContractList(true);
      });
    }
    function handleTabChange() {
      return __async(this, null, function* () {
        contractList.value = [];
        hasMore.value = true;
        currentPage.value = 1;
        if (currentRequest) {
          currentRequest = null;
        }
        yield loadContractList(true);
      });
    }
    function switchView(view) {
      currentView.value = view;
      if (view === "detail") {
        loadContractList(true);
      } else if (view === "summary") {
        loadSummaryData();
      }
    }
    function goToDetail(contractId) {
      router.push(`/pages/contract/detail?id=${contractId}&role=pricer`);
    }
    function handleInstrumentClick(data) {
      const { instrumentInfo, userInfo } = data;
      common_vendor.index.navigateTo({
        url: `/pages/trade/execute?instrumentId=${instrumentInfo.instrumentId}&setterID=${userInfo.userID}`
      });
    }
    common_vendor.onMounted(() => {
      loadSummaryData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => switchView("summary")),
        b: common_vendor.p({
          type: currentView.value === "summary" ? "primary" : "info",
          size: "small"
        }),
        c: common_vendor.o(($event) => switchView("detail")),
        d: common_vendor.p({
          type: currentView.value === "detail" ? "primary" : "info",
          size: "small"
        }),
        e: currentView.value === "detail"
      }, currentView.value === "detail" ? {
        f: common_vendor.f(statusTabs.value, (tab, k0, i0) => {
          return {
            a: tab.value,
            b: "9676a60e-4-" + i0 + ",9676a60e-3",
            c: common_vendor.p({
              title: tab.label,
              name: tab.value
            })
          };
        }),
        g: common_vendor.o(handleTabChange),
        h: common_vendor.o(($event) => activeTab.value = $event),
        i: common_vendor.p({
          modelValue: activeTab.value
        })
      } : {}, {
        j: currentView.value === "summary"
      }, currentView.value === "summary" ? {
        k: common_vendor.o(handleInstrumentClick),
        l: common_vendor.p({
          contracts: summaryContractList.value,
          ["user-role"]: "pricer",
          ["enable-click"]: true
        })
      } : {}, {
        m: currentView.value === "detail"
      }, currentView.value === "detail" ? {
        n: common_vendor.f(contractList.value, (contract, k0, i0) => {
          return {
            a: contract.ID,
            b: common_vendor.o(($event) => goToDetail(contract.ID), contract.ID),
            c: "9676a60e-6-" + i0 + ",9676a60e-0",
            d: common_vendor.p({
              contract,
              ["user-role"]: "pricer"
            })
          };
        }),
        o: isRefreshing.value,
        p: common_vendor.o(onRefresh),
        q: common_vendor.o(loadMore)
      } : {}, {
        r: summaryContractList.value.length === 0 && !loading.value
      }, summaryContractList.value.length === 0 && !loading.value ? {} : {}, {
        s: loading.value
      }, loading.value ? {} : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9676a60e"]]);
wx.createPage(MiniProgramPage);
