/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CancelContractDialog: typeof import('./../components/CancelContractDialog.vue')['default']
    CommoditySelector: typeof import('./../components/CommoditySelector.vue')['default']
    ContractCard: typeof import('./../components/ContractCard.vue')['default']
    ContractSummary: typeof import('./../components/ContractSummary.vue')['default']
    InstrumentSelector: typeof import('./../components/InstrumentSelector.vue')['default']
    InstrumentSelectorOrigin: typeof import('./../components/InstrumentSelectorOrigin.vue')['default']
    LimePainter: typeof import('./../components/lime-painter/lime-painter.vue')['default']
    LPainter: typeof import('./../components/l-painter/l-painter.vue')['default']
    LPainterImage: typeof import('./../components/l-painter-image/l-painter-image.vue')['default']
    LPainterQrcode: typeof import('./../components/l-painter-qrcode/l-painter-qrcode.vue')['default']
    LPainterText: typeof import('./../components/l-painter-text/l-painter-text.vue')['default']
    LPainterView: typeof import('./../components/l-painter-view/l-painter-view.vue')['default']
    MpHtml: typeof import('./../components/mp-html/mp-html.vue')['default']
    Node: typeof import('./../components/mp-html/node/node.vue')['default']
    QuotationCard: typeof import('./../components/marketplace/QuotationCard.vue')['default']
    QuotationListPoster: typeof import('./../components/marketplace/QuotationListPoster.vue')['default']
    QuoteCard: typeof import('./../components/QuoteCard.vue')['default']
    SelectInput: typeof import('./../components/SelectInput.vue')['default']
    TradeRequestItem: typeof import('./../components/TradeRequestItem.vue')['default']
    TradeRequestList: typeof import('./../components/TradeRequestList.vue')['default']
    UserSelector: typeof import('./../components/UserSelector.vue')['default']
    ZeroMarkdownView: typeof import('./../components/zero-markdown-view/zero-markdown-view.vue')['default']
  }
}
