"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "ContractSummary",
  props: {
    contracts: {},
    userRole: {},
    enableClick: { type: Boolean, default: false }
  },
  emits: ["instrumentClick"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const expandedUsers = common_vendor.ref(/* @__PURE__ */ new Set());
    const partnerLabel = common_vendor.computed(() => {
      return props.userRole === "pricer" ? "合作伙伴" : "点价方";
    });
    const groupedData = common_vendor.computed(() => {
      const grouped = {};
      props.contracts.forEach((contract) => {
        var _a, _b, _c, _d, _e;
        if (contract.status !== "Executing" || contract.remainingQuantity <= 0) {
          return;
        }
        let userName;
        let userID;
        if (props.userRole === "pricer") {
          userName = ((_a = contract.setter) == null ? void 0 : _a.nickName) || ((_b = contract.setter) == null ? void 0 : _b.userName) || `用户${contract.setterID}`;
          userID = contract.setterID;
        } else {
          userName = ((_c = contract.pricer) == null ? void 0 : _c.nickName) || ((_d = contract.pricer) == null ? void 0 : _d.userName) || `用户${contract.pricerID}`;
          userID = contract.pricerID;
        }
        const instrumentName = ((_e = contract.instrument) == null ? void 0 : _e.instrument_name) || `合约${contract.instrumentRefID}`;
        const instrumentId = contract.instrumentRefID;
        if (!grouped[userName]) {
          grouped[userName] = {
            userID,
            userName,
            instruments: {},
            totalContracts: 0,
            totalQuantity: 0,
            totalAvailable: 0
          };
        }
        if (!grouped[userName].instruments[instrumentName]) {
          grouped[userName].instruments[instrumentName] = {
            instrumentId,
            instrumentName,
            basis: {
              quantity: 0,
              frozenQuantity: 0,
              availableQuantity: 0,
              avgPrice: 0,
              contractCount: 0
            },
            fixed: {
              quantity: 0,
              frozenQuantity: 0,
              availableQuantity: 0,
              avgPrice: 0,
              contractCount: 0
            },
            totalQuantity: 0,
            totalFrozen: 0,
            totalAvailable: 0,
            contracts: []
          };
        }
        const userInfo = grouped[userName];
        const instrumentInfo = userInfo.instruments[instrumentName];
        instrumentInfo.contracts.push(contract);
        const remainingQty = contract.remainingQuantity;
        const frozenQty = contract.frozenQuantity || 0;
        const availableQty = remainingQty - frozenQty;
        if (contract.priceType === "basis") {
          const typeInfo = instrumentInfo.basis;
          const currentTotalValue = typeInfo.quantity * typeInfo.avgPrice;
          const newTotalValue = currentTotalValue + remainingQty * contract.priceValue;
          typeInfo.quantity += remainingQty;
          typeInfo.frozenQuantity += frozenQty;
          typeInfo.availableQuantity += availableQty;
          typeInfo.avgPrice = typeInfo.quantity > 0 ? newTotalValue / typeInfo.quantity : 0;
          typeInfo.contractCount += 1;
        } else if (contract.priceType === "fixed") {
          const typeInfo = instrumentInfo.fixed;
          const currentTotalValue = typeInfo.quantity * typeInfo.avgPrice;
          const newTotalValue = currentTotalValue + remainingQty * contract.priceValue;
          typeInfo.quantity += remainingQty;
          typeInfo.frozenQuantity += frozenQty;
          typeInfo.availableQuantity += availableQty;
          typeInfo.avgPrice = typeInfo.quantity > 0 ? newTotalValue / typeInfo.quantity : 0;
          typeInfo.contractCount += 1;
        }
        instrumentInfo.totalQuantity += remainingQty;
        instrumentInfo.totalFrozen += frozenQty;
        instrumentInfo.totalAvailable += availableQty;
        userInfo.totalContracts += 1;
        userInfo.totalQuantity += remainingQty;
        userInfo.totalAvailable += availableQty;
      });
      return grouped;
    });
    const totalContracts = common_vendor.computed(() => {
      return Object.values(groupedData.value).reduce((sum, user) => sum + user.totalContracts, 0);
    });
    const totalAvailable = common_vendor.computed(() => {
      return Object.values(groupedData.value).reduce((sum, user) => sum + user.totalAvailable, 0);
    });
    function toggleUserExpanded(userName) {
      const key = String(userName);
      if (expandedUsers.value.has(key)) {
        expandedUsers.value.delete(key);
      } else {
        expandedUsers.value.add(key);
      }
    }
    function isUserExpanded(userName) {
      return expandedUsers.value.has(String(userName));
    }
    function handleInstrumentClick(instrumentInfo, userInfo) {
      if (props.enableClick) {
        emit("instrumentClick", { instrumentInfo, userInfo });
      }
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(partnerLabel.value),
        b: common_vendor.t(Object.keys(groupedData.value).length),
        c: common_vendor.t(totalContracts.value),
        d: common_vendor.t(totalAvailable.value),
        e: common_vendor.f(groupedData.value, (userInfo, userName, i0) => {
          return common_vendor.e({
            a: common_vendor.t(userInfo.userName),
            b: common_vendor.t(userInfo.totalContracts),
            c: common_vendor.t(userInfo.totalAvailable),
            d: isUserExpanded(userName) ? 1 : "",
            e: common_vendor.o(($event) => toggleUserExpanded(userName), userName),
            f: isUserExpanded(userName)
          }, isUserExpanded(userName) ? {
            g: common_vendor.f(userInfo.instruments, (instrumentInfo, instrumentName, i1) => {
              return common_vendor.e({
                a: common_vendor.t(instrumentInfo.instrumentName),
                b: common_vendor.t(instrumentInfo.totalQuantity),
                c: common_vendor.t(instrumentInfo.totalAvailable),
                d: instrumentInfo.basis.quantity > 0
              }, instrumentInfo.basis.quantity > 0 ? {
                e: common_vendor.t(instrumentInfo.basis.contractCount),
                f: common_vendor.t(instrumentInfo.basis.quantity),
                g: common_vendor.t(instrumentInfo.basis.frozenQuantity),
                h: common_vendor.t(instrumentInfo.basis.availableQuantity),
                i: common_vendor.t(instrumentInfo.basis.avgPrice.toFixed(2))
              } : {}, {
                j: instrumentInfo.fixed.quantity > 0
              }, instrumentInfo.fixed.quantity > 0 ? {
                k: common_vendor.t(instrumentInfo.fixed.contractCount),
                l: common_vendor.t(instrumentInfo.fixed.quantity),
                m: common_vendor.t(instrumentInfo.fixed.frozenQuantity),
                n: common_vendor.t(instrumentInfo.fixed.availableQuantity),
                o: common_vendor.t(instrumentInfo.fixed.avgPrice.toFixed(2))
              } : {}, {
                p: instrumentInfo.basis.quantity === 0 && instrumentInfo.fixed.quantity === 0
              }, instrumentInfo.basis.quantity === 0 && instrumentInfo.fixed.quantity === 0 ? {} : {}, {
                q: instrumentName,
                r: common_vendor.o(($event) => handleInstrumentClick(instrumentInfo, userInfo), instrumentName)
              });
            }),
            h: _ctx.enableClick ? 1 : ""
          } : {}, {
            i: userName
          });
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cf10ea1b"]]);
wx.createComponent(Component);
