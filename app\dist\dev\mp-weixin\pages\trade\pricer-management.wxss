/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.refresh-indicator.data-v-732d0900 {
  padding: 16rpx;
  margin: 0 20rpx 20rpx 20rpx;
  background-color: #409eff !important;
  color: white;
  text-align: center;
}
.filter-tabs.data-v-732d0900 {
  margin-bottom: 20rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}
.load-more-tip .loading-spinner.data-v-732d0900 {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #409eff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin-732d0900 1s linear infinite;
}
@keyframes spin-732d0900 {
to {
    transform: rotate(360deg);
}
}
.empty-state .empty-icon.data-v-732d0900 {
  width: 128rpx;
  height: 128rpx;
  opacity: 0.3;
}