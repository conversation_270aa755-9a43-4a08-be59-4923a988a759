/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-header.data-v-9676a60e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.page-header .page-title.data-v-9676a60e {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.page-header .view-switch.data-v-9676a60e {
  display: flex;
  gap: 10rpx;
}
.filter-bar.data-v-9676a60e {
  margin-bottom: 30rpx;
}
.filter-tabs.data-v-9676a60e {
  margin-bottom: 20rpx;
}
.scroll-container.data-v-9676a60e {
  height: calc(100vh - 200rpx);
}
.contract-list.data-v-9676a60e {
  padding: 0 20rpx;
}
.detail-view .setter-group.data-v-9676a60e {
  margin-bottom: 40rpx;
}
.detail-view .setter-group .setter-header.data-v-9676a60e {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx 12rpx 0 0;
  padding: 24rpx 30rpx;
}
.detail-view .setter-group .setter-header .setter-name.data-v-9676a60e {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}
.detail-view .setter-group .instrument-group.data-v-9676a60e {
  background: white;
  border-radius: 0 0 12rpx 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.detail-view .setter-group .instrument-group.data-v-9676a60e:not(:last-child) {
  border-radius: 0;
}
.detail-view .setter-group .instrument-group.data-v-9676a60e:last-child {
  border-radius: 0 0 12rpx 12rpx;
}
.detail-view .setter-group .instrument-group .instrument-header.data-v-9676a60e {
  background: #f8f9fa;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e4e7ed;
}
.detail-view .setter-group .instrument-group .instrument-header .instrument-name.data-v-9676a60e {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.detail-view .setter-group .instrument-group .contract-details.data-v-9676a60e {
  padding: 20rpx 30rpx;
}
.detail-view .setter-group .instrument-group .contract-details .contract-detail-item.data-v-9676a60e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}
.detail-view .setter-group .instrument-group .contract-details .contract-detail-item.data-v-9676a60e:last-child {
  margin-bottom: 0;
}
.detail-view .setter-group .instrument-group .contract-details .contract-detail-item.data-v-9676a60e:active {
  background: #e9ecef;
  transform: scale(0.98);
}
.detail-view .setter-group .instrument-group .contract-details .contract-detail-item .contract-basic.data-v-9676a60e {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.detail-view .setter-group .instrument-group .contract-details .contract-detail-item .contract-basic .contract-code.data-v-9676a60e {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.detail-view .setter-group .instrument-group .contract-details .contract-detail-item .contract-meta.data-v-9676a60e {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 24rpx;
}
.detail-view .setter-group .instrument-group .contract-details .contract-detail-item .contract-meta .quantity.data-v-9676a60e {
  color: #333;
  font-weight: 500;
}
.detail-view .setter-group .instrument-group .contract-details .contract-detail-item .contract-meta .price.data-v-9676a60e {
  color: #666;
}
.detail-view .setter-group .instrument-group .contract-details .contract-detail-item .contract-meta .date.data-v-9676a60e {
  color: #999;
}
.empty-state.data-v-9676a60e, .loading-state.data-v-9676a60e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
  gap: 30rpx;
}