# 功能名称：全局统一底部导航栏 (Global Unified Bottom Tabbar)

## 1. 功能简介

本项目旨在设计并实现一个全局统一的底部 Tabbar 组件。该组件将作为应用核心功能模块（如工作台、报价市场、我的）的主要导航入口，确保在 H5、小程序、App 等多端环境下提供一致、流畅的视觉和交互体验。

我们将采用 `wot-design-uni` UI 组件库中的 `wd-tabbar` 组件作为基础，通过 Uni-app 的自定义 Tabbar 功能进行实现，以达到高度的可定制性和统一的维护性。

## 1.1 路径修复与优化方案

### 1.1.1 路径错误分析

通过对比文档与 `pages.json` 实际配置，发现以下路径不匹配问题：

| 文档中的路径 | 实际pages.json中的路径 | 状态 |
|-------------|---------------------|------|
| `/pages/workbench/index` | `pages/workspace/index` | ❌ 路径错误 |
| `/pages/quotation/index` | `pages/quotes/marketplace` | ❌ 路径错误 |
| `/pages/my/index` | `pages/profile/index` | ❌ 路径错误 |

### 1.1.2 基于现有fg-tabbar的优化策略

现有 `fg-tabbar` 实现具有以下优势，应当保留和扩展：
- **策略模式设计** - 支持多种tabbar策略切换
- **状态管理** - 使用reactive状态管理，支持持久化
- **多图标类型支持** - 支持UI库图标、UnoCSS图标、本地图标、字体图标
- **缓存机制** - 智能的页面缓存策略

## 2. 数据定义与路径修复

### 2.1 修正后的数据结构

基于现有 `fg-tabbar` 架构，扩展业务场景的配置。复用现有的 `FgTabBarItem` 类型定义：

```typescript
// 在 app/src/layouts/fg-tabbar/tabbarList.ts 中扩展
import type { TabBar } from '@uni-helper/vite-plugin-uni-pages'

type FgTabBarItem = TabBar['list'][0] & {
  icon: string
  iconType: 'uiLib' | 'unocss' | 'iconfont' | 'local'
}

// 添加业务场景的tabbar策略
export const TABBAR_MAP = {
  NO_TABBAR: 0,
  NATIVE_TABBAR: 1,
  CUSTOM_TABBAR_WITH_CACHE: 2,
  CUSTOM_TABBAR_WITHOUT_CACHE: 3,
  BUSINESS_TABBAR: 4,  // 新增业务场景
}

// 修正后的业务tabbar配置（匹配实际页面路径）
export const businessTabbarList: FgTabBarItem[] = [
  {
    iconPath: 'static/tabbar/workspace.png',
    selectedIconPath: 'static/tabbar/workspaceHL.png',
    pagePath: 'pages/workspace/index',  // 匹配实际页面
    text: '工作台',
    icon: 'home',
    iconType: 'uiLib',
  },
  {
    iconPath: 'static/tabbar/quotes.png',
    selectedIconPath: 'static/tabbar/quotesHL.png',
    pagePath: 'pages/quotes/marketplace',  // 匹配实际页面
    text: '报价市场',
    icon: 'cart',
    iconType: 'uiLib',
  },
  {
    iconPath: 'static/tabbar/profile.png',
    selectedIconPath: 'static/tabbar/profileHL.png',
    pagePath: 'pages/profile/index',  // 匹配实际页面
    text: '我的',
    icon: 'user',
    iconType: 'uiLib',
  }
];

// 动态选择tabbar配置
export const getActiveTabbarList = () => {
  switch (selectedTabbarStrategy) {
    case TABBAR_MAP.BUSINESS_TABBAR:
      return businessTabbarList;
    default:
      return tabbarList;
  }
};
```

## 3. 实施方案详细设计

### 3.1 路径修复策略

#### 方案A：修改配置以匹配现有页面（推荐）
直接使用现有页面路径，无需创建新页面：
- `pages/workspace/index` - 工作台页面
- `pages/quotes/marketplace` - 报价市场页面
- `pages/profile/index` - 我的页面

#### 方案B：创建新页面以匹配文档路径
如果业务需要严格按照原文档路径，需要创建：
- `pages/workbench/index.vue`
- `pages/quotation/index.vue`
- `pages/my/index.vue`

### 3.2 环境与依赖准备

1.  **确认 `wot-design-uni` 已安装**
    项目中已安装，无需额外操作。

2.  **验证组件库引入**
    确保 `wot-design-uni` 已在项目中正确配置。

### 3.3 复用现有fg-tabbar组件

**推荐方案**：直接扩展现有的 `app/src/layouts/fg-tabbar/fg-tabbar.vue` 组件，而不是创建新组件。

#### 3.3.1 扩展fg-tabbar组件支持业务场景

```vue
<!-- 在 app/src/layouts/fg-tabbar/fg-tabbar.vue 中修改 -->
<script setup lang="ts">
import { tabbarStore } from './tabbar'
import {
  tabbarList as _tabBarList,
  businessTabbarList,
  cacheTabbarEnable,
  selectedTabbarStrategy,
  TABBAR_MAP,
  getActiveTabbarList
} from './tabbarList'

const customTabbarEnable
= selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE
  || selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITHOUT_CACHE
  || selectedTabbarStrategy === TABBAR_MAP.BUSINESS_TABBAR

// 动态获取当前使用的tabbar配置
const activeTabbarConfig = getActiveTabbarList()
const tabbarList = activeTabbarConfig.map(item => ({ ...item, path: `/${item.pagePath}` }))

function selectTabBar({ value: index }: { value: number }) {
  const url = tabbarList[index].path
  tabbarStore.setCurIdx(index)
  if (cacheTabbarEnable || selectedTabbarStrategy === TABBAR_MAP.BUSINESS_TABBAR) {
    uni.switchTab({ url })
  }
  else {
    uni.navigateTo({ url })
  }
}

onLoad(() => {
  // 解决原生 tabBar 未隐藏导致有2个 tabBar 的问题
  const hideRedundantTabbarEnable = selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE
    || selectedTabbarStrategy === TABBAR_MAP.BUSINESS_TABBAR
  hideRedundantTabbarEnable
  && uni.hideTabBar({
    fail(err) {
      console.log('hideTabBar fail: ', err)
    },
    success(res) {
      console.log('hideTabBar success: ', res)
    },
  })
})
</script>
```

### 3.4 配置 `pages.json` 更新

#### 3.4.1 页面layout配置优化

为业务页面添加正确的layout配置：

```json
{
  "path": "pages/workspace/index",
  "type": "home",
  "layout": "tabbar",  // 使用tabbar布局
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "工作台"
  }
},
{
  "path": "pages/quotes/marketplace",
  "type": "page",
  "layout": "tabbar",  // 使用tabbar布局
  "style": {
    "navigationBarTitleText": "报价市场",
    "navigationStyle": "default"
  }
},
{
  "path": "pages/profile/index",
  "type": "page",
  "layout": "tabbar",  // 使用tabbar布局
  "style": {
    "navigationBarTitleText": "我的"
  }
}
```

#### 3.4.2 tabBar配置更新

修改 `pages.json` 中的 tabBar 配置以匹配实际页面路径：

```json
{
  "tabBar": {
    "custom": false,  // 根据策略动态设置
    "color": "#999999",
    "selectedColor": "#018d71",
    "backgroundColor": "#F8F8F8",
    "borderStyle": "black",
    "list": [
      {
        "iconPath": "static/tabbar/workspace.png",
        "selectedIconPath": "static/tabbar/workspaceHL.png",
        "pagePath": "pages/workspace/index",
        "text": "工作台"
      },
      {
        "iconPath": "static/tabbar/quotes.png",
        "selectedIconPath": "static/tabbar/quotesHL.png",
        "pagePath": "pages/quotes/marketplace",
        "text": "报价市场"
      },
      {
        "iconPath": "static/tabbar/profile.png",
        "selectedIconPath": "static/tabbar/profileHL.png",
        "pagePath": "pages/profile/index",
        "text": "我的"
      }
    ]
  }
}
```

**重要**: 所有路径必须与 `tabbarList.ts` 中定义的路径完全一致。

### 3.5 状态管理扩展

扩展现有的 `tabbar.ts` 状态管理以支持策略切换：

```typescript
// 在 app/src/layouts/fg-tabbar/tabbar.ts 中扩展
export const tabbarStore = reactive({
  curIdx: uni.getStorageSync('app-tabbar-index') || 0,
  strategy: selectedTabbarStrategy,
  setCurIdx(idx: number) {
    this.curIdx = idx;
    uni.setStorageSync('app-tabbar-index', idx);
  },
  setStrategy(strategy: number) {
    this.strategy = strategy;
    this.curIdx = 0; // 重置索引
    uni.setStorageSync('app-tabbar-strategy', strategy);
  }
});
```

### 3.6 处理页面内容遮挡问题

由于自定义 Tabbar 是 `fixed` 定位组件，需要为页面内容预留空间。现有 `fg-tabbar` 组件已通过 `placeholder` 属性处理此问题。

**验证方案**:
1. 确认 `fg-tabbar.vue` 中的 `wd-tabbar` 组件包含 `placeholder` 属性
2. 测试页面底部内容是否被遮挡
3. 如有遮挡，可添加全局样式类：

```css
/* 如需额外处理遮挡问题 */
.page-container-with-tabbar {
  padding-bottom: 65px;
  box-sizing: border-box;
}
```

## 4. 实施步骤建议

### 4.1 第一阶段：路径修复
1. **确认页面存在性** - 验证目标页面是否已创建
2. **统一路径配置** - 更新所有配置文件中的路径引用
3. **测试路径跳转** - 确保所有路径都能正确跳转

### 4.2 第二阶段：配置扩展
1. **扩展tabbarList.ts** - 添加业务场景配置
2. **更新fg-tabbar组件** - 支持动态配置切换
3. **优化状态管理** - 增强状态持久化和策略管理

### 4.3 第三阶段：集成测试
1. **多端测试** - H5、小程序、App端功能验证
2. **缓存测试** - 页面缓存和状态持久化测试
3. **性能测试** - 确保切换流畅性

## 5. 接口定义

**`fg-tabbar/fg-tabbar.vue` 组件（复用现有）**

*   **Props**: 无
*   **Events**: 无（事件处理在组件内部完成，通过 `uni.switchTab` 与应用其他部分交互）
*   **依赖**: `wot-design-uni` 的 `wd-tabbar` 和 `wd-tabbar-item` 组件

## 6. 相关页面

*   `pages/workspace/index.vue` - 工作台页面
*   `pages/quotes/marketplace.vue` - 报价市场页面
*   `pages/profile/index.vue` - 我的页面
*   `app/src/layouts/fg-tabbar/fg-tabbar.vue` (复用并扩展)
*   `app/src/layouts/fg-tabbar/tabbarList.ts` (扩展配置)
*   `app/src/layouts/fg-tabbar/tabbar.ts` (扩展状态管理)
*   `app/src/pages.json` (修改配置)

## 6. 测试用例

| 测试项 | 测试步骤 | 预期结果 | 平台 |
| :--- | :--- | :--- | :--- |
| **基础渲染** | 1. 进入任意一个 Tabbar 页面（如工作台）。 | 1. 页面底部正确显示 Tabbar。 <br> 2. Tabbar 上有“工作台”、“报价市场”、“我的”三个标签。 <br> 3. “工作台”标签处于高亮选中状态。 | H5, 小程序, App |
| **导航功能** | 1. 在工作台页面，点击“报价市场”标签。 <br> 2. 再点击“我的”标签。 | 1. 页面成功跳转到报价市场页面，且“报价市场”标签高亮。 <br> 2. 页面成功跳转到我的页面，且“我的”标签高亮。 | H5, 小程序, App |
| **状态持久化** | 1. 从“我的”页面切换到“工作台”。 <br> 2. 刷新页面（H5）或退出重进（小程序）。 | 1. 页面仍停留在工作台页面，且“工作台”标签高亮。 | H5, 小程序 |
| **非 Tabbar 页面** | 1. 从工作台页面进入一个详情页（非 Tabbar 页面）。 | 1. 底部 Tabbar 消失。 | H5, 小程序, App |
| **内容避让** | 1. 在每个 Tabbar 页面滚动到最底部。 | 1. 页面最下方的所有内容都不会被 Tabbar 遮挡。 | H5, 小程序, App |
| **策略切换** | 1. 修改 `selectedTabbarStrategy` 配置。<br> 2. 重新运行项目。 | 1. Tabbar 按新策略正常工作。<br> 2. 状态管理正确切换。 | 开发环境 |

## 8. 注意事项和最佳实践

### 8.1 路径一致性原则
- 所有配置文件中的页面路径必须完全一致
- 使用相对路径，不要包含前导斜杠
- 确保页面文件实际存在

### 8.2 策略切换管理
- 通过环境变量或配置文件控制策略切换
- 避免硬编码策略选择
- 提供开发和生产环境的不同配置

### 8.3 性能优化建议
- 合理使用页面缓存策略
- 避免频繁的状态更新
- 优化图标资源加载

### 8.4 维护性考虑
- 保持配置文件的可读性
- 添加详细的注释说明
- 建立配置变更的测试流程

## 9. 风险评估与应对

### 9.1 潜在风险
- **页面不存在** - 配置的页面路径对应的文件不存在
- **缓存冲突** - 不同策略间的缓存数据冲突
- **多端兼容性** - 不同平台的表现差异

### 9.2 应对措施
- **路径验证** - 实施前验证所有页面路径
- **缓存隔离** - 不同策略使用不同的缓存key
- **渐进式部署** - 先在单一平台测试，再扩展到其他平台

## 10. 原有注意事项

1.  **内容遮挡**：现有 `fg-tabbar` 组件已通过 `placeholder` 属性处理遮挡问题，如仍有遮挡可添加额外的 `padding-bottom`。
2.  **图标库**：复用现有的多图标类型支持（uiLib、unocss、iconfont、local），根据需要选择合适的图标类型。
3.  **角标 (Badge)**：可扩展现有状态管理，通过 `tabbarStore` 维护角标数量，传递给 `wd-tabbar-item` 的 `badge` 相关 props。
4.  **性能**：现有 `fg-tabbar` 已优化性能，支持多种策略切换以适应不同性能需求。
5.  **路径一致性**：**关键修复点** - 确保 `pages.json` 和 `tabbarList.ts` 中的页面路径完全一致，使用相对路径（不含前导 `/`）。
6.  **策略选择**：通过修改 `selectedTabbarStrategy` 可灵活切换不同的 tabbar 实现策略。
7.  **向后兼容**：新的业务配置不影响现有的 tabbar 功能，可平滑迁移。
