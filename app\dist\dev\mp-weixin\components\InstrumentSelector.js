"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const store_instrument = require("../store/instrument.js");
const types_instrument = require("../types/instrument.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_search2 = common_vendor.resolveComponent("wd-search");
  const _easycom_wd_radio2 = common_vendor.resolveComponent("wd-radio");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_icon2 + _easycom_wd_input2 + _easycom_wd_button2 + _easycom_wd_search2 + _easycom_wd_radio2 + _easycom_wd_popup2)();
}
const _easycom_wd_icon = () => "../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_input = () => "../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_button = () => "../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_search = () => "../node-modules/wot-design-uni/components/wd-search/wd-search.js";
const _easycom_wd_radio = () => "../node-modules/wot-design-uni/components/wd-radio/wd-radio.js";
const _easycom_wd_popup = () => "../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_input + _easycom_wd_button + _easycom_wd_search + _easycom_wd_radio + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "InstrumentSelector"
}), {
  __name: "InstrumentSelector",
  props: {
    modelValue: {
      type: Number,
      default: 0
    },
    label: {
      type: String,
      default: "期货合约"
    },
    labelWidth: {
      type: String,
      default: "160rpx"
    },
    placeholder: {
      type: String,
      default: "请选择期货合约"
    },
    required: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  emits: ["update:modelValue", "change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const showModal = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const searchKeyword = common_vendor.ref("");
    const selectedInstrument = common_vendor.ref(null);
    const currentStep = common_vendor.ref(0);
    const selectedExchange = common_vendor.ref("");
    const selectedProduct = common_vendor.ref("");
    const instrumentStore = store_instrument.useInstrumentStore();
    const currentList = common_vendor.computed(() => {
      const groupedData = instrumentStore.getGroupedData();
      if (currentStep.value === 0) {
        return groupedData.exchanges.filter(
          (exchange) => !searchKeyword.value || exchange.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
        );
      } else if (currentStep.value === 1) {
        const products = groupedData.productsByExchange[selectedExchange.value] || [];
        return products.filter(
          (product) => !searchKeyword.value || product.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
        );
      } else {
        const key = `${selectedExchange.value}_${selectedProduct.value}`;
        const contracts = groupedData.contractsByProduct[key] || [];
        return contracts.filter(
          (contract) => !searchKeyword.value || contract.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
        );
      }
    });
    const modalTitle = common_vendor.computed(() => {
      if (currentStep.value === 0)
        return "选择交易所";
      if (currentStep.value === 1)
        return "选择商品";
      return "选择合约";
    });
    const breadcrumb = common_vendor.computed(() => {
      const items = [];
      if (selectedExchange.value) {
        items.push(types_instrument.ExchangeMap[selectedExchange.value] || selectedExchange.value);
      }
      if (selectedProduct.value) {
        items.push(selectedProduct.value);
      }
      return items.join(" > ");
    });
    common_vendor.watch(() => props.modelValue, (newId) => {
      if (newId) {
        const instrument = instrumentStore.getInstrumentById(newId);
        selectedInstrument.value = instrument;
      } else {
        selectedInstrument.value = null;
      }
    }, { immediate: true });
    function initializeData() {
      return __async(this, null, function* () {
        try {
          isLoading.value = true;
          const result = yield instrumentStore.loadInstruments();
          if (!result.success) {
            common_vendor.index.showToast({ title: result.error || "数据加载失败，请重试", icon: "error" });
            return;
          }
          if (props.modelValue) {
            const instrument = instrumentStore.getInstrumentById(props.modelValue);
            if (instrument) {
              selectedInstrument.value = instrument;
            }
          }
        } catch (error) {
          common_vendor.index.showToast({ title: "数据加载失败，请重试", icon: "error" });
          throw error;
        } finally {
          isLoading.value = false;
        }
      });
    }
    function showInstrumentPicker() {
      resetSelection();
      showModal.value = true;
    }
    function resetSelection() {
      currentStep.value = 0;
      selectedExchange.value = "";
      selectedProduct.value = "";
      searchKeyword.value = "";
    }
    function searchInstruments() {
    }
    function handleItemClick(item) {
      if (currentStep.value === 0) {
        selectedExchange.value = item.value;
        currentStep.value = 1;
        searchKeyword.value = "";
      } else if (currentStep.value === 1) {
        selectedProduct.value = item.value;
        currentStep.value = 2;
        searchKeyword.value = "";
      } else {
        confirmInstrumentSelection(item.instrumentData);
      }
    }
    function confirmInstrumentSelection(instrument) {
      selectedInstrument.value = instrument;
      showModal.value = false;
      emit("update:modelValue", instrument.id);
      emit("change", instrument.id, instrument);
      resetSelection();
    }
    function goBack() {
      if (currentStep.value > 0) {
        currentStep.value--;
        searchKeyword.value = "";
        if (currentStep.value === 0) {
          selectedExchange.value = "";
          selectedProduct.value = "";
        } else if (currentStep.value === 1) {
          selectedProduct.value = "";
        }
      }
    }
    function removeInstrument() {
      selectedInstrument.value = null;
      emit("update:modelValue", 0);
      emit("change", 0, null);
    }
    const displayValue = common_vendor.computed(() => {
      if (selectedInstrument.value) {
        return `${types_instrument.ExchangeMap[selectedInstrument.value.exchange_id] || selectedInstrument.value.exchange_id}.${selectedInstrument.value.instrument_id}`;
      }
      return "";
    });
    common_vendor.onMounted(() => __async(this, null, function* () {
      try {
        yield initializeData();
      } catch (error) {
        console.error("数据初始化失败:", error);
        common_vendor.index.showModal({
          title: "加载失败",
          content: "数据加载失败，请检查网络连接后重试",
          showCancel: true,
          confirmText: "重试",
          success: (res) => {
            if (res.confirm) {
              initializeData();
            }
          }
        });
      }
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {
        b: common_vendor.p({
          name: "loading",
          ["custom-class"]: "loading-icon"
        })
      } : {
        c: common_vendor.p({
          name: "arrow-right",
          ["custom-class"]: "arrow-icon"
        })
      }, {
        d: common_vendor.o(showInstrumentPicker),
        e: common_vendor.o(removeInstrument),
        f: common_vendor.p({
          ["model-value"]: displayValue.value,
          label: __props.label,
          ["label-width"]: __props.labelWidth,
          placeholder: __props.placeholder,
          required: __props.required,
          disabled: __props.disabled,
          clearable: __props.clearable && !!selectedInstrument.value,
          readonly: true
        }),
        g: currentStep.value > 0
      }, currentStep.value > 0 ? {
        h: common_vendor.o(goBack),
        i: common_vendor.p({
          type: "info",
          size: "small",
          ["custom-class"]: "back-btn"
        })
      } : {}, {
        j: common_vendor.t(modalTitle.value),
        k: breadcrumb.value
      }, breadcrumb.value ? {
        l: common_vendor.t(breadcrumb.value)
      } : {}, {
        m: common_vendor.o(searchInstruments),
        n: common_vendor.o(($event) => searchKeyword.value = $event),
        o: common_vendor.p({
          placeholder: `搜索${modalTitle.value.replace("选择", "")}`,
          ["custom-class"]: "dj-search",
          modelValue: searchKeyword.value
        }),
        p: common_vendor.f(currentList.value, (item, k0, i0) => {
          var _a, _b, _c;
          return common_vendor.e({
            a: common_vendor.t(item.label)
          }, currentStep.value === 2 ? {
            b: common_vendor.t((_a = item.instrumentData) == null ? void 0 : _a.product_name)
          } : {}, currentStep.value < 2 ? {} : {
            c: "c01cf354-6-" + i0 + ",c01cf354-3",
            d: common_vendor.p({
              value: ((_b = selectedInstrument.value) == null ? void 0 : _b.id) === ((_c = item.instrumentData) == null ? void 0 : _c.id),
              ["custom-class"]: "dj-radio"
            })
          }, {
            e: item.value,
            f: common_vendor.o(($event) => handleItemClick(item), item.value)
          });
        }),
        q: currentStep.value === 2,
        r: currentStep.value < 2,
        s: currentList.value.length === 0 && !isLoading.value
      }, currentList.value.length === 0 && !isLoading.value ? {} : {}, {
        t: isLoading.value
      }, isLoading.value ? {} : {}, {
        v: common_vendor.o(($event) => showModal.value = $event),
        w: common_vendor.p({
          position: "bottom",
          ["custom-style"]: "height: 80%",
          ["custom-class"]: "dj-popup",
          modelValue: showModal.value
        })
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c01cf354"]]);
wx.createComponent(Component);
