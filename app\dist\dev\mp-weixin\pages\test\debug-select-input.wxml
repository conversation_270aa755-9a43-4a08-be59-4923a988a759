<layout-default-uni class="data-v-cc338929" u-s="{{['d']}}" u-i="cc338929-0" bind:__l="__l"><view class="debug-container data-v-cc338929"><view class="test-section data-v-cc338929"><text class="section-title data-v-cc338929">SelectInput 功能测试</text><select-input wx:if="{{d}}" class="data-v-cc338929" bindchange="{{a}}" bindsearch="{{b}}" u-i="cc338929-1,cc338929-0" bind:__l="__l" bindupdateModelValue="{{c}}" u-p="{{d}}"/><view class="current-value data-v-cc338929"><text class="label data-v-cc338929">当前值: </text><text class="value data-v-cc338929">{{e}}</text></view><view class="button-group data-v-cc338929"><button class="debug-btn data-v-cc338929" bindtap="{{f}}">清空值</button><button class="debug-btn data-v-cc338929" bindtap="{{g}}">清空日志</button></view></view><view class="debug-section data-v-cc338929"><text class="section-title data-v-cc338929">调试信息</text><view class="debug-info data-v-cc338929"><view wx:for="{{h}}" wx:for-item="info" wx:key="b" class="debug-item data-v-cc338929">{{info.a}}</view><view wx:if="{{i}}" class="no-debug data-v-cc338929"> 暂无调试信息 </view></view><text class="section-title data-v-cc338929">选项数据</text><view class="options-info data-v-cc338929"><view wx:for="{{j}}" wx:for-item="option" wx:key="d" class="option-item data-v-cc338929"><text class="option-value data-v-cc338929">{{option.a}}</text><text class="option-label data-v-cc338929">{{option.b}}</text><text class="option-desc data-v-cc338929">{{option.c}}</text></view></view></view></view></layout-default-uni>