/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.debug-container.data-v-cc338929 {
  padding: 20rpx;
  min-height: 100vh;
  background: #f5f5f5;
}
.test-section.data-v-cc338929, .debug-section.data-v-cc338929 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
  overflow: visible;
}
.section-title.data-v-cc338929 {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 30rpx;
  display: block;
}
.current-value.data-v-cc338929 {
  margin: 30rpx 0;
  padding: 20rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  border-left: 4rpx solid #3b82f6;
}
.current-value .label.data-v-cc338929 {
  font-weight: 500;
  color: #374151;
}
.current-value .value.data-v-cc338929 {
  color: #1f2937;
  font-family: monospace;
}
.button-group.data-v-cc338929 {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}
.debug-btn.data-v-cc338929 {
  flex: 1;
  padding: 20rpx;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.debug-info.data-v-cc338929 {
  max-height: 600rpx;
  overflow-y: auto;
  background: #1f2937;
  border-radius: 8rpx;
  padding: 20rpx;
}
.debug-item.data-v-cc338929 {
  color: #10b981;
  font-family: monospace;
  font-size: 24rpx;
  line-height: 1.5;
  margin-bottom: 8rpx;
  word-break: break-all;
}
.no-debug.data-v-cc338929 {
  color: #6b7280;
  text-align: center;
  padding: 40rpx;
}
.options-info.data-v-cc338929 {
  border: 1rpx solid #e5e7eb;
  border-radius: 8rpx;
  overflow: hidden;
}
.option-item.data-v-cc338929 {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 20rpx;
  border-bottom: 1rpx solid #f3f4f6;
}
.option-item.data-v-cc338929:last-child {
  border-bottom: none;
}
.option-item .option-value.data-v-cc338929 {
  font-family: monospace;
  color: #7c3aed;
  font-weight: 500;
}
.option-item .option-label.data-v-cc338929 {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}
.option-item .option-desc.data-v-cc338929 {
  font-size: 24rpx;
  color: #6b7280;
}