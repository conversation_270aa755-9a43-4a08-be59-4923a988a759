<view class="user-selector-wrapper data-v-d36d9223"><wd-input wx:if="{{f}}" class="data-v-d36d9223" u-s="{{['suffix']}}" bindclick="{{d}}" bindclear="{{e}}" u-i="d36d9223-0" bind:__l="__l" u-p="{{f}}"><view class="input-suffix data-v-d36d9223" slot="suffix"><wd-icon wx:if="{{a}}" class="data-v-d36d9223" u-i="d36d9223-1,d36d9223-0" bind:__l="__l" u-p="{{b}}"/><wd-icon wx:else class="data-v-d36d9223" u-i="d36d9223-2,d36d9223-0" bind:__l="__l" u-p="{{c||''}}"/></view></wd-input><wd-popup wx:if="{{o}}" class="data-v-d36d9223" u-s="{{['d']}}" u-i="d36d9223-3" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"><view class="user-picker data-v-d36d9223"><view class="picker-header data-v-d36d9223"><view class="header-content data-v-d36d9223"><view class="title-section data-v-d36d9223"><text class="picker-title data-v-d36d9223">{{g}}</text></view></view></view><view class="picker-content data-v-d36d9223"><wd-search wx:if="{{j}}" class="data-v-d36d9223" bindsearch="{{h}}" u-i="d36d9223-4,d36d9223-3" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"/><view class="user-list data-v-d36d9223"><view wx:for="{{k}}" wx:for-item="user" wx:key="e" class="user-item data-v-d36d9223" bindtap="{{user.f}}"><view class="item-info data-v-d36d9223"><text class="item-name data-v-d36d9223">{{user.a}}</text><text class="item-phone data-v-d36d9223">{{user.b}}</text></view><view class="item-arrow data-v-d36d9223"><wd-radio wx:if="{{user.d}}" class="data-v-d36d9223" u-i="{{user.c}}" bind:__l="__l" u-p="{{user.d}}"/></view></view></view><view wx:if="{{l}}" class="empty-list data-v-d36d9223"><text class="data-v-d36d9223">暂无数据</text></view><view wx:if="{{m}}" class="loading-indicator data-v-d36d9223"><text class="data-v-d36d9223">加载中...</text></view></view></view></wd-popup></view>