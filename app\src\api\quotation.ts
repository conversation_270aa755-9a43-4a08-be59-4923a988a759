import { http } from '@/http/http'
import type {
  ICreateQuotationRequest,
  IUpdateQuotationRequest,
  IPublishQuotationRequest,
  IQuotationListRequest,
  IMyQuotationListRequest,
  IQuotationResponse,
  IQuotationListResponse,
  IQuotationFilterOptions,
  IQuotationStats
} from '@/types/quotation'

// ============ 报价管理API ============

/**
 * 创建报价
 */
export function createQuotation(data: ICreateQuotationRequest): Promise<IResData<IQuotationResponse>> {
  return http.post<IQuotationResponse>('/dianjia/quotations', data)
}

/**
 * 更新报价
 */
export function updateQuotation(data: IUpdateQuotationRequest): Promise<IResData<IQuotationResponse>> {
  return http.put<IQuotationResponse>(`/dianjia/quotations/${data.id}`, data)
}

/**
 * 删除报价（仅限草稿）
 */
export function deleteQuotation(quotationId: number): Promise<IResData<any>> {
  return http.delete(`/dianjia/quotations/${quotationId}`)
}

/**
 * 发布报价（将Draft状态改为Active，并设置过期时间）
 */
export function publishQuotation(data: IPublishQuotationRequest): Promise<IResData<any>> {
  return http.post(`/dianjia/quotations/${data.id}/publish`, data)
}

/**
 * 切换报价状态（Active <-> Draft）
 */
export function toggleQuotationStatus(quotationId: number): Promise<IResData<any>> {
  return http.post(`/dianjia/quotations/${quotationId}/toggle`)
}

// ============ 报价列表API ============

/**
 * 获取公开报价列表（公开市场）
 */
export function getPublicQuotationList(params?: IQuotationListRequest): Promise<IResData<IQuotationListResponse>> {
  return http.get<IQuotationListResponse>('/dianjia/public-quotations', params)
}

/**
 * 获取我的报价列表
 */
export function getMyQuotationList(params?: IMyQuotationListRequest): Promise<IResData<IQuotationListResponse>> {
  return http.get<IQuotationListResponse>('/dianjia/my-quotations', params)
}

/**
 * 获取公开报价详情（无需认证）
 */
export function getPublicQuotationDetail(quotationId: number): Promise<IResData<IQuotationResponse>> {
  return http.get<IQuotationResponse>(`/dianjia/public-quotations/${quotationId}`)
}

/**
 * 获取报价详情（需要认证，带权限验证）
 */
export function getQuotationDetail(quotationId: number): Promise<IResData<IQuotationResponse>> {
  return http.get<IQuotationResponse>(`/dianjia/quotations/${quotationId}`)
}

// ============ 辅助功能API ============

/**
 * 获取报价筛选选项（用于公开市场筛选）
 */
export function getQuotationFilterOptions(): Promise<IResData<IQuotationFilterOptions>> {
  return http.get<IQuotationFilterOptions>('/dianjia/quotations/filter-options')
}

/**
 * 获取用户报价统计信息
 */
export function getMyQuotationStats(): Promise<IResData<IQuotationStats>> {
  return http.get<IQuotationStats>('/dianjia/my-quotations/stats')
}

// ============ 快捷操作方法 ============

/**
 * 创建并发布报价（一步操作）
 */
export function createAndPublishQuotation(data: ICreateQuotationRequest): Promise<IResData<IQuotationResponse>> {
  const requestData = { ...data, status: 'Active' as const }
  return createQuotation(requestData)
}

/**
 * 保存报价草稿
 */
export function saveQuotationDraft(data: ICreateQuotationRequest): Promise<IResData<IQuotationResponse>> {
  const requestData = { ...data, status: 'Draft' as const }
  return createQuotation(requestData)
}

/**
 * 获取有效报价列表（我的激活中报价）
 */
export function getMyActiveQuotations(params?: Omit<IMyQuotationListRequest, 'filter'>): Promise<IResData<IQuotationListResponse>> {
  return getMyQuotationList({ ...params, filter: 'valid' })
}

/**
 * 获取无效报价列表（我的草稿、过期、撤回报价）
 */
export function getMyInactiveQuotations(params?: Omit<IMyQuotationListRequest, 'filter'>): Promise<IResData<IQuotationListResponse>> {
  return getMyQuotationList({ ...params, filter: 'invalid' })
}

/**
 * 搜索公开报价（按关键词）
 */
export function searchPublicQuotations(keyword: string, params?: Omit<IQuotationListRequest, 'keyword'>): Promise<IResData<IQuotationListResponse>> {
  return getPublicQuotationList({ ...params, keyword })
}

/**
 * 按商品名称获取公开报价
 */
export function getQuotationsByCommodity(commodityName: string, params?: Omit<IQuotationListRequest, 'commodityName'>): Promise<IResData<IQuotationListResponse>> {
  return getPublicQuotationList({ ...params, commodityName })
}

/**
 * 按价格类型获取公开报价
 */
export function getQuotationsByPriceType(priceType: string, params?: Omit<IQuotationListRequest, 'priceType'>): Promise<IResData<IQuotationListResponse>> {
  return getPublicQuotationList({ ...params, priceType })
}

// ============ 批量操作 ============

/**
 * 批量切换报价状态
 */
export function batchToggleQuotationStatus(quotationIds: number[]): Promise<IResData<any>> {
  const promises = quotationIds.map(id => toggleQuotationStatus(id))
  return Promise.all(promises).then(() => ({ code: 200, data: null, msg: '批量状态切换成功' }))
}

/**
 * 批量删除报价草稿
 */
export function batchDeleteQuotations(quotationIds: number[]): Promise<IResData<any>> {
  const promises = quotationIds.map(id => deleteQuotation(id))
  return Promise.all(promises).then(() => ({ code: 200, data: null, msg: '批量删除成功' }))
}
