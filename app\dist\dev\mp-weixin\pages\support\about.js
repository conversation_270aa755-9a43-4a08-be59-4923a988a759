"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_toast = require("../../utils/toast.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_icon2 + _easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "about",
  setup(__props) {
    const appVersion = common_vendor.ref("V2.1.0");
    const buildTime = common_vendor.ref("2024-01-15");
    const checking = common_vendor.ref(false);
    const handleLogoError = () => {
      console.log("Logo加载失败，使用默认图标");
    };
    const openWebsite = () => {
      common_vendor.index.showModal({
        title: "打开网站",
        content: "是否要在浏览器中打开官方网站？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "请在浏览器中访问 www.dianjia.com",
              icon: "none",
              duration: 3e3
            });
          }
        }
      });
    };
    const callService = () => {
      common_vendor.index.showModal({
        title: "拨打电话",
        content: "是否要拨打客服热线 ************？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.makePhoneCall({
              phoneNumber: "************",
              fail: (err) => {
                console.error("拨打电话失败:", err);
                utils_toast.toast.error("拨打电话失败");
              }
            });
          }
        }
      });
    };
    const checkUpdate = () => __async(this, null, function* () {
      checking.value = true;
      try {
        yield new Promise((resolve) => setTimeout(resolve, 2e3));
        const hasUpdate = Math.random() > 0.7;
        if (hasUpdate) {
          common_vendor.index.showModal({
            title: "发现新版本",
            content: "发现新版本 V2.2.0，是否立即更新？",
            confirmText: "立即更新",
            cancelText: "稍后更新",
            success: (res) => {
              if (res.confirm) {
                utils_toast.toast.success("正在跳转到更新页面...");
              }
            }
          });
        } else {
          utils_toast.toast.success("当前已是最新版本");
        }
      } catch (error) {
        console.error("检查更新失败:", error);
        utils_toast.toast.error("检查更新失败，请稍后重试");
      } finally {
        checking.value = false;
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$1,
        b: common_vendor.o(handleLogoError),
        c: common_vendor.t(appVersion.value),
        d: common_vendor.t(buildTime.value),
        e: common_vendor.p({
          name: "home",
          size: "32rpx",
          color: "#667eea"
        }),
        f: common_vendor.p({
          name: "link",
          size: "32rpx",
          color: "#667eea"
        }),
        g: common_vendor.o(openWebsite),
        h: common_vendor.p({
          name: "phone",
          size: "32rpx",
          color: "#667eea"
        }),
        i: common_vendor.o(callService),
        j: common_vendor.p({
          name: "mail",
          size: "32rpx",
          color: "#667eea"
        }),
        k: common_vendor.t(checking.value ? "检查中..." : "检查更新"),
        l: common_vendor.o(checkUpdate),
        m: common_vendor.p({
          type: "primary",
          size: "large",
          ["custom-class"]: "check-update-btn",
          loading: checking.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8a2c2bc0"]]);
wx.createPage(MiniProgramPage);
