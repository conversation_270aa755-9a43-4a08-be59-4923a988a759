"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_contract = require("../../api/contract.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_tab2 = common_vendor.resolveComponent("wd-tab");
  const _easycom_wd_tabs2 = common_vendor.resolveComponent("wd-tabs");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_button2 + _easycom_wd_tab2 + _easycom_wd_tabs2 + _easycom_wd_loading2 + _component_layout_default_uni)();
}
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_tab = () => "../../node-modules/wot-design-uni/components/wd-tab/wd-tab.js";
const _easycom_wd_tabs = () => "../../node-modules/wot-design-uni/components/wd-tabs/wd-tabs.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
if (!Math) {
  (_easycom_wd_button + ContractSummary + _easycom_wd_tab + _easycom_wd_tabs + ContractCard + _easycom_wd_loading + CancelContractDialog)();
}
const CancelContractDialog = () => "../../components/CancelContractDialog.js";
const ContractCard = () => "../../components/ContractCard.js";
const ContractSummary = () => "../../components/ContractSummary.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "setter-list",
  setup(__props) {
    const router = common_vendor.useRouter();
    const contractList = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const currentView = common_vendor.ref("detail");
    const summaryContractList = common_vendor.ref([]);
    const currentPage = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const total = common_vendor.ref(0);
    const hasMore = common_vendor.ref(true);
    let currentRequest = null;
    const activeTab = common_vendor.ref("all");
    const statusTabs = common_vendor.ref([
      { label: "全部", value: "all" },
      { label: "未执行", value: "Unexecuted" },
      { label: "执行中", value: "Executing" },
      { label: "已完成", value: "Completed" },
      { label: "已取消", value: "Cancelled" }
    ]);
    const showCancelDialog = common_vendor.ref(false);
    const currentContract = common_vendor.ref(null);
    function loadContractList(refresh = false) {
      return __async(this, null, function* () {
        if (refresh) {
          currentPage.value = 1;
          contractList.value = [];
          hasMore.value = true;
          isRefreshing.value = true;
          if (currentRequest) {
            currentRequest = null;
          }
        } else {
          if (isLoading.value || !hasMore.value)
            return;
          isLoading.value = true;
        }
        try {
          const params = {
            page: currentPage.value,
            pageSize: pageSize.value,
            status: activeTab.value === "all" ? void 0 : activeTab.value
          };
          const requestPromise = api_contract.getContractsAsSetter(params);
          currentRequest = requestPromise;
          const response = yield requestPromise;
          if (currentRequest !== requestPromise) {
            return;
          }
          if (response.code === 0) {
            let list;
            let totalCount;
            if (Array.isArray(response.data)) {
              list = response.data;
              totalCount = response.data.length;
              hasMore.value = false;
            } else {
              list = response.data.list || [];
              totalCount = response.data.total || 0;
              hasMore.value = contractList.value.length + list.length < totalCount;
            }
            if (refresh) {
              contractList.value = list;
            } else {
              contractList.value.push(...list);
            }
            total.value = totalCount;
          } else {
            common_vendor.index.showToast({
              title: response.msg || "获取合同列表失败",
              icon: "error"
            });
          }
        } catch (error) {
          console.error("获取合同列表失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        } finally {
          loading.value = false;
          isLoading.value = false;
          isRefreshing.value = false;
        }
      });
    }
    function loadMore() {
      return __async(this, null, function* () {
        if (!hasMore.value || isLoading.value)
          return;
        currentPage.value++;
        yield loadContractList();
      });
    }
    function onRefresh() {
      return __async(this, null, function* () {
        yield loadContractList(true);
      });
    }
    function handleTabChange() {
      return __async(this, null, function* () {
        contractList.value = [];
        hasMore.value = true;
        currentPage.value = 1;
        if (currentRequest) {
          currentRequest = null;
        }
        yield loadContractList(true);
      });
    }
    function goToCreate() {
      router.push("/pages/contract/form");
    }
    function goToDetail(contractId) {
      router.push(`/pages/contract/detail?id=${contractId}&role=setter`);
    }
    function editContract(contractId) {
      router.push(`/pages/contract/form?id=${contractId}`);
    }
    function activateContract(contractId) {
      return __async(this, null, function* () {
        try {
          const response = yield api_contract.activateContract(contractId);
          if (response.code === 0) {
            common_vendor.index.showToast({
              title: "激活成功",
              icon: "success"
            });
            loadContractList();
          } else {
            common_vendor.index.showToast({
              title: response.msg || "激活失败",
              icon: "error"
            });
          }
        } catch (error) {
          console.error("激活合同失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        }
      });
    }
    function deactivateContract(contractId) {
      return __async(this, null, function* () {
        try {
          const response = yield api_contract.deactivateContract(contractId);
          if (response.code === 0) {
            common_vendor.index.showToast({
              title: "挂起成功",
              icon: "success"
            });
            loadContractList();
          } else {
            common_vendor.index.showToast({
              title: response.msg || "挂起失败",
              icon: "error"
            });
          }
        } catch (error) {
          console.error("挂起合同失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        }
      });
    }
    function deleteContract(contractId) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "删除后无法恢复，确认删除？",
        success: (res) => __async(this, null, function* () {
          if (res.confirm) {
            try {
              const response = yield api_contract.deleteContract(contractId);
              if (response.code === 0) {
                common_vendor.index.showToast({
                  title: "删除成功",
                  icon: "success"
                });
                loadContractList();
              } else {
                common_vendor.index.showToast({
                  title: response.msg || "删除失败",
                  icon: "error"
                });
              }
            } catch (error) {
              console.error("删除合同失败:", error);
              common_vendor.index.showToast({
                title: "网络错误",
                icon: "error"
              });
            }
          }
        })
      });
    }
    function hasCancelRecords(contract) {
      return contract.status === "Cancelled" || contract.remainingQuantity < contract.totalQuantity;
    }
    function cancelContract(contract) {
      currentContract.value = contract;
      showCancelDialog.value = true;
    }
    function handleCancelConfirm(data) {
      return __async(this, null, function* () {
        if (!currentContract.value)
          return;
        try {
          const cancelData = {
            cancelQuantity: data.cancelQuantity,
            reason: data.reason
          };
          const response = yield api_contract.cancelContract(currentContract.value.ID, cancelData);
          if (response.code === 0) {
            common_vendor.index.showToast({
              title: "取消成功",
              icon: "success"
            });
            showCancelDialog.value = false;
            loadContractList();
          } else {
            common_vendor.index.showToast({
              title: response.msg || "取消失败",
              icon: "error"
            });
          }
        } catch (error) {
          console.error("取消合同失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        }
      });
    }
    function viewCancelRecords(contract) {
      router.push(`/pages/contract/cancel-records?contractId=${contract.ID}`);
    }
    function loadSummaryData() {
      return __async(this, null, function* () {
        try {
          const response = yield api_contract.getContractsAsSetter({
            status: "Executing",
            pageSize: 1e3
            // 获取足够多的数据进行汇总
          });
          if (response.code === 0) {
            summaryContractList.value = response.data.list || [];
          }
        } catch (error) {
          console.error("获取汇总数据失败:", error);
        }
      });
    }
    function switchView(view) {
      currentView.value = view;
      if (view === "detail") {
        loadContractList(true);
      } else if (view === "summary") {
        loadSummaryData();
      }
    }
    function handleSummaryInstrumentClick(data) {
      const { instrumentInfo, userInfo } = data;
      common_vendor.index.showToast({
        title: `查看${userInfo.userName}的${instrumentInfo.instrumentName}合同明细`,
        icon: "none",
        duration: 2e3
      });
      switchView("detail");
    }
    common_vendor.onMounted(() => {
      loading.value = true;
      loadContractList(true);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => switchView("summary")),
        b: common_vendor.p({
          type: currentView.value === "summary" ? "primary" : "info",
          size: "small"
        }),
        c: common_vendor.o(($event) => switchView("detail")),
        d: common_vendor.p({
          type: currentView.value === "detail" ? "primary" : "info",
          size: "small"
        }),
        e: common_vendor.o(goToCreate),
        f: common_vendor.p({
          type: "success",
          size: "small",
          round: true
        }),
        g: currentView.value === "summary"
      }, currentView.value === "summary" ? {
        h: common_vendor.o(handleSummaryInstrumentClick),
        i: common_vendor.p({
          contracts: summaryContractList.value,
          ["user-role"]: "setter",
          ["enable-click"]: true
        })
      } : {}, {
        j: currentView.value === "detail"
      }, currentView.value === "detail" ? {
        k: common_vendor.f(statusTabs.value, (tab, k0, i0) => {
          return {
            a: tab.value,
            b: "f7b919ed-6-" + i0 + ",f7b919ed-5",
            c: common_vendor.p({
              title: tab.label,
              name: tab.value
            })
          };
        }),
        l: common_vendor.o(handleTabChange),
        m: common_vendor.o(($event) => activeTab.value = $event),
        n: common_vendor.p({
          modelValue: activeTab.value
        })
      } : {}, {
        o: currentView.value === "detail"
      }, currentView.value === "detail" ? common_vendor.e({
        p: common_vendor.f(contractList.value, (contract, k0, i0) => {
          return {
            a: common_vendor.w(({
              contract: contract2
            }, s1, i1) => {
              return common_vendor.e({
                a: contract2.status === "Unexecuted"
              }, contract2.status === "Unexecuted" ? {
                b: common_vendor.o(($event) => activateContract(contract2.ID), contract2.ID),
                c: "f7b919ed-8-" + i0 + "-" + i1 + "," + ("f7b919ed-7-" + i0),
                d: common_vendor.p({
                  type: "success",
                  size: "small"
                })
              } : {}, {
                e: contract2.status === "Executing" && contract2.frozenQuantity === 0
              }, contract2.status === "Executing" && contract2.frozenQuantity === 0 ? {
                f: common_vendor.o(($event) => deactivateContract(contract2.ID), contract2.ID),
                g: "f7b919ed-9-" + i0 + "-" + i1 + "," + ("f7b919ed-7-" + i0),
                h: common_vendor.p({
                  type: "warning",
                  size: "small"
                })
              } : {}, {
                i: contract2.status === "Unexecuted"
              }, contract2.status === "Unexecuted" ? {
                j: common_vendor.o(($event) => cancelContract(contract2), contract2.ID),
                k: "f7b919ed-10-" + i0 + "-" + i1 + "," + ("f7b919ed-7-" + i0),
                l: common_vendor.p({
                  type: "error",
                  size: "small"
                })
              } : {}, {
                m: contract2.status === "Unexecuted"
              }, contract2.status === "Unexecuted" ? {
                n: common_vendor.o(($event) => editContract(contract2.ID), contract2.ID),
                o: "f7b919ed-11-" + i0 + "-" + i1 + "," + ("f7b919ed-7-" + i0),
                p: common_vendor.p({
                  type: "primary",
                  size: "small"
                })
              } : {}, {
                q: contract2.status === "Unexecuted"
              }, contract2.status === "Unexecuted" ? {
                r: common_vendor.o(($event) => deleteContract(contract2.ID), contract2.ID),
                s: "f7b919ed-12-" + i0 + "-" + i1 + "," + ("f7b919ed-7-" + i0),
                t: common_vendor.p({
                  type: "error",
                  size: "small"
                })
              } : {}, {
                v: hasCancelRecords(contract2)
              }, hasCancelRecords(contract2) ? {
                w: common_vendor.o(($event) => viewCancelRecords(contract2), contract2.ID),
                x: "f7b919ed-13-" + i0 + "-" + i1 + "," + ("f7b919ed-7-" + i0),
                y: common_vendor.p({
                  type: "info",
                  size: "small"
                })
              } : {}, {
                z: common_vendor.o(() => {
                }, contract2.ID),
                A: i1,
                B: s1
              });
            }, {
              name: "actions",
              path: "p[" + i0 + "].a",
              vueId: "f7b919ed-7-" + i0 + ",f7b919ed-0"
            }),
            b: contract.ID,
            c: common_vendor.o(($event) => goToDetail(contract.ID), contract.ID),
            d: "f7b919ed-7-" + i0 + ",f7b919ed-0",
            e: common_vendor.p({
              contract,
              ["user-role"]: "setter"
            })
          };
        }),
        q: !contractList.value.length && !loading.value
      }, !contractList.value.length && !loading.value ? {
        r: common_vendor.o(goToCreate),
        s: common_vendor.p({
          type: "primary"
        })
      } : {}, {
        t: isLoading.value && contractList.value.length > 0
      }, isLoading.value && contractList.value.length > 0 ? {
        v: common_vendor.p({
          size: "small"
        })
      } : {}, {
        w: !hasMore.value && contractList.value.length > 0
      }, !hasMore.value && contractList.value.length > 0 ? {} : {}, {
        x: isRefreshing.value,
        y: common_vendor.o(onRefresh),
        z: common_vendor.o(loadMore)
      }) : {}, {
        A: loading.value && contractList.value.length === 0
      }, loading.value && contractList.value.length === 0 ? {} : {}, {
        B: common_vendor.o(handleCancelConfirm),
        C: common_vendor.o(($event) => showCancelDialog.value = $event),
        D: common_vendor.p({
          ["contract-data"]: currentContract.value,
          modelValue: showCancelDialog.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f7b919ed"]]);
wx.createPage(MiniProgramPage);
