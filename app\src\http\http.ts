import type { CustomRequestOptions } from '@/http/interceptor'
import { useUserStore } from '@/store'

// 错误处理类型定义
interface HttpError {
  statusCode: number
  data?: any
  errMsg: string
  code?: number
}

// 防止重复弹出401对话框
let is401DialogShowing = false

// 统一错误处理函数
function handleHttpError(error: HttpError, hideErrorToast = false) {
  const { statusCode, data, errMsg, code } = error

  // 根据不同错误类型显示不同的提示
  let title = '请求失败'

  // 优先处理401状态码（无论业务code是什么）
  if (statusCode === 401) {
    title = '登录已过期，请重新登录'

    // 防止重复弹出对话框
    if (is401DialogShowing) {
      console.log('401对话框已经在显示中，跳过重复弹窗')
      return
    }

    // 使用uni.showModal弹出对话框（更可靠）
    try {
      is401DialogShowing = true
      uni.showModal({
        title: '登录提示',
        content: '您当前没有登录，请登录后查看',
        showCancel: true,
        cancelText: '取消',
        confirmText: '去登录',
        success: (res) => {
          is401DialogShowing = false
          if (res.confirm) {
            // 用户点击了"去登录"，跳转到登录页
            uni.reLaunch({
              url: '/pages/login/index'
            })
          }
          // 用户点击取消或确定都清理用户信息
          const userStore = useUserStore()
          userStore.clearUserInfo()
        },
        fail: () => {
          is401DialogShowing = false
          // 弹窗失败时也要清理用户信息
          const userStore = useUserStore()
          userStore.clearUserInfo()
        }
      })
    } catch (e) {
      is401DialogShowing = false
      console.warn('处理401错误失败:', e)
      // 即使弹窗失败，也要清理用户信息
      try {
        const userStore = useUserStore()
        userStore.clearUserInfo()
      } catch (storeError) {
        console.warn('清理用户信息失败:', storeError)
      }
    }
    return // 401处理完毕，直接返回，不显示toast
  }

  // 处理其他业务错误（code !== 0）
  if (code && code !== 0) {
    // 业务错误：优先使用后端返回的具体错误信息
    title = (data?.msg || errMsg || '操作失败')

    // 开发环境下打印业务错误详情
    // #ifdef H5
    if (import.meta.env.DEV) {
      console.warn('业务错误:', { code, msg: title, data, statusCode })
    }
    // #endif
  } else {
    // 其他HTTP状态错误处理
    switch (statusCode) {
      case 403:
        title = '没有权限访问该资源'
        break
      case 404:
        title = '请求的资源不存在'
        break
      case 500:
        title = '服务器内部错误，请稍后重试'
        break
      case 502:
        title = '网关错误，请稍后重试'
        break
      case 503:
        title = '服务暂不可用，请稍后重试'
        break
      case 0:
        title = '网络连接失败，请检查网络设置'
        break
      default:
        if (statusCode >= 400) {
          // 尝试从响应数据中获取错误信息
          title = (data?.msg || errMsg || '请求错误，请检查网络连接')
        } else {
          title = errMsg || '未知错误'
        }
    }
    
    // 开发环境下打印HTTP错误详情
    // #ifdef H5
    if (import.meta.env.DEV) {
      console.error('HTTP错误:', { statusCode, msg: title, data, errMsg })
    }
    // #endif
  }
  
  // 显示错误提示
  if (!hideErrorToast) {
    uni.showToast({
      icon: 'none',
      title,
      duration: 3000  // 延长显示时间让用户能看清错误信息
    })
  }
}

export function http<T>(options: CustomRequestOptions) {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        const responseData = res.data as IResData<T>

        // 1. 优先处理HTTP状态码错误（包括401）
        if (res.statusCode < 200 || res.statusCode >= 300) {
          const error: HttpError = {
            statusCode: res.statusCode,
            data: responseData,
            errMsg: responseData?.msg || `HTTP ${res.statusCode} 错误`,
            code: responseData?.code
          }
          handleHttpError(error, options.hideErrorToast)
          reject(error)
          return
        }

        // 2. 处理业务逻辑错误（HTTP状态码正常，但业务code !== 0）
        if (responseData.code && responseData.code !== 0) {
          const error: HttpError = {
            statusCode: res.statusCode,
            data: responseData,
            errMsg: responseData.msg || '操作失败',
            code: responseData.code
          }
          handleHttpError(error, options.hideErrorToast)
          reject(error)
          return
        }

        // 3. 成功响应：返回业务数据
        resolve(responseData)
      },
      // 响应失败
      fail() {
        const error: HttpError = {
          statusCode: 0,
          errMsg: '网络连接失败，请检查网络设置'
        }
        handleHttpError(error, options.hideErrorToast)
        reject(error)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @param header 请求头，默认为json格式
 * @returns
 */
export function httpGet<T>(url: string, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    query,
    method: 'GET',
    header,
    ...options,
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @param header 请求头，默认为json格式
 * @returns
 */
export function httpPost<T>(url: string, data?: Record<string, any>, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
    header,
    ...options,
  })
}
/**
 * PUT 请求
 */
export function httpPut<T>(url: string, data?: Record<string, any>, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    data,
    query,
    method: 'PUT',
    header,
    ...options,
  })
}

/**
 * DELETE 请求（无请求体，仅 query）
 */
export function httpDelete<T>(url: string, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    query,
    method: 'DELETE',
    header,
    ...options,
  })
}

http.get = httpGet
http.post = httpPost
http.put = httpPut
http.delete = httpDelete

// 支持与 alovaJS 类似的API调用
http.Get = httpGet
http.Post = httpPost
http.Put = httpPut
http.Delete = httpDelete
