"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../common/vendor.js");
const store_socket = require("./socket.js");
const utils_toast = require("../utils/toast.js");
const useMarketStore = common_vendor.defineStore("market", () => {
  const marketData = common_vendor.ref({});
  const subscribedSymbols = common_vendor.ref([]);
  const socketStore = store_socket.useSocketStore();
  function subscribe(symbol, exchange) {
    if (subscribedSymbols.value.includes(symbol)) {
      console.log(`[MarketStore] 合约 ${symbol} 已订阅，跳过重复订阅`);
      return;
    }
    if (!socketStore.isConnected) {
      utils_toast.toast.error("WebSocket未连接，无法订阅行情");
      return;
    }
    try {
      socketStore.sendMessage("subscribe_market", {
        symbol,
        exchange
      });
      subscribedSymbols.value.push(symbol);
      console.log(`[MarketStore] 已发送订阅请求: ${symbol} (${exchange})`);
      utils_toast.toast.success(`已订阅 ${symbol} 行情`);
    } catch (error) {
      console.error(`[MarketStore] 订阅失败:`, error);
      utils_toast.toast.error(`订阅 ${symbol} 行情失败`);
    }
  }
  function unsubscribe(symbol) {
    if (!subscribedSymbols.value.includes(symbol)) {
      console.log(`[MarketStore] 合约 ${symbol} 未订阅，跳过取消订阅`);
      return;
    }
    if (!socketStore.isConnected) {
      utils_toast.toast.error("WebSocket未连接，无法取消订阅");
      return;
    }
    try {
      socketStore.sendMessage("unsubscribe_market", {
        symbol
      });
      const index = subscribedSymbols.value.indexOf(symbol);
      if (index > -1) {
        subscribedSymbols.value.splice(index, 1);
      }
      delete marketData.value[symbol];
      console.log(`[MarketStore] 已发送取消订阅请求: ${symbol}`);
      utils_toast.toast.success(`已取消订阅 ${symbol} 行情`);
    } catch (error) {
      console.error(`[MarketStore] 取消订阅失败:`, error);
      utils_toast.toast.error(`取消订阅 ${symbol} 行情失败`);
    }
  }
  function handleMarketUpdate(data) {
    try {
      if (!data || typeof data !== "object") {
        console.error("[MarketStore] 无效的行情数据格式:", data);
        return;
      }
      const { symbol } = data;
      if (!symbol) {
        console.error("[MarketStore] 行情数据缺少symbol字段:", data);
        return;
      }
      if (!subscribedSymbols.value.includes(symbol)) {
        console.warn(`[MarketStore] 收到未订阅合约 ${symbol} 的行情数据`);
        return;
      }
      const marketInfo = __spreadValues({
        gateway_name: data.gateway_name || "",
        symbol: data.symbol || "",
        exchange: data.exchange || "",
        datetime: data.datetime || "",
        name: data.name || "",
        volume: data.volume || "0",
        turnover: data.turnover || "0",
        open_interest: data.open_interest || "0",
        last_price: data.last_price || "0",
        last_volume: data.last_volume || "0",
        limit_up: data.limit_up || "0",
        limit_down: data.limit_down || "0",
        open_price: data.open_price || "0",
        high_price: data.high_price || "0",
        low_price: data.low_price || "0",
        pre_close: data.pre_close || "0",
        bid_price_1: data.bid_price_1 || "0",
        bid_price_2: data.bid_price_2 || "0",
        bid_price_3: data.bid_price_3 || "0",
        bid_price_4: data.bid_price_4 || "0",
        bid_price_5: data.bid_price_5 || "0",
        ask_price_1: data.ask_price_1 || "0",
        ask_price_2: data.ask_price_2 || "0",
        ask_price_3: data.ask_price_3 || "0",
        ask_price_4: data.ask_price_4 || "0",
        ask_price_5: data.ask_price_5 || "0",
        bid_volume_1: data.bid_volume_1 || "0",
        bid_volume_2: data.bid_volume_2 || "0",
        bid_volume_3: data.bid_volume_3 || "0",
        bid_volume_4: data.bid_volume_4 || "0",
        bid_volume_5: data.bid_volume_5 || "0",
        ask_volume_1: data.ask_volume_1 || "0",
        ask_volume_2: data.ask_volume_2 || "0",
        ask_volume_3: data.ask_volume_3 || "0",
        ask_volume_4: data.ask_volume_4 || "0",
        ask_volume_5: data.ask_volume_5 || "0",
        localtime: data.localtime || "",
        vt_symbol: data.vt_symbol || ""
      }, data);
      marketData.value[symbol] = marketInfo;
      console.log(`[MarketStore] 更新行情数据: ${symbol}`, marketInfo);
    } catch (error) {
      console.error("[MarketStore] 处理行情更新失败:", error);
    }
  }
  function getMarketData(symbol) {
    return marketData.value[symbol];
  }
  function getAllMarketData() {
    return marketData.value;
  }
  function isSubscribed(symbol) {
    return subscribedSymbols.value.includes(symbol);
  }
  function getSubscribedSymbols() {
    return [...subscribedSymbols.value];
  }
  function clearAll() {
    marketData.value = {};
    subscribedSymbols.value = [];
    console.log("[MarketStore] 已清空所有行情数据和订阅关系");
  }
  function initializeEventHandlers() {
    socketStore.registerHandler("market_update", handleMarketUpdate);
    console.log("[MarketStore] 已注册market_update事件处理器");
  }
  function cleanupEventHandlers() {
    socketStore.unregisterHandler("market_update", handleMarketUpdate);
    console.log("[MarketStore] 已取消注册market_update事件处理器");
  }
  initializeEventHandlers();
  return {
    // 只读状态
    marketData: common_vendor.readonly(marketData),
    subscribedSymbols: common_vendor.readonly(subscribedSymbols),
    // 公开方法
    subscribe,
    unsubscribe,
    getMarketData,
    getAllMarketData,
    isSubscribed,
    getSubscribedSymbols,
    clearAll,
    cleanupEventHandlers,
    // 内部方法，作为事件处理器
    handleMarketUpdate
  };
});
exports.useMarketStore = useMarketStore;
