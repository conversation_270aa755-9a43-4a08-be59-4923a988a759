/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-card.data-v-f97f9319 {
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
}
.avatar-section.data-v-f97f9319 {
  position: relative;
  margin-right: 24rpx;
}
.avatar-section .avatar.data-v-f97f9319 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  border: 3rpx solid #fff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}
.avatar-section .avatar-edit-icon.data-v-f97f9319 {
  position: absolute;
  bottom: -2rpx;
  right: -2rpx;
  width: 32rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #fff;
}
.user-info.data-v-f97f9319 {
  flex: 1;
}
.user-info .username.data-v-f97f9319 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}
.user-info .user-id.data-v-f97f9319 {
  font-size: 24rpx;
  color: #909399;
}
.profile-list.data-v-f97f9319 {
  overflow: hidden;
}
.section-title.data-v-f97f9319 {
  padding: 20rpx 24rpx 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}
.list-item.data-v-f97f9319 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.3s ease;
}
.list-item.data-v-f97f9319:last-child {
  border-bottom: none;
}
.list-item.data-v-f97f9319:active {
  background-color: #f8f9fa;
}
.item-left.data-v-f97f9319 {
  display: flex;
  align-items: center;
}
.item-left .item-label.data-v-f97f9319 {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
}
.item-right.data-v-f97f9319 {
  display: flex;
  align-items: center;
}
.item-right .item-value.data-v-f97f9319 {
  font-size: 26rpx;
  color: #606266;
  margin-right: 12rpx;
}
.logout-text.data-v-f97f9319 {
  color: #f56c6c !important;
}
.edit-popup.data-v-f97f9319 {
  padding: 32rpx;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
}
.popup-header.data-v-f97f9319 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}
.popup-header .popup-title.data-v-f97f9319 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.popup-content.data-v-f97f9319 {
  margin-bottom: 48rpx;
}
.popup-actions.data-v-f97f9319 {
  display: flex;
  gap: 20rpx;
}
.popup-actions .cancel-btn.data-v-f97f9319,
.popup-actions .save-btn.data-v-f97f9319 {
  flex: 1;
  height: 80rpx !important;
  border-radius: 40rpx !important;
  font-size: 28rpx !important;
}
.data-v-f97f9319 .save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}
.avatar-cropper-popup.data-v-f97f9319 {
  padding: 32rpx;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 75vh;
}
.image-preview.data-v-f97f9319 {
  margin: 32rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 320rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}
.image-preview .preview-image.data-v-f97f9319 {
  max-width: 100%;
  max-height: 320rpx;
  border-radius: 12rpx;
}
.crop-tips.data-v-f97f9319 {
  text-align: center;
  margin-bottom: 32rpx;
}
.crop-tips text.data-v-f97f9319 {
  font-size: 22rpx;
  color: #909399;
  line-height: 1.4;
}
.data-v-f97f9319 .upload-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}