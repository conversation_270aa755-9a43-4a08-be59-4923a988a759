/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-b051b919 {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  box-sizing: border-box;
}
.search-section.data-v-b051b919 {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  padding: 20rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  width: calc(100% - 40rpx);
  box-sizing: border-box;
  animation: slideInDown-b051b919 0.3s ease-out;
}
.search-bar.data-v-b051b919 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.search-bar.data-v-b051b919 .search-input {
  flex: 1;
  border-radius: 12rpx !important;
  border: 2rpx solid #e4e7ed !important;
  transition: all 0.3s ease !important;
}
.search-bar.data-v-b051b919 .search-input:focus-within {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1) !important;
}
.search-bar.data-v-b051b919 .search-icon {
  color: #667eea !important;
}
.filter-button.data-v-b051b919 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  position: relative;
  background: rgba(102, 126, 234, 0.05);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}
.filter-button.data-v-b051b919:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  transform: translateY(-1rpx);
}
.filter-button.data-v-b051b919:active {
  transform: translateY(0);
}
.filter-button .filter-text.data-v-b051b919 {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
}
.filter-button.data-v-b051b919 .filter-icon {
  color: #667eea !important;
}
.filter-button .filter-dot.data-v-b051b919 {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 12rpx;
  height: 12rpx;
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(255, 71, 87, 0.3);
  animation: pulse-b051b919 2s infinite;
}
.active-filters.data-v-b051b919 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 16rpx;
  animation: fadeIn-b051b919 0.3s ease-out;
}
.filter-tag.data-v-b051b919 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}
.filter-tag.data-v-b051b919:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.filter-tag.data-v-b051b919 .tag-close-icon {
  color: white !important;
  transition: transform 0.2s ease;
}
.filter-tag.data-v-b051b919 .tag-close-icon:hover {
  transform: scale(1.2);
}
.scroll-container.data-v-b051b919 {
  flex: 1;
  padding: 20rpx;
  box-sizing: border-box;
}
.quotation-list.data-v-b051b919 {
  width: 100%;
  box-sizing: border-box;
}
.quotation-list.data-v-b051b919 .quotation-card {
  background: rgba(255, 255, 255, 0.95) !important;
  -webkit-backdrop-filter: blur(10rpx) !important;
          backdrop-filter: blur(10rpx) !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12) !important;
  transition: all 0.3s ease !important;
}
.quotation-list.data-v-b051b919 .quotation-card:hover {
  transform: translateY(-4rpx) !important;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15) !important;
}
.empty-state.data-v-b051b919 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  margin: 36rpx;
  animation: fadeIn-b051b919 0.5s ease-out;
}
.empty-state.data-v-b051b919 .empty-image {
  opacity: 0.6;
  filter: grayscale(20%);
}
.empty-state .empty-text.data-v-b051b919 {
  font-size: 28rpx;
  color: #606266;
  margin: 24rpx 0 40rpx;
  text-align: center;
  font-weight: 500;
}
.empty-state.data-v-b051b919 .empty-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 44rpx !important;
  font-weight: 600 !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}
.empty-state.data-v-b051b919 .empty-button:hover {
  transform: translateY(-2rpx) !important;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3) !important;
}
.loading-more.data-v-b051b919 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.loading-more.data-v-b051b919 .loading-spinner {
  color: #667eea !important;
}
.loading-more .loading-text.data-v-b051b919 {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
}
.no-more.data-v-b051b919 {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #909399;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.filter-panel.data-v-b051b919 {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.filter-header.data-v-b051b919 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 2rpx solid rgba(228, 231, 237, 0.5);
  background: rgba(102, 126, 234, 0.05);
}
.filter-header .filter-title.data-v-b051b919 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.filter-header.data-v-b051b919 .reset-button {
  color: #667eea !important;
  font-weight: 500 !important;
}
.filter-section.data-v-b051b919 {
  padding: 32rpx 24rpx;
}
.filter-section .section-title.data-v-b051b919 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}
.tag-list.data-v-b051b919 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.tag-list.data-v-b051b919 .filter-tag-item {
  margin: 0 !important;
  font-size: 26rpx !important;
  padding: 12rpx 20rpx !important;
  border-radius: 20rpx !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  border: 2rpx solid transparent !important;
}
.tag-list.data-v-b051b919 .filter-tag-item:hover {
  transform: translateY(-1rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
}
.tag-list.data-v-b051b919 .filter-tag-item[data-type=primary] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
}
.tag-list.data-v-b051b919 .filter-tag-item[data-type=default] {
  background: rgba(102, 126, 234, 0.05) !important;
  color: #667eea !important;
  border-color: rgba(102, 126, 234, 0.2) !important;
}
.tag-list.data-v-b051b919 .filter-tag-item[data-type=default]:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  border-color: #667eea !important;
}
@keyframes slideInDown-b051b919 {
from {
    opacity: 0;
    transform: translateY(-20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes fadeIn-b051b919 {
from {
    opacity: 0;
    transform: translateY(10rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes pulse-b051b919 {
0%, 100% {
    transform: scale(1);
    opacity: 1;
}
50% {
    transform: scale(1.1);
    opacity: 0.8;
}
}
@media (max-width: 750rpx) {
.search-section.data-v-b051b919 {
    margin: 10rpx;
    padding: 16rpx;
    width: calc(100% - 20rpx);
}
.scroll-container.data-v-b051b919 {
    padding: 10rpx;
}
.empty-state.data-v-b051b919 {
    margin: 16rpx;
    padding: 80rpx 20rpx;
}
}