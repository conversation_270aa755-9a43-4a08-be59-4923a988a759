/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-header.data-v-f7b919ed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.page-header .page-title.data-v-f7b919ed {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.page-header .header-actions.data-v-f7b919ed {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.page-header .header-actions .view-switch.data-v-f7b919ed {
  display: flex;
  gap: 10rpx;
}
.page-header .header-actions.data-v-f7b919ed .wd-button[type=success] {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(103, 194, 58, 0.3);
  transition: all 0.3s ease;
}
.page-header .header-actions.data-v-f7b919ed .wd-button[type=success]:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(103, 194, 58, 0.4);
}
.page-header .header-actions.data-v-f7b919ed .wd-button[type=success]:active {
  transform: translateY(0);
  box-shadow: 0 2rpx 8rpx rgba(103, 194, 58, 0.3);
}
.page-header .header-actions.data-v-f7b919ed .wd-button[type=success] .create-icon {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 8rpx;
  display: inline-block;
  line-height: 1;
}
.filter-tabs.data-v-f7b919ed {
  margin-bottom: 30rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}
.scroll-container.data-v-f7b919ed {
  height: calc(100vh - 200rpx);
}
.scroll-container .loading-more.data-v-f7b919ed {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  gap: 16rpx;
  color: #999;
  font-size: 28rpx;
}
.scroll-container .no-more.data-v-f7b919ed {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  color: #999;
  font-size: 28rpx;
}
.empty-state.data-v-f7b919ed, .loading-state.data-v-f7b919ed {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
  gap: 30rpx;
}