"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_index = require("../../utils/index.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _component_layout_tabbar_uni = common_vendor.resolveComponent("layout-tabbar-uni");
  (_easycom_wd_icon2 + _component_layout_tabbar_uni)();
}
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
if (!Math) {
  _easycom_wd_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "WorkspaceIndex"
}), {
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const sellerMenu = common_vendor.ref([
      {
        title: "合同管理",
        icon: "file-word",
        path: "/pages/contract/setter-list",
        color: "text-blue-500"
      },
      {
        title: "交易审核",
        icon: "check-circle",
        path: "/pages/trade/setter-management",
        color: "text-green-500"
      },
      {
        title: "我的报价",
        icon: "read",
        path: "/pages/quotes/my-list",
        color: "text-purple-500"
      },
      {
        title: "报表",
        // Changed
        icon: "chart-pie",
        path: "/pages/reports/setter-view",
        color: "text-orange-500"
      }
    ]);
    const pricerMenu = common_vendor.ref([
      {
        title: "合同中心",
        icon: "cart",
        path: "/pages/contract/pricer-list",
        color: "text-purple-500"
      },
      {
        title: "发起点价",
        icon: "add-circle",
        path: "/pages/trade/execute",
        color: "text-red-500"
      },
      {
        title: "我的交易",
        icon: "list",
        path: "/pages/trade/pricer-management",
        color: "text-indigo-500"
      },
      {
        title: "报表",
        // Changed
        icon: "chart-pie",
        path: "/pages/reports/pricer-view",
        color: "text-amber-500"
      }
    ]);
    const commonMenu = common_vendor.ref([
      {
        title: "行情看板",
        icon: "chart-bar",
        path: "/pages/dashboard/index",
        color: "text-cyan-500"
      },
      {
        title: "报价市场",
        icon: "shop",
        path: "/pages/quotes/marketplace",
        color: "text-green-500"
      },
      {
        title: "消息通知",
        icon: "notification",
        path: "/pages/notifications/index",
        color: "text-amber-500"
      },
      {
        title: "账户中心",
        icon: "setting",
        path: "/pages/profile/index",
        color: "text-gray-500"
      }
    ]);
    function handleNavigate(path) {
      if (!path)
        return;
      utils_index.navigateToPage({ url: path });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(common_vendor.unref(userStore).userInfo.nickName),
        b: common_vendor.f(sellerMenu.value, (item, k0, i0) => {
          return {
            a: "c2bb628e-1-" + i0 + ",c2bb628e-0",
            b: common_vendor.p({
              name: item.icon,
              ["custom-class"]: item.color,
              size: "30px"
            }),
            c: common_vendor.t(item.title),
            d: item.title,
            e: common_vendor.o(($event) => handleNavigate(item.path), item.title)
          };
        }),
        c: common_vendor.f(pricerMenu.value, (item, k0, i0) => {
          return {
            a: "c2bb628e-2-" + i0 + ",c2bb628e-0",
            b: common_vendor.p({
              name: item.icon,
              ["custom-class"]: item.color,
              size: "30px"
            }),
            c: common_vendor.t(item.title),
            d: item.title,
            e: common_vendor.o(($event) => handleNavigate(item.path), item.title)
          };
        }),
        d: common_vendor.f(commonMenu.value, (item, k0, i0) => {
          return {
            a: "c2bb628e-3-" + i0 + ",c2bb628e-0",
            b: common_vendor.p({
              name: item.icon,
              ["custom-class"]: item.color,
              size: "30px"
            }),
            c: common_vendor.t(item.title),
            d: item.title,
            e: common_vendor.o(($event) => handleNavigate(item.path), item.title)
          };
        })
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c2bb628e"]]);
wx.createPage(MiniProgramPage);
