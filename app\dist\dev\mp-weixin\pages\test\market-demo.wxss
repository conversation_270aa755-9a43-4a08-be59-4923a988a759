
.market-demo.data-v-af17903f {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-af17903f {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: white;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.title.data-v-af17903f {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.status.data-v-af17903f {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background: #ff4757;
  color: white;
}
.status.connected.data-v-af17903f {
  background: #2ed573;
}
.controls.data-v-af17903f {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.input-group.data-v-af17903f {
  margin-bottom: 20rpx;
}
.input.data-v-af17903f {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}
.button-group.data-v-af17903f {
  display: flex;
  gap: 20rpx;
}
.btn.data-v-af17903f {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}
.btn-primary.data-v-af17903f {
  background: #3742fa;
  color: white;
}
.btn-secondary.data-v-af17903f {
  background: #747d8c;
  color: white;
}
.btn-danger.data-v-af17903f {
  background: #ff4757;
  color: white;
}
.btn-small.data-v-af17903f {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}
.btn.data-v-af17903f:disabled {
  background: #ddd;
  color: #999;
}
.subscriptions.data-v-af17903f, .market-data.data-v-af17903f {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.section-title.data-v-af17903f {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.empty.data-v-af17903f {
  text-align: center;
  color: #999;
  padding: 40rpx;
}
.subscription-list.data-v-af17903f {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}
.subscription-item.data-v-af17903f {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}
.data-list.data-v-af17903f {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.data-item.data-v-af17903f {
  border: 2rpx solid #eee;
  border-radius: 10rpx;
  padding: 20rpx;
}
.data-header.data-v-af17903f {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #eee;
}
.symbol.data-v-af17903f {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.exchange.data-v-af17903f {
  font-size: 24rpx;
  color: #666;
  background: #e9ecef;
  padding: 5rpx 15rpx;
  border-radius: 15rpx;
}
.timestamp.data-v-af17903f {
  font-size: 24rpx;
  color: #999;
}
.data-content.data-v-af17903f {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.price-info.data-v-af17903f, .quote-info.data-v-af17903f {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.price-item.data-v-af17903f, .quote-item.data-v-af17903f {
  flex: 1;
  min-width: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}
.label.data-v-af17903f {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.value.data-v-af17903f {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.value.price.data-v-af17903f {
  color: #ff4757;
}
.volume.data-v-af17903f {
  font-size: 22rpx;
  color: #999;
  margin-top: 5rpx;
}
