<layout-tabbar-uni class="data-v-456eb489" u-s="{{['d']}}" u-i="456eb489-0" bind:__l="__l"><view class="page-container gradient-bg-primary data-v-456eb489"><view class="tab-container data-v-456eb489"><view class="{{['tab-item', 'data-v-456eb489', a && 'active']}}" bindtap="{{b}}"><text class="tab-text data-v-456eb489">点价</text></view><view class="{{['tab-item', 'data-v-456eb489', c && 'active']}}" bindtap="{{d}}"><text class="tab-text data-v-456eb489">洗基差</text></view></view><view class="selector-section data-v-456eb489"><combination-selector wx:if="{{g}}" class="data-v-456eb489" bindchange="{{e}}" u-i="456eb489-1,456eb489-0" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"/></view><view class="info-card common-card data-v-456eb489"><view class="stats-grid data-v-456eb489"><view class="stat-item stat-blue data-v-456eb489"><text class="stat-number data-v-456eb489">{{h}}</text><text class="stat-label data-v-456eb489">{{i}}剩余</text></view><view class="stat-item stat-orange data-v-456eb489"><text class="stat-number data-v-456eb489">{{j}}</text><text class="stat-label data-v-456eb489">{{k}}冻结</text></view><view class="stat-item stat-green data-v-456eb489"><text class="stat-number data-v-456eb489">{{l}}</text><text class="stat-label data-v-456eb489">{{m}}可用</text></view><view class="stat-item stat-purple data-v-456eb489"><text class="stat-number data-v-456eb489">{{n}}</text><text class="stat-label data-v-456eb489">{{o}}均价</text></view></view></view><view class="quote-card common-card data-v-456eb489"><quote-card wx:if="{{q}}" class="data-v-456eb489" bindpriceClick="{{p}}" u-i="456eb489-2,456eb489-0" bind:__l="__l" u-p="{{q}}"/></view><view class="operation-card common-card data-v-456eb489"><trade-operation-form wx:if="{{t}}" class="r data-v-456eb489" u-r="tradeFormRef" bindsubmit="{{s}}" u-i="456eb489-3,456eb489-0" bind:__l="__l" u-p="{{t}}"/></view><trade-request-list wx:if="{{v}}" class="data-v-456eb489" u-i="456eb489-4,456eb489-0" bind:__l="__l" u-p="{{v}}"/></view></layout-tabbar-uni>