"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_instrument = require("../api/instrument.js");
const types_instrument = require("../types/instrument.js");
const useInstrumentStore = common_vendor.defineStore("instrument", () => {
  const instruments = common_vendor.ref([]);
  const isLoading = common_vendor.ref(false);
  const isLoaded = common_vendor.ref(false);
  const lastLoadTime = common_vendor.ref(0);
  const error = common_vendor.ref(null);
  const CACHE_DURATION = 5 * 60 * 1e3;
  const groupedData = common_vendor.ref({
    exchanges: [],
    productsByExchange: {},
    contractsByProduct: {},
    instrumentsById: {},
    instrumentsByInstrumentId: {}
  });
  const isDataFresh = common_vendor.computed(() => {
    return isLoaded.value && Date.now() - lastLoadTime.value < CACHE_DURATION;
  });
  function loadInstruments(forceRefresh = false) {
    return __async(this, null, function* () {
      if (isDataFresh.value && !forceRefresh) {
        return { success: true, data: instruments.value };
      }
      if (isLoading.value) {
        return new Promise((resolve) => {
          const checkLoading = () => {
            if (!isLoading.value) {
              resolve({ success: isLoaded.value, data: instruments.value });
            } else {
              setTimeout(checkLoading, 100);
            }
          };
          checkLoading();
        });
      }
      try {
        isLoading.value = true;
        error.value = null;
        console.log("开始加载合约数据...");
        const response = yield api_instrument.getInstrumentSelectList();
        if (response.code === 0 && response.data) {
          instruments.value = response.data;
          lastLoadTime.value = Date.now();
          isLoaded.value = true;
          buildGroupedData();
          console.log(`合约数据加载成功，共 ${instruments.value.length} 个合约`);
          return { success: true, data: instruments.value };
        } else {
          throw new Error(response.msg || "获取合约数据失败");
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : "加载合约数据失败";
        error.value = errorMsg;
        console.error("加载合约数据失败:", err);
        return { success: false, error: errorMsg };
      } finally {
        isLoading.value = false;
      }
    });
  }
  function buildGroupedData() {
    const data = {
      exchanges: [],
      productsByExchange: {},
      contractsByProduct: {},
      instrumentsById: {},
      instrumentsByInstrumentId: {}
    };
    data.exchanges = Object.entries(types_instrument.ExchangeMap).map(([value, label]) => ({
      value,
      label
    }));
    const productSet = /* @__PURE__ */ new Map();
    for (const instrument of instruments.value) {
      data.instrumentsById[instrument.id] = instrument;
      data.instrumentsByInstrumentId[instrument.instrument_id] = instrument;
      const productKey = `${instrument.exchange_id}_${instrument.product_name}`;
      if (!productSet.has(productKey)) {
        productSet.set(productKey, instrument.product_name);
        if (!data.productsByExchange[instrument.exchange_id]) {
          data.productsByExchange[instrument.exchange_id] = [];
        }
        data.productsByExchange[instrument.exchange_id].push({
          value: instrument.product_name,
          label: instrument.product_name
        });
      }
    }
    for (const instrument of instruments.value) {
      const key = `${instrument.exchange_id}_${instrument.product_name}`;
      if (!data.contractsByProduct[key]) {
        data.contractsByProduct[key] = [];
      }
      data.contractsByProduct[key].push({
        value: instrument.instrument_id,
        label: instrument.instrument_id,
        instrumentData: instrument
      });
    }
    groupedData.value = data;
  }
  function getInstrumentById(id) {
    return groupedData.value.instrumentsById[id] || null;
  }
  function getInstrumentByInstrumentId(instrumentId) {
    return groupedData.value.instrumentsByInstrumentId[instrumentId] || null;
  }
  function getExchanges() {
    return groupedData.value.exchanges;
  }
  function getProductsByExchange(exchangeId) {
    return groupedData.value.productsByExchange[exchangeId] || [];
  }
  function getContractsByProduct(exchangeId, productName) {
    const key = `${exchangeId}_${productName}`;
    return groupedData.value.contractsByProduct[key] || [];
  }
  function searchInstruments(keyword) {
    if (!keyword.trim())
      return instruments.value;
    const lowerKeyword = keyword.toLowerCase();
    return instruments.value.filter(
      (instrument) => instrument.instrument_id.toLowerCase().includes(lowerKeyword) || instrument.instrument_name.toLowerCase().includes(lowerKeyword) || instrument.product_name.toLowerCase().includes(lowerKeyword)
    );
  }
  function clearCache() {
    instruments.value = [];
    isLoaded.value = false;
    lastLoadTime.value = 0;
    error.value = null;
    groupedData.value = {
      exchanges: [],
      productsByExchange: {},
      contractsByProduct: {},
      instrumentsById: {},
      instrumentsByInstrumentId: {}
    };
  }
  function getGroupedData() {
    return groupedData.value;
  }
  function ensureDataLoaded() {
    return __async(this, null, function* () {
      if (!isLoaded.value || !isDataFresh.value) {
        yield loadInstruments();
      }
    });
  }
  function getAllInstruments() {
    return __async(this, null, function* () {
      yield ensureDataLoaded();
      return instruments.value;
    });
  }
  function safeGetInstrumentById(id) {
    return __async(this, null, function* () {
      yield ensureDataLoaded();
      return getInstrumentById(id);
    });
  }
  function safeGetInstrumentByInstrumentId(instrumentId) {
    return __async(this, null, function* () {
      yield ensureDataLoaded();
      return getInstrumentByInstrumentId(instrumentId);
    });
  }
  return {
    // 状态
    instruments,
    isLoading,
    isLoaded,
    error,
    isDataFresh,
    // 方法
    loadInstruments,
    getInstrumentById,
    getInstrumentByInstrumentId,
    getExchanges,
    getProductsByExchange,
    getContractsByProduct,
    searchInstruments,
    clearCache,
    getGroupedData,
    ensureDataLoaded,
    getAllInstruments,
    safeGetInstrumentById,
    safeGetInstrumentByInstrumentId
  };
}, {
  persist: {
    key: "instrument-store",
    storage: {
      getItem: common_vendor.index.getStorageSync,
      setItem: common_vendor.index.setStorageSync
    },
    paths: ["instruments", "isLoaded", "lastLoadTime", "groupedData"]
  }
});
exports.useInstrumentStore = useInstrumentStore;
