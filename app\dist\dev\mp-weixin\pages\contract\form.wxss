/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-8bc4e71f {
  padding-bottom: 120rpx;
  padding-top: 20rpx;
}
.form-container.data-v-8bc4e71f {
  padding: 20rpx;
}
.form-section.data-v-8bc4e71f {
  margin-bottom: 20rpx;
}
.form-section .section-title.data-v-8bc4e71f {
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 30rpx;
  padding-bottom: 16rpx;
  position: relative;
}
.form-section .section-title.data-v-8bc4e71f::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2rpx;
}
.form-actions.data-v-8bc4e71f {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: white;
  display: flex;
  gap: 20rpx;
  z-index: 10;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.form-actions.data-v-8bc4e71f .wd-button {
  flex: 1;
}
.data-v-8bc4e71f  .dj-form-field,.data-v-8bc4e71f  .dj-form-textarea {
  background: white;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.data-v-8bc4e71f  .dj-form-field:hover,.data-v-8bc4e71f  .dj-form-textarea:hover {
  background-color: #f9fafc;
}
.data-v-8bc4e71f  .dj-form-field:focus-within,.data-v-8bc4e71f  .dj-form-textarea:focus-within {
  background-color: #f9fafc;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.data-v-8bc4e71f  .dj-form-field.is-disabled,.data-v-8bc4e71f  .dj-form-textarea.is-disabled {
  background-color: #f5f7fa;
  cursor: not-allowed;
}
.data-v-8bc4e71f  .dj-form-field::before,.data-v-8bc4e71f  .dj-form-textarea::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, #667eea, #764ba2);
}
.data-v-8bc4e71f  .dj-form-label {
  color: #303133;
  text-align: right;
  justify-content: flex-end;
  width: 200rpx;
  padding-right: 24rpx;
  font-size: 28rpx;
}
.data-v-8bc4e71f  .dj-form-input {
  color: #303133;
  font-size: 28rpx;
}
.data-v-8bc4e71f  .dj-form-input.is-placeholder {
  color: #909399;
  font-style: italic;
}
.data-v-8bc4e71f  .dj-form-input:focus {
  color: #667eea;
}
.data-v-8bc4e71f  .dj-form-textarea-container {
  width: 100%;
}
.data-v-8bc4e71f  .dj-btn-danger,.data-v-8bc4e71f  .dj-btn-secondary,.data-v-8bc4e71f  .dj-btn-primary {
  border-radius: 8rpx;
  font-weight: 500;
  font-size: 32rpx;
  transition: all 0.3s ease;
  padding: 20rpx;
}
.data-v-8bc4e71f  .dj-btn-danger:hover,.data-v-8bc4e71f  .dj-btn-secondary:hover,.data-v-8bc4e71f  .dj-btn-primary:hover {
  opacity: 0.9;
}
.data-v-8bc4e71f  .dj-btn-danger:active,.data-v-8bc4e71f  .dj-btn-secondary:active,.data-v-8bc4e71f  .dj-btn-primary:active {
  opacity: 1;
}
.data-v-8bc4e71f  .dj-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
}
.data-v-8bc4e71f  .dj-btn-primary:hover {
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  opacity: 0.95;
}
.data-v-8bc4e71f  .dj-btn-primary:active {
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
  opacity: 1;
}
.data-v-8bc4e71f  .dj-btn-primary.is-loading {
  background: linear-gradient(135deg, #a5b0f3 0%, #a586c0 100%);
  opacity: 0.8;
}
.data-v-8bc4e71f  .dj-btn-secondary {
  background: white;
  color: #606266;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.data-v-8bc4e71f  .dj-btn-secondary:hover {
  color: #303133;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.data-v-8bc4e71f  .dj-btn-secondary:active {
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.data-v-8bc4e71f  .dj-btn-danger {
  background: linear-gradient(135deg, #f56c6c 0%, #e64242 100%);
  border: none;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(245, 108, 108, 0.2);
}
.data-v-8bc4e71f  .dj-btn-danger:hover {
  box-shadow: 0 4rpx 12rpx rgba(245, 108, 108, 0.3);
  opacity: 0.95;
}
.data-v-8bc4e71f  .dj-btn-danger:active {
  box-shadow: 0 2rpx 8rpx rgba(245, 108, 108, 0.2);
  opacity: 1;
}
.data-v-8bc4e71f  .dj-price-type-radio-group {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
}
.data-v-8bc4e71f  .dj-price-type-radio-group .wd-radio {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0;
}
.data-v-8bc4e71f  .dj-radio .wd-checkbox__label,.data-v-8bc4e71f  .dj-checkbox .wd-checkbox__label,.data-v-8bc4e71f  .dj-radio .wd-radio__label,.data-v-8bc4e71f  .dj-checkbox .wd-radio__label {
  color: #303133;
  font-size: 28rpx;
  font-weight: 500;
  padding-left: 12rpx;
}
.data-v-8bc4e71f  .dj-radio .wd-checkbox__shape,.data-v-8bc4e71f  .dj-checkbox .wd-checkbox__shape,.data-v-8bc4e71f  .dj-radio .wd-radio__shape,.data-v-8bc4e71f  .dj-checkbox .wd-radio__shape {
  border-color: #c0c4cc;
  transition: all 0.3s ease;
}
.data-v-8bc4e71f  .dj-radio .wd-checkbox__shape.is-checked,.data-v-8bc4e71f  .dj-checkbox .wd-checkbox__shape.is-checked,.data-v-8bc4e71f  .dj-radio .wd-radio__shape.is-checked,.data-v-8bc4e71f  .dj-checkbox .wd-radio__shape.is-checked {
  background-color: #667eea;
  border-color: #667eea;
  box-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
}
.data-v-8bc4e71f  .dj-checkbox .wd-checkbox__shape {
  border-radius: 4rpx;
}
.data-v-8bc4e71f  .dj-search {
  background: white;
  border-radius: 40rpx;
  padding: 0 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.data-v-8bc4e71f  .dj-search:focus-within {
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.data-v-8bc4e71f  .dj-search .wd-search__input {
  background: transparent;
  color: #303133;
  font-size: 28rpx;
  height: 80rpx;
}
.data-v-8bc4e71f  .dj-search .wd-search__icon {
  color: #667eea;
}
.data-v-8bc4e71f  .dj-search .wd-search__placeholder {
  color: #909399;
  font-size: 28rpx;
}
.data-v-8bc4e71f  .dj-popup .wd-popup__container {
  border-radius: 16rpx 16rpx 0 0;
  background: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}