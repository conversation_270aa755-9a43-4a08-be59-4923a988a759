"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const hooks_usePageAuth = require("./hooks/usePageAuth.js");
const http_interceptor = require("./http/interceptor.js");
const router_interceptor = require("./router/interceptor.js");
const store_index = require("./store/index.js");
const store_socket = require("./store/socket.js");
if (!Math) {
  "./pages/workspace/index.js";
  "./pages/index/index.js";
  "./pages/about/about.js";
  "./pages/about/alova.js";
  "./pages/about/vue-query.js";
  "./pages/contract/cancel-records.js";
  "./pages/contract/detail.js";
  "./pages/contract/form.js";
  "./pages/contract/list.js";
  "./pages/contract/pricer-list.js";
  "./pages/contract/setter-list.js";
  "./pages/login/index.js";
  "./pages/profile/change-password.js";
  "./pages/profile/index.js";
  "./pages/quotes/detail.js";
  "./pages/quotes/edit.js";
  "./pages/quotes/marketplace.js";
  "./pages/quotes/my-list.js";
  "./pages/quotes/public-list.js";
  "./pages/support/about.js";
  "./pages/support/content-viewer.js";
  "./pages/support/feedback.js";
  "./pages/test/debug-select-input.js";
  "./pages/test/market-demo.js";
  "./pages/test/test-instrument-store.js";
  "./pages/test/test-user-selector.js";
  "./pages/trade/CombinationSelector.js";
  "./pages/trade/execute.js";
  "./pages/trade/pricer-management.js";
  "./pages/trade/setter-management.js";
  "./pages/trade/TradeOperationForm.js";
  "./pages-sub/demo/index.js";
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "App",
  setup(__props) {
    hooks_usePageAuth.usePageAuth();
    common_vendor.onLaunch(() => {
      console.log("App Launch");
    });
    common_vendor.onShow(() => {
      console.log("App Show");
    });
    common_vendor.onHide(() => {
      console.log("App Hide");
    });
    return () => {
    };
  }
});
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(store_index.store);
  app.use(router_interceptor.routeInterceptor);
  app.use(http_interceptor.requestInterceptor);
  app.use(common_vendor.VueQueryPlugin);
  const socketStore = store_socket.useSocketStore();
  socketStore.connect();
  app.component("layout-default-uni", Layout_Default_Uni);
  app.component("layout-fg-tabbar-uni", Layout_FgTabbar_Uni);
  app.component("layout-tabbar-uni", Layout_Tabbar_Uni);
  return {
    app
  };
}
const Layout_Default_Uni = () => "./layouts/default.js";
const Layout_FgTabbar_Uni = () => "./layouts/fg-tabbar/fg-tabbar.js";
const Layout_Tabbar_Uni = () => "./layouts/tabbar.js";
createApp().app.mount("#app");
exports.createApp = createApp;
