"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_contract = require("../api/contract.js");
const store_instrument = require("../store/instrument.js");
function useContractData() {
  const instrumentStore = store_instrument.useInstrumentStore();
  const availableContracts = common_vendor.ref([]);
  const instrumentId = common_vendor.ref(null);
  const setterID = common_vendor.ref(null);
  const setterName = common_vendor.ref("");
  const instrument = common_vendor.ref(null);
  const currentContracts = common_vendor.computed(() => {
    if (!instrumentId.value || !setterID.value)
      return [];
    return availableContracts.value.filter(
      (contract) => contract.instrumentRefID === instrumentId.value && contract.setterID === setterID.value
    );
  });
  const availableCombinations = common_vendor.computed(() => {
    const combinations = {};
    availableContracts.value.forEach((contract) => {
      var _a, _b, _c;
      const key = `${contract.setterID}-${contract.instrumentRefID}`;
      if (!combinations[key]) {
        const setterName2 = ((_a = contract.setter) == null ? void 0 : _a.nickName) || `用户${contract.setterID}`;
        const instrumentName = ((_b = contract.instrument) == null ? void 0 : _b.instrument_name) || ((_c = contract.instrument) == null ? void 0 : _c.instrument_id) || `合约${contract.instrumentRefID}`;
        combinations[key] = {
          value: key,
          label: `${setterName2}-${instrumentName}`,
          setterID: contract.setterID,
          instrumentRefID: contract.instrumentRefID,
          setterName: setterName2,
          instrumentName,
          contract
          // 保存原始合同数据以便后续使用
        };
      }
    });
    return Object.values(combinations);
  });
  const currentCombination = common_vendor.computed(() => {
    if (!setterID.value || !instrumentId.value)
      return null;
    const key = `${setterID.value}-${instrumentId.value}`;
    return availableCombinations.value.find((combo) => combo.value === key) || null;
  });
  const currentCombinationDisplay = common_vendor.computed(() => {
    if (!currentCombination.value)
      return { setterName: "请选择", instrumentName: "交易组合" };
    return {
      setterName: currentCombination.value.setterName,
      instrumentName: currentCombination.value.instrumentName
    };
  });
  const pickerValue = common_vendor.computed({
    get: () => {
      var _a;
      return ((_a = currentCombination.value) == null ? void 0 : _a.value) || "";
    },
    set: () => {
    }
  });
  const getCurrentAvailableContracts = (isPointPrice) => {
    return common_vendor.computed(() => {
      if (isPointPrice) {
        return currentContracts.value.filter((contract) => contract.priceType === "basis");
      } else {
        return currentContracts.value.filter((contract) => contract.priceType === "fixed");
      }
    });
  };
  const getContractStats = () => {
    return common_vendor.computed(() => {
      const contracts = currentContracts.value;
      let remainingQuantity = 0;
      let frozenQuantity = 0;
      let totalWeightedPrice = 0;
      let totalQuantity = 0;
      contracts.forEach((contract) => {
        remainingQuantity += contract.remainingQuantity;
        frozenQuantity += contract.frozenQuantity;
        const availableQuantity = contract.remainingQuantity - contract.frozenQuantity;
        if (availableQuantity > 0) {
          totalWeightedPrice += contract.priceValue * availableQuantity;
          totalQuantity += availableQuantity;
        }
      });
      return {
        remainingQuantity,
        frozenQuantity,
        availableQuantity: totalQuantity,
        weightedPrice: totalQuantity > 0 ? totalWeightedPrice / totalQuantity : 0
      };
    });
  };
  const getContractStatsByType = (isPointPrice) => {
    return common_vendor.computed(() => {
      const contracts = getCurrentAvailableContracts(isPointPrice).value;
      let remainingQuantity = 0;
      let frozenQuantity = 0;
      let totalWeightedPrice = 0;
      let totalQuantity = 0;
      contracts.forEach((contract) => {
        remainingQuantity += contract.remainingQuantity;
        frozenQuantity += contract.frozenQuantity;
        const availableQuantity = contract.remainingQuantity - contract.frozenQuantity;
        if (availableQuantity > 0) {
          totalWeightedPrice += contract.priceValue * availableQuantity;
          totalQuantity += availableQuantity;
        }
      });
      return {
        remainingQuantity,
        frozenQuantity,
        availableQuantity: totalQuantity,
        weightedPrice: totalQuantity > 0 ? totalWeightedPrice / totalQuantity : 0
      };
    });
  };
  function loadInstrument(id) {
    return __async(this, null, function* () {
      try {
        console.log("开始加载期货合约信息, ID:", id);
        const instrumentData = yield instrumentStore.safeGetInstrumentById(id);
        if (instrumentData) {
          instrument.value = instrumentData;
          console.log("期货合约信息加载成功:", instrument.value);
        } else {
          console.warn("未找到ID为", id, "的期货合约");
        }
      } catch (error) {
        console.error("加载期货合约信息失败:", error);
      }
    });
  }
  function loadAllAvailableContracts() {
    return __async(this, null, function* () {
      var _a;
      try {
        console.log("开始加载所有可用合同");
        const response = yield api_contract.getContractsAsPricer({ status: "Executing" });
        console.log("合同API响应:", response);
        if (response.code === 0) {
          availableContracts.value = ((_a = response.data) == null ? void 0 : _a.list) || [];
          console.log("所有可用合同加载成功，数量:", availableContracts.value.length);
          if (availableContracts.value.length > 0 && availableCombinations.value.length > 0 && !currentCombination.value) {
            const firstCombo = availableCombinations.value[0];
            setterID.value = firstCombo.setterID;
            instrumentId.value = firstCombo.instrumentRefID;
            setterName.value = firstCombo.setterName;
            if (firstCombo.instrumentRefID) {
              yield loadInstrument(firstCombo.instrumentRefID);
            }
          }
        } else {
          console.warn("合同API返回错误:", response.msg);
        }
      } catch (error) {
        console.error("加载所有可用合同失败:", error);
      }
    });
  }
  function handleCombinationChange(selectedValue) {
    console.log("组合选择变更:", selectedValue);
    const selectedCombo = availableCombinations.value.find((combo) => combo.value === selectedValue);
    if (selectedCombo) {
      setterID.value = selectedCombo.setterID;
      instrumentId.value = selectedCombo.instrumentRefID;
      setterName.value = selectedCombo.setterName;
      console.log("更新选中组合:", {
        setterID: setterID.value,
        instrumentId: instrumentId.value,
        setterName: setterName.value
      });
      if (selectedCombo.instrumentRefID) {
        loadInstrument(selectedCombo.instrumentRefID);
      }
    }
  }
  function resetContractData() {
    availableContracts.value = [];
    instrumentId.value = null;
    setterID.value = null;
    setterName.value = "";
    instrument.value = null;
  }
  return {
    // 状态
    availableContracts,
    instrumentId,
    setterID,
    setterName,
    instrument,
    // 计算属性
    currentContracts,
    availableCombinations,
    currentCombination,
    currentCombinationDisplay,
    pickerValue,
    // 方法
    getCurrentAvailableContracts,
    getContractStats,
    getContractStatsByType,
    loadInstrument,
    loadAllAvailableContracts,
    handleCombinationChange,
    resetContractData
  };
}
exports.useContractData = useContractData;
