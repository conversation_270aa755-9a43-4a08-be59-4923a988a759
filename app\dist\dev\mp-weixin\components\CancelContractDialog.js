"use strict";
const common_vendor = require("../common/vendor.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_textarea2 = common_vendor.resolveComponent("wd-textarea");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_icon2 + _easycom_wd_input2 + _easycom_wd_button2 + _easycom_wd_textarea2 + _easycom_wd_popup2)();
}
const _easycom_wd_icon = () => "../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_input = () => "../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_button = () => "../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_textarea = () => "../node-modules/wot-design-uni/components/wd-textarea/wd-textarea.js";
const _easycom_wd_popup = () => "../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_input + _easycom_wd_button + _easycom_wd_textarea + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "CancelContractDialog",
  props: {
    modelValue: { type: Boolean, default: false },
    contractData: { default: null }
  },
  emits: ["update:modelValue", "confirm"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const visible = common_vendor.ref(false);
    const cancelQuantity = common_vendor.ref("");
    const cancelReason = common_vendor.ref("");
    const loading = common_vendor.ref(false);
    const maxQuantity = common_vendor.computed(() => {
      var _a;
      return ((_a = props.contractData) == null ? void 0 : _a.remainingQuantity) || 0;
    });
    common_vendor.watch(() => props.modelValue, (newVal) => {
      visible.value = newVal;
      if (newVal) {
        cancelQuantity.value = "";
        cancelReason.value = "";
        loading.value = false;
      }
    });
    common_vendor.watch(visible, (newVal) => {
      emit("update:modelValue", newVal);
    });
    const handleClose = () => {
      visible.value = false;
    };
    const fillAllQuantity = () => {
      cancelQuantity.value = String(maxQuantity.value);
    };
    const handleQuantityInput = (value) => {
      const num = parseInt(value);
      if (num > maxQuantity.value) {
        cancelQuantity.value = String(maxQuantity.value);
      }
    };
    const handleConfirm = () => {
      const quantity = parseInt(cancelQuantity.value);
      if (!quantity || quantity <= 0) {
        common_vendor.index.showToast({
          title: "请输入有效的取消数量",
          icon: "error"
        });
        return;
      }
      if (quantity > maxQuantity.value) {
        common_vendor.index.showToast({
          title: "取消数量不能超过剩余数量",
          icon: "error"
        });
        return;
      }
      emit("confirm", {
        cancelQuantity: quantity,
        reason: cancelReason.value || "用户取消"
      });
    };
    return (_ctx, _cache) => {
      var _a, _b, _c, _d;
      return {
        a: common_vendor.o(handleClose),
        b: common_vendor.p({
          name: "close",
          size: "20px"
        }),
        c: common_vendor.t((_a = _ctx.contractData) == null ? void 0 : _a.contractCode),
        d: common_vendor.t((_b = _ctx.contractData) == null ? void 0 : _b.remainingQuantity),
        e: common_vendor.o(handleQuantityInput),
        f: common_vendor.o(($event) => cancelQuantity.value = $event),
        g: common_vendor.p({
          type: "number",
          placeholder: "请输入取消数量",
          max: (_c = _ctx.contractData) == null ? void 0 : _c.remainingQuantity,
          min: 1,
          modelValue: cancelQuantity.value
        }),
        h: common_vendor.o(fillAllQuantity),
        i: common_vendor.p({
          type: "info",
          size: "small",
          ["custom-style"]: "margin-left: 20rpx;"
        }),
        j: common_vendor.t((_d = _ctx.contractData) == null ? void 0 : _d.remainingQuantity),
        k: common_vendor.o(($event) => cancelReason.value = $event),
        l: common_vendor.p({
          placeholder: "请输入取消原因，例如：线下提货",
          maxlength: 200,
          ["show-word-limit"]: true,
          ["auto-height"]: true,
          modelValue: cancelReason.value
        }),
        m: common_vendor.o(handleClose),
        n: common_vendor.p({
          type: "info",
          size: "large",
          ["custom-style"]: "margin-right: 20rpx;"
        }),
        o: common_vendor.o(handleConfirm),
        p: common_vendor.p({
          type: "primary",
          size: "large",
          loading: loading.value
        }),
        q: common_vendor.o(($event) => visible.value = $event),
        r: common_vendor.p({
          position: "center",
          ["close-on-click-modal"]: false,
          ["custom-style"]: "border-radius: 16rpx; overflow: hidden;",
          modelValue: visible.value
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e269c6f4"]]);
wx.createComponent(Component);
