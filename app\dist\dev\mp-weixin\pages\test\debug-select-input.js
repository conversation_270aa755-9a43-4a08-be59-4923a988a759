"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
if (!Math) {
  SelectInput();
}
const SelectInput = () => "../../components/SelectInput.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "DebugSelectInput"
}), {
  __name: "debug-select-input",
  setup(__props) {
    const testValue = common_vendor.ref("");
    const debugInfo = common_vendor.ref([]);
    const options = [
      { value: "apple", label: "苹果", description: "红色水果" },
      { value: "banana", label: "香蕉", description: "黄色水果" },
      { value: "orange", label: "橙子", description: "橙色水果" },
      { value: "grape", label: "葡萄", description: "紫色水果" }
    ];
    function addDebugInfo(message) {
      const timestamp = (/* @__PURE__ */ new Date()).toLocaleTimeString();
      debugInfo.value.unshift(`[${timestamp}] ${message}`);
      if (debugInfo.value.length > 20) {
        debugInfo.value = debugInfo.value.slice(0, 20);
      }
    }
    function handleChange(value, option) {
      addDebugInfo(`Change: value="${value}", option=${option ? JSON.stringify(option) : "null"}`);
    }
    function handleSearch(keyword) {
      addDebugInfo(`Search: keyword="${keyword}"`);
    }
    common_vendor.watch(testValue, (newValue, oldValue) => {
      addDebugInfo(`Watch: "${oldValue}" -> "${newValue}"`);
    });
    function clearDebug() {
      debugInfo.value = [];
    }
    function clearValue() {
      testValue.value = "";
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(handleChange),
        b: common_vendor.o(handleSearch),
        c: common_vendor.o(($event) => testValue.value = $event),
        d: common_vendor.p({
          label: "测试选择",
          ["label-width"]: "160rpx",
          placeholder: "输入水果名称",
          options,
          ["min-chars"]: 1,
          ["max-results"]: 5,
          clearable: true,
          modelValue: testValue.value
        }),
        e: common_vendor.t(testValue.value || "(空)"),
        f: common_vendor.o(clearValue),
        g: common_vendor.o(clearDebug),
        h: common_vendor.f(debugInfo.value, (info, index, i0) => {
          return {
            a: common_vendor.t(info),
            b: index
          };
        }),
        i: debugInfo.value.length === 0
      }, debugInfo.value.length === 0 ? {} : {}, {
        j: common_vendor.f(options, (option, index, i0) => {
          return {
            a: common_vendor.t(option.value),
            b: common_vendor.t(option.label),
            c: common_vendor.t(option.description),
            d: index
          };
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cc338929"]]);
wx.createPage(MiniProgramPage);
