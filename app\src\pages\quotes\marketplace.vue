<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "报价市场",
    "navigationStyle": "default"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { getPublicQuotationList } from '@/api/quotation'
import QuotationCard from '@/components/marketplace/QuotationCard.vue'
import type {
  IQuotationResponse,
  IQuotationListRequest,
  QuotationPriceType
} from '@/types/quotation'

defineOptions({
  name: 'QuotationMarketplace'
})

// ==================== 响应式数据声明 ====================

// 页面状态管理
const isLoading = ref<boolean>(false)
const isRefreshing = ref<boolean>(false)
const hasMore = ref<boolean>(true)

// 搜索和筛选状态
const searchKeyword = ref<string>('')
const showFilterPanel = ref<boolean>(false)
const selectedPriceType = ref<QuotationPriceType | ''>('')

// ActionSheet交互状态
const showActionSheet = ref<boolean>(false)
const selectedQuotation = ref<IQuotationResponse | null>(null)

// 数据存储
const quotationList = ref<IQuotationResponse[]>([])
const currentPage = ref<number>(1)
const pageSize = ref<number>(10)
const total = ref<number>(0)

// 静态配置数据
const priceTypeOptions = [
  { label: '全部类型', value: '' },
  { label: '一口价', value: 'Fixed' },
  { label: '基差报价', value: 'Basis' }
] as const

// ==================== 计算属性 ====================

const hasActiveFilters = computed(() => {
  return selectedPriceType.value || searchKeyword.value.trim()
})

// ==================== 数据获取相关函数 ====================


/**
 * 加载报价列表
 * @param refresh 是否刷新（重置分页）
 */
async function loadQuotationList(refresh: boolean = false): Promise<void> {
  if (refresh) {
    currentPage.value = 1
    quotationList.value = []
    isRefreshing.value = true
  } else {
    isLoading.value = true
  }

  try {
    const params: IQuotationListRequest = {
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 添加筛选条件
    if (selectedPriceType.value) {
      params.priceType = selectedPriceType.value
    }
    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }

    const res = await getPublicQuotationList(params)
    const { list, total: totalCount } = res.data

    if (refresh) {
      quotationList.value = list
    } else {
      quotationList.value.push(...list)
    }

    total.value = totalCount
    hasMore.value = quotationList.value.length < totalCount

  } catch (error) {
    console.error('加载报价列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

/**
 * 加载更多数据
 */
async function loadMore(): Promise<void> {
  if (!hasMore.value || isLoading.value) return

  currentPage.value++
  await loadQuotationList()
}

/**
 * 下拉刷新
 */
async function onRefresh(): Promise<void> {
  await loadQuotationList(true)
}

// ==================== 搜索和筛选相关函数 ====================

/**
 * 执行搜索
 */
async function onSearch(): Promise<void> {
  await loadQuotationList(true)
}

/**
 * 清空搜索关键词
 */
async function clearSearch(): Promise<void> {
  searchKeyword.value = ''
  await loadQuotationList(true)
}

/**
 * 显示筛选面板
 */
function showFilters(): void {
  showFilterPanel.value = true
}


/**
 * 价格类型筛选
 * @param priceType 价格类型
 */
async function onPriceTypeFilter(priceType: QuotationPriceType | ''): Promise<void> {
  selectedPriceType.value = priceType
  showFilterPanel.value = false
  await loadQuotationList(true)
}

/**
 * 清空所有筛选条件
 */
async function clearFilters(): Promise<void> {
  selectedPriceType.value = ''
  searchKeyword.value = ''
  showFilterPanel.value = false
  await loadQuotationList(true)
}

// ==================== 交互处理函数 ====================

// ActionSheet操作选项配置
const actionSheetOptions = [
  { name: '查看详情', value: 'detail' },
  { name: '发起点价', value: 'contact' }
]

/**
 * 显示报价操作菜单
 * @param quotation 选中的报价
 */
function showQuotationActions(quotation: IQuotationResponse): void {
  selectedQuotation.value = quotation
  showActionSheet.value = true
}

/**
 * 处理ActionSheet选择
 * @param item 选中的操作项
 */
function handleActionSelect({ item }: { item: { name: string; value: string } }): void {
  showActionSheet.value = false

  if (!selectedQuotation.value) return

  switch (item.value) {
    case 'detail':
      viewDetail(selectedQuotation.value)
      break
    case 'contact':
      contactPublisher(selectedQuotation.value)
      break
  }

  selectedQuotation.value = null
}

/**
 * 查看报价详情
 * @param quotation 报价对象
 */
function viewDetail(quotation: IQuotationResponse): void {
  uni.navigateTo({
    url: `/pages/quotes/detail?id=${quotation.id}&from=marketplace`
  })
}

/**
 * 联系发布者/发起点价
 * @param _quotation 报价对象（暂未使用）
 */
function contactPublisher(_quotation: IQuotationResponse): void {
  // TODO: 实现联系功能，可能是发起点价请求
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

/**
 * 处理发布者点击事件，跳转到用户报价主页
 * @param userID 用户ID
 */
function handlePublisherClick(userID: number): void {
  uni.navigateTo({
    url: `/pages/quotes/public-list?id=${userID}`
  })
}

// ==================== 格式化和工具函数 ====================
// 注意：报价卡片相关的格式化函数已迁移到 QuotationCard.vue 组件中

// ==================== 页面生命周期函数 ====================

/**
 * 页面挂载时初始化数据
 */
onMounted(async () => {
  await loadQuotationList()
})
</script>

<template>
  <view class="page-container gradient-bg-primary">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <wd-input
          v-model="searchKeyword"
          placeholder="搜索报价标题、企业名称..."
          clearable
          custom-class="search-input"
          @confirm="onSearch"
          @clear="clearSearch"
        >
          <template #prefix>
            <wd-icon name="search" size="32rpx" custom-class="search-icon" />
          </template>
        </wd-input>

        <view class="filter-button" @click="showFilters">
          <wd-icon name="filter" size="32rpx" custom-class="filter-icon" />
          <text class="filter-text">筛选</text>
          <view v-if="hasActiveFilters" class="filter-dot"></view>
        </view>
      </view>

      <!-- 活跃筛选标签 -->
      <view v-if="hasActiveFilters" class="active-filters">
        <view v-if="selectedPriceType" class="filter-tag">
          {{ priceTypeOptions.find(opt => opt.value === selectedPriceType)?.label }}
          <wd-icon name="close" size="24rpx" custom-class="tag-close-icon" @click="onPriceTypeFilter('')" />
        </view>
      </view>
    </view>

    <!-- 列表内容 -->
    <scroll-view 
      class="scroll-container"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view class="list-container">
        <!-- 报价列表 -->
        <view v-if="quotationList.length > 0" class="quotation-list">
          <QuotationCard
            v-for="quotation in quotationList"
            :key="quotation.id"
            :quotation="quotation"
            @click="showQuotationActions"
            @publisherClick="handlePublisherClick"
          />
        </view>
        
        <!-- 空状态 -->
        <view v-else-if="!isLoading" class="empty-state">
          <wd-img
            src="/static/images/empty-market.png"
            width="200rpx"
            height="200rpx"
            mode="aspectFit"
            custom-class="empty-image"
          />
          <text class="empty-text">
            {{ hasActiveFilters ? '没有找到匹配的报价' : '市场暂无报价' }}
          </text>
          <wd-button
            v-if="hasActiveFilters"
            type="primary"
            custom-class="empty-button"
            @click="clearFilters"
          >
            清空筛选条件
          </wd-button>
        </view>

        <!-- 加载更多 -->
        <view v-if="isLoading && quotationList.length > 0" class="loading-more">
          <wd-loading size="24rpx" custom-class="loading-spinner" />
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 没有更多 -->
        <view v-if="!hasMore && quotationList.length > 0" class="no-more">
          <text>没有更多报价了</text>
        </view>
      </view>
    </scroll-view>

    <!-- 操作菜单 -->
    <wd-action-sheet
      v-model="showActionSheet"
      :actions="actionSheetOptions"
      cancel-text="取消"
      custom-class="action-sheet"
      @select="handleActionSelect"
    />

    <!-- 筛选面板 -->
    <wd-popup
      v-model="showFilterPanel"
      position="right"
      :z-index="999"
      custom-style="width: 75vw; height: 100vh;"
      custom-class="filter-popup"
    >
      <view class="filter-panel">
        <view class="filter-header">
          <text class="filter-title">筛选条件</text>
          <wd-button type="text" custom-class="reset-button" @click="clearFilters">重置</wd-button>
        </view>


        <!-- 价格类型筛选 -->
        <view class="filter-section">
          <text class="section-title">价格类型</text>
          <view class="tag-list">
            <wd-tag
              v-for="option in priceTypeOptions"
              :key="option.value"
              :type="selectedPriceType === option.value ? 'primary' : 'default'"
              custom-class="filter-tag-item"
              @click="onPriceTypeFilter(option.value as any)"
            >
              {{ option.label }}
            </wd-tag>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
// 设计系统变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$primary-dark: #764ba2;
$bg-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
$bg-card: rgba(255, 255, 255, 0.95);
$radius-sm: 8rpx;
$radius-md: 12rpx;
$radius-lg: 20rpx;
$radius-xl: 44rpx;
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
$shadow-focus: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
$spacing-sm: 20rpx;
$spacing-md: 36rpx;
$spacing-lg: 48rpx;

// 页面容器样式已移至全局样式文件
.page-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  box-sizing: border-box;
}

.search-section {
  background: $bg-card;
  backdrop-filter: blur(10rpx);
  padding: $spacing-sm;
  margin: $spacing-sm;
  border-radius: $radius-lg;
  box-shadow: $shadow-md;
  width: calc(100% - 40rpx);
  box-sizing: border-box;
  animation: slideInDown 0.3s ease-out;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 16rpx;

  :deep(.search-input) {
    flex: 1;
    border-radius: $radius-md !important;
    border: 2rpx solid #e4e7ed !important;
    transition: all 0.3s ease !important;

    &:focus-within {
      border-color: $primary-color !important;
      box-shadow: $shadow-focus !important;
    }
  }

  :deep(.search-icon) {
    color: $primary-color !important;
  }
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  border-radius: $radius-md;
  position: relative;
  background: rgba(102, 126, 234, 0.05);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: $primary-color;
    transform: translateY(-1rpx);
  }

  &:active {
    transform: translateY(0);
  }

  .filter-text {
    font-size: 26rpx;
    color: $primary-color;
    font-weight: 500;
  }

  :deep(.filter-icon) {
    color: $primary-color !important;
  }

  .filter-dot {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    width: 12rpx;
    height: 12rpx;
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    border-radius: 50%;
    box-shadow: 0 2rpx 4rpx rgba(255, 71, 87, 0.3);
    animation: pulse 2s infinite;
  }
}

.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 16rpx;
  animation: fadeIn 0.3s ease-out;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: $primary-gradient;
  color: white;
  font-size: 24rpx;
  border-radius: $radius-lg;
  box-shadow: $shadow-sm;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-1rpx);
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  }

  :deep(.tag-close-icon) {
    color: white !important;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.2);
    }
  }
}

.scroll-container {
  flex: 1;
  padding: $spacing-sm;
  box-sizing: border-box;
}

.quotation-list {
  width: 100%;
  box-sizing: border-box;

  :deep(.quotation-card) {
    background: $bg-card !important;
    backdrop-filter: blur(10rpx) !important;
    border-radius: $radius-lg !important;
    box-shadow: $shadow-md !important;
    transition: all 0.3s ease !important;

    &:hover {
      transform: translateY(-4rpx) !important;
      box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15) !important;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: $bg-card;
  backdrop-filter: blur(10rpx);
  border-radius: $radius-lg;
  box-shadow: $shadow-md;
  margin: $spacing-md;
  animation: fadeIn 0.5s ease-out;

  :deep(.empty-image) {
    opacity: 0.6;
    filter: grayscale(20%);
  }

  .empty-text {
    font-size: 28rpx;
    color: #606266;
    margin: 24rpx 0 40rpx;
    text-align: center;
    font-weight: 500;
  }

  :deep(.empty-button) {
    background: $primary-gradient !important;
    border-radius: $radius-xl !important;
    font-weight: 600 !important;
    box-shadow: $shadow-sm !important;
    transition: all 0.3s ease !important;

    &:hover {
      transform: translateY(-2rpx) !important;
      box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3) !important;
    }
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: $bg-card;
  backdrop-filter: blur(10rpx);
  border-radius: $radius-lg;
  margin: $spacing-sm;
  box-shadow: $shadow-sm;

  :deep(.loading-spinner) {
    color: $primary-color !important;
  }

  .loading-text {
    margin-left: 16rpx;
    font-size: 26rpx;
    color: $primary-color;
    font-weight: 500;
  }
}

.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #909399;
  background: $bg-card;
  backdrop-filter: blur(10rpx);
  border-radius: $radius-lg;
  margin: $spacing-sm;
  box-shadow: $shadow-sm;
}

.filter-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: $bg-card;
  backdrop-filter: blur(10rpx);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 2rpx solid rgba(228, 231, 237, 0.5);
  background: rgba(102, 126, 234, 0.05);

  .filter-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  :deep(.reset-button) {
    color: $primary-color !important;
    font-weight: 500 !important;
  }
}

.filter-section {
  padding: 32rpx 24rpx;

  .section-title {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 20rpx;
  }
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;

  :deep(.filter-tag-item) {
    margin: 0 !important;
    font-size: 26rpx !important;
    padding: 12rpx 20rpx !important;
    border-radius: $radius-lg !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    border: 2rpx solid transparent !important;

    &:hover {
      transform: translateY(-1rpx) !important;
      box-shadow: $shadow-sm !important;
    }

    &[data-type="primary"] {
      background: $primary-gradient !important;
      color: white !important;
      box-shadow: $shadow-sm !important;
    }

    &[data-type="default"] {
      background: rgba(102, 126, 234, 0.05) !important;
      color: $primary-color !important;
      border-color: rgba(102, 126, 234, 0.2) !important;

      &:hover {
        background: rgba(102, 126, 234, 0.1) !important;
        border-color: $primary-color !important;
      }
    }
  }
}

// 动画定义
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .search-section {
    margin: 10rpx;
    padding: 16rpx;
    width: calc(100% - 20rpx);
  }

  .scroll-container {
    padding: 10rpx;
  }

  .empty-state {
    margin: 16rpx;
    padding: 80rpx 20rpx;
  }
}
</style>