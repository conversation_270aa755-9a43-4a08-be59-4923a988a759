<layout-tabbar-uni class="data-v-b5177f87" u-s="{{['d']}}" u-i="b5177f87-0" bind:__l="__l"><view class="data-v-b5177f87"><view class="mt-8 text-center text-xl text-gray-400 data-v-b5177f87"> 组件使用、请求调用、unocss </view><request-comp class="data-v-b5177f87" u-i="b5177f87-1,b5177f87-0" bind:__l="__l"/><view class="mb-6 h-1px bg-_a_eee data-v-b5177f87"/><view class="text-center data-v-b5177f87"><button type="primary" size="mini" class="w-160px data-v-b5177f87" bindtap="{{a}}"> 前往 alova 示例页面 </button></view><view class="text-center data-v-b5177f87"><button type="primary" size="mini" class="w-160px data-v-b5177f87" bindtap="{{b}}"> vue-query 示例页面 </button></view><view class="mt-6 text-center text-sm data-v-b5177f87"><view class="inline-block w-80_a_ text-gray-400 data-v-b5177f87"> 为了方便脚手架动态生成不同UI模板，本页的按钮统一使用UI库无关的原生button </view></view></view></layout-tabbar-uni>