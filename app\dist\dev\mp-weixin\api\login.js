"use strict";
const common_vendor = require("../common/vendor.js");
const http_http = require("../http/http.js");
function logout() {
  return http_http.http.get("/user/logout");
}
function getWxCode() {
  return new Promise((resolve, reject) => {
    common_vendor.index.login({
      provider: "weixin",
      success: (res) => resolve(res),
      fail: (err) => reject(new Error(err))
    });
  });
}
exports.getWxCode = getWxCode;
exports.logout = logout;
