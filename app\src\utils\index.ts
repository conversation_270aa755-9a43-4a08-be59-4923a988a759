import { pages, subPackages } from '@/pages.json'
import { isMpWeixin } from './platform'
import { tabbarList } from '@/layouts/fg-tabbar/tabbarList'

export function getLastPage() {
  // getCurrentPages() 至少有1个元素，所以不再额外判断
  // const lastPage = getCurrentPages().at(-1)
  // 上面那个在低版本安卓中打包会报错，所以改用下面这个【虽然我加了 src/interceptions/prototype.ts，但依然报错】
  const pages = getCurrentPages()
  return pages[pages.length - 1]
}

/**
 * 获取当前页面路由的 path 路径和 redirectPath 路径
 * path 如 '/pages/login/index'
 * redirectPath 如 '/pages/demo/base/route-interceptor'
 */
export function currRoute() {
  const lastPage = getLastPage()
  const currRoute = (lastPage as any).$page
  // console.log('lastPage.$page:', currRoute)
  // console.log('lastPage.$page.fullpath:', currRoute.fullPath)
  // console.log('lastPage.$page.options:', currRoute.options)
  // console.log('lastPage.options:', (lastPage as any).options)
  // 经过多端测试，只有 fullPath 靠谱，其他都不靠谱
  const { fullPath } = currRoute as { fullPath: string }
  // console.log(fullPath)
  // eg: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor (小程序)
  // eg: /pages/login/index?redirect=%2Fpages%2Froute-interceptor%2Findex%3Fname%3Dfeige%26age%3D30(h5)
  return getUrlObj(fullPath)
}

function ensureDecodeURIComponent(url: string) {
  if (url.startsWith('%')) {
    return ensureDecodeURIComponent(decodeURIComponent(url))
  }
  return url
}
/**
 * 解析 url 得到 path 和 query
 * 比如输入url: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor
 * 输出: {path: /pages/login/index, query: {redirect: /pages/demo/base/route-interceptor}}
 */
export function getUrlObj(url: string) {
  const [path, queryStr] = url.split('?')
  // console.log(path, queryStr)

  if (!queryStr) {
    return {
      path,
      query: {},
    }
  }
  const query: Record<string, string> = {}
  queryStr.split('&').forEach((item) => {
    const [key, value] = item.split('=')
    // console.log(key, value)
    query[key] = ensureDecodeURIComponent(value) // 这里需要统一 decodeURIComponent 一下，可以兼容h5和微信y
  })
  return { path, query }
}
/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 这里设计得通用一点，可以传递 key 作为判断依据，默认是 needLogin, 与 route-block 配对使用
 * 如果没有传 key，则表示所有的 pages，如果传递了 key, 则表示通过 key 过滤
 */
export function getAllPages(key = 'needLogin') {
  // 这里处理主包
  const mainPages = pages
    .filter(page => !key || page[key])
    .map(page => ({
      ...page,
      path: `/${page.path}`,
    }))

  // 这里处理分包
  const subPages: any[] = []
  subPackages.forEach((subPageObj) => {
    // console.log(subPageObj)
    const { root } = subPageObj

    subPageObj.pages
      .filter(page => !key || page[key])
      .forEach((page: { path: string } & Record<string, any>) => {
        subPages.push({
          ...page,
          path: `/${root}/${page.path}`,
        })
      })
  })
  const result = [...mainPages, ...subPages]
  // console.log(`getAllPages by ${key} result: `, result)
  return result
}

/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 只得到 path 数组
 */
export const getNeedLoginPages = (): string[] => getAllPages('needLogin').map(page => page.path)

/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 只得到 path 数组
 */
export const needLoginPages: string[] = getAllPages('needLogin').map(page => page.path)

/**
 * 根据微信小程序当前环境，判断应该获取的 baseUrl
 */
export function getEnvBaseUrl() {
  // 请求基准地址
  let baseUrl = import.meta.env.VITE_SERVER_BASEURL

  // # 有些同学可能需要在微信小程序里面根据 develop、trial、release 分别设置上传地址，参考代码如下。
  const VITE_SERVER_BASEURL__WEIXIN_DEVELOP = 'http://127.0.0.1:8888'
  const VITE_SERVER_BASEURL__WEIXIN_TRIAL = 'https://ukw0y1.laf.run'
  const VITE_SERVER_BASEURL__WEIXIN_RELEASE = 'https://ukw0y1.laf.run'

  // 微信小程序端环境区分
  if (isMpWeixin) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUrl = VITE_SERVER_BASEURL__WEIXIN_DEVELOP || baseUrl
        break
      case 'trial':
        baseUrl = VITE_SERVER_BASEURL__WEIXIN_TRIAL || baseUrl
        break
      case 'release':
        baseUrl = VITE_SERVER_BASEURL__WEIXIN_RELEASE || baseUrl
        break
    }
  }

  return baseUrl
}

/**
 * 根据微信小程序当前环境，判断应该获取的 UPLOAD_BASEURL
 */
export function getEnvBaseUploadUrl() {
  // 请求基准地址
  let baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL

  const VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP = 'https://ukw0y1.laf.run/upload'
  const VITE_UPLOAD_BASEURL__WEIXIN_TRIAL = 'https://ukw0y1.laf.run/upload'
  const VITE_UPLOAD_BASEURL__WEIXIN_RELEASE = 'https://ukw0y1.laf.run/upload'

  // 微信小程序端环境区分
  if (isMpWeixin) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUploadUrl = VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP || baseUploadUrl
        break
      case 'trial':
        baseUploadUrl = VITE_UPLOAD_BASEURL__WEIXIN_TRIAL || baseUploadUrl
        break
      case 'release':
        baseUploadUrl = VITE_UPLOAD_BASEURL__WEIXIN_RELEASE || baseUploadUrl
        break
    }
  }

  return baseUploadUrl
}

/**
 * 检查页面是否为 tabBar 页面
 * @param url 页面路径，如 '/pages/workspace/index' 或 'pages/workspace/index'，或者 '/' 表示首页
 * @returns 是否为 tabBar 页面
 */
export function isTabBarPage(url: string): boolean {
  // 处理特殊情况：空路径或根路径，通常指向首页
  if (!url || url === '/' || url === '') {
    // 检查首页是否在 tabBar 中
    // 从 pages.json 中找到 type="home" 的页面
    const homePages = pages.filter(page => page.type === 'home')
    for (const homePage of homePages) {
      const homePagePath = `/${homePage.path}`
      if (tabbarList.some(item => `/${item.pagePath}` === homePagePath)) {
        return true
      }
    }
    return false
  }

  // 标准化路径格式，确保以 '/' 开头
  const normalizedUrl = url.startsWith('/') ? url : `/${url}`

  // 移除查询参数
  const path = normalizedUrl.split('?')[0]

  // 检查是否在 tabBar 列表中
  return tabbarList.some(item => `/${item.pagePath}` === path)
}

/**
 * 统一的页面跳转函数
 * 自动判断目标页面是否为 tabBar 页面，选择合适的跳转方式
 * @param options 跳转选项
 * @param options.url 目标页面路径，支持 '/' 表示首页
 * @param options.success 成功回调
 * @param options.fail 失败回调
 * @param options.complete 完成回调
 */
export function navigateToPage(options: {
  url: string
  success?: () => void
  fail?: (error: any) => void
  complete?: () => void
}) {
  let { url } = options
  const { success, fail, complete } = options

  try {
    // 处理特殊情况：空路径或根路径，重定向到首页
    if (!url || url === '/' || url === '') {
      // 从 pages.json 中找到第一个 type="home" 的页面
      const homePage = pages.find(page => page.type === 'home')
      if (homePage) {
        url = `/${homePage.path}`
      } else {
        // 如果没有找到 home 页面，使用第一个 tabBar 页面
        url = `/${tabbarList[0].pagePath}`
      }
    }

    if (isTabBarPage(url)) {
      // 是 tabBar 页面，使用 switchTab
      uni.switchTab({
        url,
        success,
        fail: (error) => {
          console.error('switchTab 失败:', error)
          fail?.(error)
        },
        complete
      })
    } else {
      // 不是 tabBar 页面，使用 navigateTo
      uni.navigateTo({
        url,
        success,
        fail: (error) => {
          console.error('navigateTo 失败:', error)
          fail?.(error)
        },
        complete
      })
    }
  } catch (error) {
    console.error('页面跳转异常:', error)
    fail?.(error)
    complete?.()
  }
}


