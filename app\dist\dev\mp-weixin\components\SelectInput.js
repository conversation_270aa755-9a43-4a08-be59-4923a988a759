"use strict";
const common_vendor = require("../common/vendor.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  _easycom_wd_input2();
}
const _easycom_wd_input = () => "../node-modules/wot-design-uni/components/wd-input/wd-input.js";
if (!Math) {
  _easycom_wd_input();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "SelectInput",
  props: {
    modelValue: { default: "" },
    label: { default: "" },
    placeholder: { default: "请输入或选择" },
    labelWidth: { default: "160rpx" },
    required: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    clearable: { type: Boolean, default: true },
    options: { default: () => [] },
    filterKey: { default: "label" },
    maxResults: { default: 8 },
    minChars: { default: 1 }
  },
  emits: ["update:modelValue", "change", "search"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const inputValue = common_vendor.ref("");
    const showDropdown = common_vendor.ref(false);
    const selectedOption = common_vendor.ref(null);
    const filteredOptions = common_vendor.ref([]);
    const inputFocused = common_vendor.ref(false);
    const currentValue = common_vendor.computed({
      get: () => props.modelValue,
      set: (value) => {
        emit("update:modelValue", value);
      }
    });
    const displayValue = common_vendor.computed({
      get: () => {
        return inputValue.value;
      },
      set: (value) => {
        inputValue.value = value;
      }
    });
    const shouldShowDropdown = common_vendor.computed(() => {
      return showDropdown.value && filteredOptions.value.length > 0 && inputFocused.value && inputValue.value.length >= props.minChars;
    });
    function getOptionLabel(option) {
      return option[props.filterKey] || option.label || option.value;
    }
    function filterOptions(keyword) {
      if (!keyword.trim() || keyword.length < props.minChars) {
        filteredOptions.value = [];
        return;
      }
      const lowerKeyword = keyword.toLowerCase();
      filteredOptions.value = props.options.filter((option) => {
        const label = getOptionLabel(option);
        return label.toLowerCase().includes(lowerKeyword);
      }).slice(0, props.maxResults);
      emit("search", keyword);
    }
    function onInputFocus() {
      inputFocused.value = true;
      if (inputValue.value.trim() && inputValue.value.length >= props.minChars) {
        filterOptions(inputValue.value);
        showDropdown.value = true;
      }
    }
    function onInputBlur() {
      setTimeout(() => {
        inputFocused.value = false;
        showDropdown.value = false;
      }, 200);
    }
    function onInputChange(value) {
      inputValue.value = value;
      currentValue.value = value;
      if (value.trim() && value.length >= props.minChars) {
        filterOptions(value);
        showDropdown.value = true;
        if (!inputFocused.value) {
          inputFocused.value = true;
        }
      } else {
        filteredOptions.value = [];
        showDropdown.value = false;
      }
      const matchedOption = props.options.find(
        (option) => getOptionLabel(option) === value
      );
      if (!matchedOption) {
        selectedOption.value = null;
      } else {
        selectedOption.value = matchedOption;
      }
      emit("change", value, selectedOption.value);
    }
    function selectOption(option) {
      selectedOption.value = option;
      const optionLabel = getOptionLabel(option);
      inputValue.value = optionLabel;
      currentValue.value = optionLabel;
      showDropdown.value = false;
      inputFocused.value = false;
      emit("change", optionLabel, option);
    }
    function clearSelection() {
      selectedOption.value = null;
      inputValue.value = "";
      currentValue.value = "";
      filteredOptions.value = [];
      showDropdown.value = false;
      emit("change", "", null);
    }
    common_vendor.watch(() => props.modelValue, (newValue) => {
      if (newValue !== inputValue.value) {
        const option = props.options.find((opt) => opt.value === newValue);
        if (option) {
          selectedOption.value = option;
          inputValue.value = getOptionLabel(option);
        } else {
          selectedOption.value = null;
          inputValue.value = newValue || "";
        }
      }
    }, { immediate: true });
    common_vendor.watch(() => props.options, () => {
      if (inputValue.value.trim() && showDropdown.value) {
        filterOptions(inputValue.value);
      }
    }, { deep: true });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(onInputChange),
        b: common_vendor.o(onInputFocus),
        c: common_vendor.o(onInputBlur),
        d: common_vendor.o(clearSelection),
        e: common_vendor.p({
          ["model-value"]: displayValue.value,
          label: _ctx.label,
          ["label-width"]: _ctx.labelWidth,
          placeholder: _ctx.placeholder,
          required: _ctx.required,
          disabled: _ctx.disabled,
          clearable: _ctx.clearable
        }),
        f: shouldShowDropdown.value
      }, shouldShowDropdown.value ? {
        g: common_vendor.f(filteredOptions.value, (option, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(getOptionLabel(option)),
            b: option.description
          }, option.description ? {
            c: common_vendor.t(option.description)
          } : {}, {
            d: option.value,
            e: common_vendor.o(($event) => selectOption(option), option.value)
          });
        })
      } : {}, {
        h: shouldShowDropdown.value ? 1 : "",
        i: _ctx.label ? _ctx.labelWidth : "0rpx"
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-01d49153"]]);
wx.createComponent(Component);
