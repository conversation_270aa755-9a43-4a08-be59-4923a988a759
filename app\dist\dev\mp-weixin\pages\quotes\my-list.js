"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const api_quotation = require("../../api/quotation.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _component_layout_tabbar_uni = common_vendor.resolveComponent("layout-tabbar-uni");
  (_easycom_wd_button2 + _easycom_wd_loading2 + _component_layout_tabbar_uni)();
}
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
if (!Math) {
  (_easycom_wd_button + _easycom_wd_loading)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "MyQuotationList"
}), {
  __name: "my-list",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const activeFilter = common_vendor.ref("all");
    const filterOptions = [
      { label: "全部", value: "all" },
      { label: "上线", value: "online" },
      { label: "草稿", value: "draft" }
    ];
    const quotationList = common_vendor.ref([]);
    const currentPage = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const total = common_vendor.ref(0);
    const statusConfig = {
      Draft: {
        label: "草稿",
        color: "#909399",
        bgColor: "#f4f4f5",
        description: "报价草稿，未公开发布"
      },
      Active: {
        label: "上线",
        color: "#67C23A",
        bgColor: "#f0f9ff",
        description: "报价已公开，可被其他用户查看"
      }
    };
    const filteredList = common_vendor.computed(() => {
      if (activeFilter.value === "all") {
        return quotationList.value;
      }
      return quotationList.value;
    });
    common_vendor.computed(() => {
      return quotationList.value.filter((q) => q.status === "Active").length;
    });
    common_vendor.computed(() => {
      return quotationList.value.filter((q) => q.status === "Draft").length;
    });
    common_vendor.onMounted(() => {
      loadQuotationList();
    });
    common_vendor.onLoad((options) => {
    });
    function loadQuotationList(refresh = false) {
      return __async(this, null, function* () {
        if (refresh) {
          currentPage.value = 1;
          quotationList.value = [];
          isRefreshing.value = true;
        } else {
          isLoading.value = true;
        }
        try {
          const params = {
            page: currentPage.value,
            pageSize: pageSize.value
          };
          if (activeFilter.value === "online") {
            params.filter = "valid";
          } else if (activeFilter.value === "draft") {
            params.filter = "invalid";
          }
          const res = yield api_quotation.getMyQuotationList(params);
          const { list, total: totalCount } = res.data;
          if (refresh) {
            quotationList.value = list;
          } else {
            quotationList.value.push(...list);
          }
          total.value = totalCount;
          hasMore.value = quotationList.value.length < totalCount;
        } catch (error) {
          console.error("加载报价列表失败:", error);
          common_vendor.index.showToast({
            title: "加载失败",
            icon: "error"
          });
        } finally {
          isLoading.value = false;
          isRefreshing.value = false;
        }
      });
    }
    function loadMore() {
      return __async(this, null, function* () {
        if (!hasMore.value || isLoading.value)
          return;
        currentPage.value++;
        yield loadQuotationList();
      });
    }
    function onRefresh() {
      return __async(this, null, function* () {
        yield loadQuotationList(true);
      });
    }
    function onRefreshRestore() {
      isRefreshing.value = false;
    }
    function onFilterChange(filter) {
      return __async(this, null, function* () {
        activeFilter.value = filter;
        yield loadQuotationList(true);
      });
    }
    function createQuotation() {
      common_vendor.index.navigateTo({
        url: "/pages/quotes/edit"
      });
    }
    function viewPublicList() {
      const { ID } = userStore.userInfo;
      common_vendor.index.navigateTo({
        url: `/pages/quotes/public-list?id=${ID}`
      });
    }
    function editQuotation(quotation) {
      common_vendor.index.navigateTo({
        url: `/pages/quotes/edit?id=${quotation.id}`
      });
    }
    function viewDetail(quotation) {
      common_vendor.index.navigateTo({
        url: `/pages/quotes/detail?id=${quotation.id}&from=mylist`
      });
    }
    function publishQuotationItem(quotation) {
      return __async(this, null, function* () {
        try {
          common_vendor.index.showLoading({ title: "发布中..." });
          const expiresAt = /* @__PURE__ */ new Date();
          expiresAt.setDate(expiresAt.getDate() + 7);
          yield api_quotation.publishQuotation({
            id: quotation.id,
            expiresAt: expiresAt.toISOString()
          });
          common_vendor.index.showToast({
            title: "发布成功",
            icon: "success"
          });
          yield loadQuotationList(true);
        } catch (error) {
          console.error("发布报价失败:", error);
          common_vendor.index.showToast({
            title: "发布失败",
            icon: "error"
          });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
    function toggleQuotationStatusItem(quotation) {
      return __async(this, null, function* () {
        const isActive = quotation.status === "Active";
        const actionText = isActive ? "设为草稿" : "激活报价";
        const confirmText = isActive ? "确定要将此报价设为草稿吗？设为草稿后将不再对外展示。" : "确定要激活此报价吗？激活后将对外展示。";
        const res = yield new Promise((resolve) => {
          common_vendor.index.showModal({
            title: `确认${actionText}`,
            content: confirmText,
            success: (modalRes) => {
              resolve(modalRes.confirm);
            }
          });
        });
        if (!res)
          return;
        try {
          common_vendor.index.showLoading({ title: `${actionText}中...` });
          yield api_quotation.toggleQuotationStatus(quotation.id);
          common_vendor.index.showToast({
            title: `${actionText}成功`,
            icon: "success"
          });
          yield loadQuotationList(true);
        } catch (error) {
          console.error("切换状态失败:", error);
          common_vendor.index.showToast({
            title: `${actionText}失败`,
            icon: "error"
          });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
    function formatPrice(quotation) {
      if (quotation.priceType === "Fixed") {
        return `¥ ${quotation.price.toFixed(2)}`;
      } else if (quotation.priceType === "Basis" && quotation.instrumentRef) {
        if (quotation.price >= 0) {
          return `${quotation.instrumentRef.instrument_id} + ${quotation.price}`;
        } else {
          return `${quotation.instrumentRef.instrument_id} ${quotation.price}`;
        }
      } else {
        return quotation.price.toString();
      }
    }
    function deleteQuotationItem(quotation) {
      return __async(this, null, function* () {
        const res = yield new Promise((resolve) => {
          common_vendor.index.showModal({
            title: "确认删除",
            content: "确定要删除这个报价草稿吗？删除后无法恢复。",
            success: (modalRes) => {
              resolve(modalRes.confirm);
            }
          });
        });
        if (!res)
          return;
        try {
          common_vendor.index.showLoading({ title: "删除中..." });
          yield api_quotation.deleteQuotation(quotation.id);
          common_vendor.index.showToast({
            title: "删除成功",
            icon: "success"
          });
          yield loadQuotationList(true);
        } catch (error) {
          console.error("删除报价失败:", error);
          common_vendor.index.showToast({
            title: "删除失败",
            icon: "error"
          });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
    function formatRemainingTime(quotation) {
      if (quotation.status !== "Active")
        return "";
      if (quotation.isExpired) {
        return "已过期";
      }
      if (quotation.remainingHours <= 0) {
        return "即将过期";
      } else if (quotation.remainingHours < 24) {
        return `剩余 ${quotation.remainingHours} 小时`;
      } else {
        const days = Math.floor(quotation.remainingHours / 24);
        return `剩余 ${days} 天`;
      }
    }
    function getActionButtons(quotation) {
      const buttons = [];
      if (quotation.status === "Draft") {
        buttons.push(
          { text: "编辑", type: "primary", action: () => editQuotation(quotation) },
          { text: "发布", type: "success", action: () => publishQuotationItem(quotation) },
          { text: "删除", type: "danger", action: () => deleteQuotationItem(quotation) }
        );
      } else if (quotation.status === "Active") {
        buttons.push(
          { text: "编辑", type: "primary", action: () => editQuotation(quotation) },
          { text: "查看", type: "info", action: () => viewDetail(quotation) },
          { text: "设为草稿", type: "warning", action: () => toggleQuotationStatusItem(quotation) }
        );
      } else {
        buttons.push(
          { text: "查看", type: "info", action: () => viewDetail(quotation) }
        );
      }
      return buttons;
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(filterOptions, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: activeFilter.value === option.value ? 1 : "",
            d: common_vendor.o(($event) => onFilterChange(option.value), option.value)
          };
        }),
        b: common_vendor.o(createQuotation),
        c: common_vendor.p({
          type: "icon",
          icon: "add",
          ["custom-class"]: "add-btn",
          ["custom-style"]: "color: #5DADE2; border-color: #5DADE2;"
        }),
        d: common_vendor.o(viewPublicList),
        e: common_vendor.p({
          type: "icon",
          icon: "view",
          ["custom-class"]: "view-btn",
          ["custom-style"]: "color: #5DADE2; border-color: #5DADE2;"
        }),
        f: quotationList.value.length > 0
      }, quotationList.value.length > 0 ? {
        g: common_vendor.f(filteredList.value, (quotation, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(statusConfig[quotation.status].label),
            b: statusConfig[quotation.status].color,
            c: statusConfig[quotation.status].bgColor,
            d: common_vendor.t(quotation.title),
            e: common_vendor.t(formatPrice(quotation)),
            f: common_vendor.t(quotation.commodityName || "-"),
            g: common_vendor.t(quotation.deliveryLocation),
            h: quotation.brand
          }, quotation.brand ? {
            i: common_vendor.t(quotation.brand)
          } : {}, {
            j: common_vendor.t(quotation.createdAt.split("T")[0]),
            k: quotation.status === "Active"
          }, quotation.status === "Active" ? {
            l: common_vendor.t(formatRemainingTime(quotation)),
            m: quotation.isExpired ? 1 : ""
          } : {}, {
            n: common_vendor.f(getActionButtons(quotation), (button, k1, i1) => {
              return {
                a: common_vendor.t(button.text),
                b: button.text,
                c: common_vendor.o(button.action, button.text),
                d: "f81ce579-3-" + i0 + "-" + i1 + ",f81ce579-0",
                e: common_vendor.p({
                  type: button.type,
                  size: "small"
                })
              };
            }),
            o: common_vendor.o(() => {
            }, quotation.id),
            p: quotation.id,
            q: common_vendor.o(($event) => viewDetail(quotation), quotation.id)
          });
        })
      } : !isLoading.value ? {
        i: common_vendor.o(createQuotation),
        j: common_vendor.p({
          type: "primary"
        })
      } : {}, {
        h: !isLoading.value,
        k: isLoading.value && quotationList.value.length > 0
      }, isLoading.value && quotationList.value.length > 0 ? {
        l: common_vendor.p({
          size: "24rpx"
        })
      } : {}, {
        m: !hasMore.value && quotationList.value.length > 0
      }, !hasMore.value && quotationList.value.length > 0 ? {} : {}, {
        n: isRefreshing.value,
        o: common_vendor.o(onRefresh),
        p: common_vendor.o(onRefreshRestore),
        q: common_vendor.o(loadMore)
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f81ce579"]]);
wx.createPage(MiniProgramPage);
