/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-selector-wrapper.data-v-d36d9223 {
  position: relative;
}
.user-selector-wrapper .input-suffix.data-v-d36d9223 {
  display: flex;
  align-items: center;
}
.user-selector-wrapper .input-suffix .loading-icon.data-v-d36d9223 {
  animation: rotate-d36d9223 1s linear infinite;
  color: #667eea;
}
.user-selector-wrapper .input-suffix .arrow-icon.data-v-d36d9223 {
  color: #909399;
  transition: all 0.3s ease;
}
@keyframes rotate-d36d9223 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.user-picker.data-v-d36d9223 {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.user-picker .picker-header.data-v-d36d9223 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx 20rpx;
}
.user-picker .picker-header .header-content.data-v-d36d9223 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.user-picker .picker-header .header-content .title-section.data-v-d36d9223 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.user-picker .picker-header .header-content .title-section .picker-title.data-v-d36d9223 {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.user-picker .picker-content.data-v-d36d9223 {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  background-color: #f9fafc;
}
.user-picker .picker-content .user-list.data-v-d36d9223 {
  margin-top: 20rpx;
}
.user-picker .picker-content .user-list .user-item.data-v-d36d9223 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  background: white;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}
.user-picker .picker-content .user-list .user-item.data-v-d36d9223::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, #667eea, #764ba2);
}
.user-picker .picker-content .user-list .user-item.data-v-d36d9223:hover {
  background-color: #f9fafc;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
}
.user-picker .picker-content .user-list .user-item .item-info.data-v-d36d9223 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.user-picker .picker-content .user-list .user-item .item-info .item-name.data-v-d36d9223 {
  font-size: 32rpx;
  color: #303133;
  font-weight: 500;
}
.user-picker .picker-content .user-list .user-item .item-info .item-phone.data-v-d36d9223 {
  font-size: 28rpx;
  color: #606266;
}
.user-picker .picker-content .user-list .user-item .item-arrow.data-v-d36d9223 {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 32rpx;
  font-weight: bold;
}
.user-picker .picker-content .empty-list.data-v-d36d9223,
.user-picker .picker-content .loading-indicator.data-v-d36d9223 {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #909399;
  font-size: 28rpx;
}
.data-v-d36d9223  .dj-search {
  background: white;
  border-radius: 40rpx;
  padding: 0 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.data-v-d36d9223  .dj-search:focus-within {
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
}
.data-v-d36d9223  .dj-radio .wd-radio__label {
  color: #303133;
  font-size: 28rpx;
  font-weight: 500;
  padding-left: 12rpx;
}
.data-v-d36d9223  .dj-radio .wd-radio__shape {
  border-color: #c0c4cc;
  transition: all 0.3s ease;
}
.data-v-d36d9223  .dj-radio .wd-radio__shape.is-checked {
  background-color: #667eea;
  border-color: #667eea;
  box-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
}
.data-v-d36d9223  .dj-popup {
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}