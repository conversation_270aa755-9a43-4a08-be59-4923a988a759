<view class="trade-request-list data-v-a812eeaf"><view class="list-content data-v-a812eeaf"><view wx:if="{{a}}" class="loading-state data-v-a812eeaf"><view class="flex justify-center items-center py-8 data-v-a812eeaf"><view class="loading-spinner w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-3 data-v-a812eeaf"></view><text class="text-sm text-gray-500 data-v-a812eeaf">加载中...</text></view></view><view wx:elif="{{b}}" class="empty-state data-v-a812eeaf"><view class="text-center py-12 bg-white rounded-lg data-v-a812eeaf"><view class="empty-icon w-16 h-16 mx-auto mb-4 opacity-20 data-v-a812eeaf"><svg wx:if="{{d}}" u-s="{{['d']}}" class="w-full h-full text-gray-400 data-v-a812eeaf" u-i="a812eeaf-0" bind:__l="__l" u-p="{{d}}"><path wx:if="{{c}}" class="data-v-a812eeaf" u-i="a812eeaf-1,a812eeaf-0" bind:__l="__l" u-p="{{c}}"/></svg></view><text class="text-sm text-gray-400 data-v-a812eeaf">{{e}}</text></view></view><view wx:else class="request-list data-v-a812eeaf"><trade-request-item wx:for="{{f}}" wx:for-item="request" wx:key="a" class="data-v-a812eeaf" bindfill="{{request.b}}" bindreject="{{request.c}}" bindconvertToSimulation="{{request.d}}" bindconvertToTrade="{{request.e}}" bindcancel="{{request.f}}" u-i="{{request.g}}" bind:__l="__l" u-p="{{request.h}}"/></view></view></view>