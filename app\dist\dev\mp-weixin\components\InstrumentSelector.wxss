/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.instrument-selector-wrapper.data-v-c01cf354 {
  position: relative;
}
.instrument-selector-wrapper .input-suffix.data-v-c01cf354 {
  display: flex;
  align-items: center;
}
.instrument-selector-wrapper .input-suffix .loading-icon.data-v-c01cf354 {
  animation: rotate-c01cf354 1s linear infinite;
  color: #667eea;
}
.instrument-selector-wrapper .input-suffix .arrow-icon.data-v-c01cf354 {
  color: #909399;
  transition: all 0.3s ease;
}
@keyframes rotate-c01cf354 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.instrument-picker.data-v-c01cf354 {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.instrument-picker .picker-header.data-v-c01cf354 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx 20rpx;
}
.instrument-picker .picker-header .header-content.data-v-c01cf354 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.instrument-picker .picker-header .header-content .title-section.data-v-c01cf354 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.instrument-picker .picker-header .header-content .title-section .picker-title.data-v-c01cf354 {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.instrument-picker .picker-header .header-content .title-section .breadcrumb.data-v-c01cf354 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.instrument-picker .picker-content.data-v-c01cf354 {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  background-color: #f9fafc;
}
.instrument-picker .picker-content .instrument-list.data-v-c01cf354 {
  margin-top: 20rpx;
}
.instrument-picker .picker-content .instrument-list .instrument-item.data-v-c01cf354 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  background: white;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}
.instrument-picker .picker-content .instrument-list .instrument-item.data-v-c01cf354::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, #667eea, #764ba2);
}
.instrument-picker .picker-content .instrument-list .instrument-item.data-v-c01cf354:hover {
  background-color: #f9fafc;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
}
.instrument-picker .picker-content .instrument-list .instrument-item .item-info.data-v-c01cf354 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.instrument-picker .picker-content .instrument-list .instrument-item .item-info .item-name.data-v-c01cf354 {
  font-size: 32rpx;
  color: #303133;
  font-weight: 500;
}
.instrument-picker .picker-content .instrument-list .instrument-item .item-info .item-product.data-v-c01cf354 {
  font-size: 28rpx;
  color: #606266;
}
.instrument-picker .picker-content .instrument-list .instrument-item .item-arrow.data-v-c01cf354 {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 32rpx;
  font-weight: bold;
}
.instrument-picker .picker-content .empty-list.data-v-c01cf354,
.instrument-picker .picker-content .loading-indicator.data-v-c01cf354 {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #909399;
  font-size: 28rpx;
}
.data-v-c01cf354  .back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.data-v-c01cf354  .dj-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}
.data-v-c01cf354  .dj-btn-primary:hover {
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
  transform: translateY(-1rpx);
}
.data-v-c01cf354  .dj-btn-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
  color: white;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}
.data-v-c01cf354  .dj-btn-danger:hover {
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
  transform: translateY(-1rpx);
}
.data-v-c01cf354  .dj-search {
  background: white;
  border-radius: 40rpx;
  padding: 0 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.data-v-c01cf354  .dj-search:focus-within {
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
}
.data-v-c01cf354  .dj-radio .wd-radio__label {
  color: #303133;
  font-size: 28rpx;
  font-weight: 500;
  padding-left: 12rpx;
}
.data-v-c01cf354  .dj-radio .wd-radio__shape {
  border-color: #c0c4cc;
  transition: all 0.3s ease;
}
.data-v-c01cf354  .dj-radio .wd-radio__shape.is-checked {
  background-color: #667eea;
  border-color: #667eea;
  box-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
}
.data-v-c01cf354  .dj-popup {
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}