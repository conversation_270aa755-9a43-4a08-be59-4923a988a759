<layout-default-uni class="data-v-fe014edc" u-s="{{['d']}}" u-i="fe014edc-0" bind:__l="__l"><view class="page-container gradient-bg-primary data-v-fe014edc"><view class="filter-tabs data-v-fe014edc"><wd-tabs wx:if="{{d}}" class="data-v-fe014edc" u-s="{{['d']}}" bindchange="{{b}}" u-i="fe014edc-1,fe014edc-0" bind:__l="__l" bindupdateModelValue="{{c}}" u-p="{{d}}"><wd-tab wx:for="{{a}}" wx:for-item="tab" wx:key="a" class="data-v-fe014edc" u-i="{{tab.b}}" bind:__l="__l" u-p="{{tab.c}}"></wd-tab></wd-tabs></view><trade-request-list wx:if="{{j}}" class="data-v-fe014edc" bindfill="{{e}}" bindreject="{{f}}" bindconvertToSimulation="{{g}}" bindconvertToTrade="{{h}}" bindrefresh="{{i}}" u-i="fe014edc-3,fe014edc-0" bind:__l="__l" u-p="{{j}}"/><view wx:if="{{k}}" class="load-more-tip text-center py-4 data-v-fe014edc"><view wx:if="{{l}}" class="flex justify-center items-center data-v-fe014edc"><view class="loading-spinner w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2 data-v-fe014edc"></view><text class="text-sm text-gray-500 data-v-fe014edc">{{m}}</text></view><text wx:else class="text-xs text-gray-400 data-v-fe014edc">{{n}}</text></view><wd-popup wx:if="{{K}}" class="data-v-fe014edc" u-s="{{['d']}}" u-i="fe014edc-4,fe014edc-0" bind:__l="__l" bindupdateModelValue="{{J}}" u-p="{{K}}"><view class="feedback-dialog data-v-fe014edc"><view class="dialog-header data-v-fe014edc"><text class="dialog-title data-v-fe014edc">{{o}}</text></view><view class="dialog-content data-v-fe014edc"><view wx:if="{{p}}" class="request-summary data-v-fe014edc"><text class="summary-text data-v-fe014edc">{{q}} {{r}} 手 <text wx:if="{{s}}" class="data-v-fe014edc">@ {{t}}</text></text></view><view wx:if="{{v}}" class="fill-form data-v-fe014edc"><wd-input wx:if="{{x}}" class="data-v-fe014edc" u-i="fe014edc-5,fe014edc-4" bind:__l="__l" bindupdateModelValue="{{w}}" u-p="{{x}}"/><wd-input wx:if="{{z}}" class="mt-3 data-v-fe014edc" u-i="fe014edc-6,fe014edc-4" bind:__l="__l" bindupdateModelValue="{{y}}" u-p="{{z}}"/><wd-textarea wx:if="{{B}}" class="mt-3 data-v-fe014edc" u-i="fe014edc-7,fe014edc-4" bind:__l="__l" bindupdateModelValue="{{A}}" u-p="{{B}}"/></view><view wx:else class="reject-form data-v-fe014edc"><wd-textarea wx:if="{{D}}" class="data-v-fe014edc" u-i="fe014edc-8,fe014edc-4" bind:__l="__l" bindupdateModelValue="{{C}}" u-p="{{D}}"/></view></view><view class="dialog-actions data-v-fe014edc"><wd-button wx:if="{{F}}" class="data-v-fe014edc" u-s="{{['d']}}" bindclick="{{E}}" u-i="fe014edc-9,fe014edc-4" bind:__l="__l" u-p="{{F}}"> 取消 </wd-button><wd-button wx:if="{{I}}" class="data-v-fe014edc" u-s="{{['d']}}" bindclick="{{H}}" u-i="fe014edc-10,fe014edc-4" bind:__l="__l" u-p="{{I}}"> 确认{{G}}</wd-button></view></view></wd-popup></view></layout-default-uni>