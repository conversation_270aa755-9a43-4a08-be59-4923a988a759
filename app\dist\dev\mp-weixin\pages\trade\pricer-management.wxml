<layout-default-uni class="data-v-732d0900" u-s="{{['d']}}" u-i="732d0900-0" bind:__l="__l"><view class="page-container gradient-bg-primary data-v-732d0900"><view wx:if="{{a}}" class="refresh-indicator common-card bg-blue-500 text-white text-center data-v-732d0900"> 正在刷新数据... </view><view class="filter-tabs data-v-732d0900"><wd-tabs wx:if="{{e}}" class="data-v-732d0900" u-s="{{['d']}}" bindchange="{{c}}" u-i="732d0900-1,732d0900-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"><wd-tab wx:for="{{b}}" wx:for-item="tab" wx:key="a" class="data-v-732d0900" u-i="{{tab.b}}" bind:__l="__l" u-p="{{tab.c}}"></wd-tab></wd-tabs></view><view class="data-v-732d0900"><trade-request-list wx:if="{{h}}" class="data-v-732d0900" bindcancel="{{f}}" bindrefresh="{{g}}" u-i="732d0900-3,732d0900-0" bind:__l="__l" u-p="{{h}}"/></view><view wx:if="{{i}}" class="load-more-tip text-center py-4 data-v-732d0900"><view wx:if="{{j}}" class="flex justify-center items-center data-v-732d0900"><view class="loading-spinner w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2 data-v-732d0900"></view><text class="text-sm text-gray-500 data-v-732d0900">{{k}}</text></view><text wx:else class="text-xs text-gray-400 data-v-732d0900">{{l}}</text></view></view></layout-default-uni>